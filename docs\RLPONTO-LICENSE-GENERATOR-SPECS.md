# 🔐 RLPONTO LICENSE GENERATOR - ESPECIFICAÇÕES TÉCNICAS

## 📋 VISÃO GERAL DO SISTEMA

### 🎯 **OBJETIVO:**

Sistema separado e independente para geração, validação e revogação de licenças para o sistema **RLPONTO** (Sistema de Controle de Ponto Eletrônico para Construtoras).

### 🏗️ **ARQUITETURA:**

- **Sistema Principal**: RLPONTO (Sistema de Ponto)
- **Sistema Auxiliar**: RLPONTO License Generator (Este sistema)
- **Comunicação**: API REST com autenticação JWT
- **Segurança**: Criptografia AES-256 + RSA + HMAC

---

## 🎯 SOBRE O SISTEMA RLPONTO (SISTEMA PRINCIPAL)

### **📊 CARACTERÍSTICAS DO RLPONTO:**

```typescript
interface SistemaRLPONTO {
  nome: 'RLPONTO - Sistema de Controle de Ponto Eletrônico'
  versao: '1.0.0'
  tecnologia: 'Next.js 15 + React 19 + TypeScript + PostgreSQL + Prisma'

  modelo_negocio: {
    tipo: 'B2B - Construtoras'
    estrutura: 'Multi-empresa com alocação de funcionários'
    usuarios_finais: 'Funcionários da construção civil'
    administradores: 'RH das construtoras'
  }

  funcionalidades_principais: [
    'Registro de ponto biométrico e manual',
    'Gestão de funcionários e EPIs',
    'Relatórios de ponto e produtividade',
    'Controle de jornadas e turnos',
    'Integração com dispositivos biométricos',
    'Sistema de alertas e notificações',
    'Backup e sincronização offline',
    'APIs para integrações externas',
  ]

  niveis_acesso: [
    'admin_total', // Acesso completo ao sistema
    'admin_limitado', // Administração sem configurações críticas
    'gerente_rh', // Gestão de funcionários e relatórios
    'supervisor_obra', // Supervisão de obra específica
    'cliente_vinculado', // Acesso apenas aos funcionários alocados
    'operador_biometria', // Operação de dispositivos biométricos
    'readonly', // Apenas visualização
  ]

  limites_licenciamento: {
    funcionarios: 'Número máximo de funcionários cadastrados'
    empresas_clientes: 'Número de empresas que podem ser gerenciadas'
    dispositivos_biometricos: 'Quantidade de leitores biométricos'
    usuarios_sistema: 'Usuários com acesso ao sistema'
    armazenamento_gb: 'Espaço para fotos, documentos e backups'
  }
}
```

### **🔧 TIPOS DE LICENÇA RLPONTO:**

```typescript
interface TiposLicencaRLPONTO {
  // ✅ TIPOS POR DURAÇÃO
  duracao: {
    mensal: {
      codigo: 'MENS'
      duracao_dias: 30
      renovacao: 'Automática via pagamento'
    }
    anual: {
      codigo: 'ANUL'
      duracao_dias: 365
      desconto: '20% sobre mensal'
    }
    vitalicia: {
      codigo: 'VITA'
      duracao_dias: null // Sem expiração
      preco: '16x valor mensal'
    }
  }

  // ✅ NÍVEIS DE PLANO
  niveis: {
    basico: {
      codigo: 'BASC'
      funcionarios: 100
      empresas_clientes: 5
      dispositivos_biometricos: 3
      usuarios_sistema: 10
      armazenamento_gb: 10
      recursos: ['ponto_basico', 'relatorios_simples']
    }

    profissional: {
      codigo: 'PROF'
      funcionarios: 500
      empresas_clientes: 20
      dispositivos_biometricos: 10
      usuarios_sistema: 50
      armazenamento_gb: 50
      recursos: ['ponto_avancado', 'relatorios_completos', 'apis', 'webhooks']
    }

    enterprise: {
      codigo: 'ENTP'
      funcionarios: -1 // Ilimitado
      empresas_clientes: -1
      dispositivos_biometricos: -1
      usuarios_sistema: -1
      armazenamento_gb: 500
      recursos: ['todos_recursos', 'suporte_prioritario', 'customizacoes']
    }
  }
}
```

---

## 🔐 ESPECIFICAÇÕES DE CRIPTOGRAFIA

### **🔑 FORMATO DA CHAVE DE LICENÇA:**

```
RLPT-MENS-PROF-A1B2-C3D4-E5F6
│    │    │    │    │    │
│    │    │    │    │    └─ Checksum HMAC (4 chars)
│    │    │    │    └────── ID Único (4 chars)
│    │    │    └─────────── ID Único (4 chars)
│    │    └──────────────── Nível: BASC/PROF/ENTP
│    └───────────────────── Tipo: MENS/ANUL/VITA
└────────────────────────── Prefixo: RLPT
```

### **🛡️ CRIPTOGRAFIA MULTICAMADA:**

```typescript
interface CriptografiaLicenca {
  // ✅ CAMADA 1: DADOS DA LICENÇA
  dados_licenca: {
    algoritmo: 'AES-256-GCM'
    chave_derivacao: 'PBKDF2 com 100.000 iterações'
    salt: '32 bytes aleatórios por licença'
    iv: '12 bytes aleatórios por operação'

    dados_criptografados: {
      tipo_licenca: 'mensal|anual|vitalicia'
      nivel_plano: 'basico|profissional|enterprise'
      data_inicio: 'ISO 8601 timestamp'
      data_expiracao: 'ISO 8601 timestamp ou null'
      limites: 'Objeto com todos os limites'
      recursos: 'Array de recursos inclusos'
      cliente_id: 'UUID do cliente'
      ambiente: 'producao|homologacao|desenvolvimento'
      versao_sistema: '1.0.0'
    }
  }

  // ✅ CAMADA 2: ASSINATURA DIGITAL
  assinatura: {
    algoritmo: 'RSA-PSS com SHA-256'
    tamanho_chave: '4096 bits'
    padding: 'OAEP com MGF1'

    dados_assinados: 'dados_criptografados + metadados_publicos'
    chave_privada: 'Armazenada em HSM ou cofre seguro'
    chave_publica: 'Embarcada no sistema RLPONTO'
  }

  // ✅ CAMADA 3: INTEGRIDADE
  integridade: {
    algoritmo: 'HMAC-SHA-256'
    chave_hmac: 'Derivada da chave mestra + salt único'
    dados_verificados: 'arquivo_completo + timestamp'
  }
}
```

### **📁 ESTRUTURA DO ARQUIVO .rllic:**

```json
{
  "header": {
    "versao_formato": "1.0",
    "produto": "RLPONTO",
    "gerado_em": "2024-01-15T10:30:00.000Z",
    "gerado_por": "license-generator-v1.0",
    "algoritmo_criptografia": "AES-256-GCM+RSA-PSS+HMAC-SHA-256"
  },

  "licenca": {
    "chave_licenca": "RLPT-MENS-PROF-A1B2-C3D4-E5F6",
    "dados_criptografados": "base64_encrypted_payload",
    "salt": "base64_32_bytes_salt",
    "iv": "base64_12_bytes_iv",
    "assinatura_digital": "base64_rsa_signature"
  },

  "cliente": {
    "cliente_id": "uuid-v4",
    "nome_empresa": "Construtora ABC Ltda",
    "cnpj": "12.345.678/0001-90",
    "email_contato": "<EMAIL>"
  },

  "integridade": {
    "checksum_arquivo": "sha256_hash_arquivo_completo",
    "hmac_verificacao": "hmac_sha256_dados_criticos"
  }
}
```

---

## 🌐 ESPECIFICAÇÕES DA API

### **🔗 ENDPOINTS OBRIGATÓRIOS:**

#### **1. VALIDAÇÃO DE LICENÇA (RLPONTO → License Generator):**

```http
POST /api/v1/license/validate
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "chave_licenca": "RLPT-MENS-PROF-A1B2-C3D4-E5F6",
  "sistema_info": {
    "versao": "1.0.0",
    "ip_servidor": "*************",
    "dominio": "rlponto.construtoraabc.com.br"
  },
  "utilizacao_atual": {
    "funcionarios": 45,
    "empresas_clientes": 3,
    "dispositivos_biometricos": 2,
    "usuarios_sistema": 8,
    "armazenamento_gb": 5.2
  }
}
```

**Resposta:**

```json
{
  "status": "valida|invalida|expirada|revogada",
  "licenca_detalhes": {
    "tipo": "mensal",
    "nivel": "profissional",
    "data_expiracao": "2024-02-15T10:30:00.000Z",
    "dias_restantes": 25,
    "limites": {
      "funcionarios": 500,
      "empresas_clientes": 20,
      "dispositivos_biometricos": 10,
      "usuarios_sistema": 50,
      "armazenamento_gb": 50
    }
  },
  "alertas": [
    {
      "tipo": "limite_proximo",
      "recurso": "funcionarios",
      "percentual_usado": 85,
      "acao_sugerida": "Considere upgrade do plano"
    }
  ]
}
```

#### **2. REVOGAÇÃO DE LICENÇA (License Generator → RLPONTO):**

```http
POST /api/v1/license/revoke
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "chave_licenca": "RLPT-MENS-PROF-A1B2-C3D4-E5F6",
  "motivo_revogacao": "Cancelamento por inadimplência",
  "revogacao_imediata": true,
  "notificar_cliente": true
}
```

**Resposta:**

```json
{
  "status": "revogada",
  "timestamp_revogacao": "2024-01-15T15:45:00.000Z",
  "licenca_backup_criado": true,
  "cliente_notificado": true
}
```

#### **3. CONSULTA DE STATUS (License Generator → RLPONTO):**

```http
GET /api/v1/license/status/{cliente_id}
Authorization: Bearer {jwt_token}
```

**Resposta:**

```json
{
  "cliente_info": {
    "cliente_id": "uuid-v4",
    "nome_empresa": "Construtora ABC Ltda",
    "cnpj": "12.345.678/0001-90",
    "licenca_ativa": true
  },

  "licenca_atual": {
    "chave_licenca": "RLPT-****-****-****-****-E5F6",
    "tipo": "mensal",
    "nivel": "profissional",
    "data_inicio": "2024-01-15T00:00:00.000Z",
    "data_expiracao": "2024-02-15T23:59:59.000Z",
    "status": "ativa"
  },

  "utilizacao_atual": {
    "funcionarios": {
      "usado": 45,
      "limite": 500,
      "percentual": 9
    },
    "empresas_clientes": {
      "usado": 3,
      "limite": 20,
      "percentual": 15
    },
    "dispositivos_biometricos": {
      "usado": 2,
      "limite": 10,
      "percentual": 20
    },
    "usuarios_sistema": {
      "usado": 8,
      "limite": 50,
      "percentual": 16
    },
    "armazenamento_gb": {
      "usado": 5.2,
      "limite": 50,
      "percentual": 10.4
    }
  },

  "historico_validacoes": {
    "total_validacoes": 1247,
    "ultima_validacao": "2024-01-15T14:30:00.000Z",
    "validacoes_sucesso": 1245,
    "tentativas_violacao": 2,
    "sistema_online": true
  },

  "alertas_ativos": [
    {
      "tipo": "expiracao_proxima",
      "severidade": "media",
      "dias_restantes": 25,
      "acao_recomendada": "Renovar licença"
    }
  ]
}
```

#### **4. WEBHOOK DE NOTIFICAÇÕES (License Generator → RLPONTO):**

```http
POST /api/v1/webhooks/license-notification
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "evento": "licenca_revogada|licenca_expirada|limite_atingido",
  "cliente_id": "uuid-v4",
  "chave_licenca": "RLPT-MENS-PROF-A1B2-C3D4-E5F6",
  "timestamp": "2024-01-15T15:45:00.000Z",
  "detalhes": {
    "motivo": "Cancelamento por inadimplência",
    "acao_requerida": "Bloquear sistema em 24 horas"
  }
}
```

---

## 🏗️ ARQUITETURA DO LICENSE GENERATOR

### **📊 FUNCIONALIDADES OBRIGATÓRIAS:**

#### **1. GERAÇÃO DE LICENÇAS:**

```typescript
interface GeradorLicencas {
  interface_geracao: {
    formulario_cliente: {
      nome_empresa: 'string (obrigatório)'
      cnpj: 'string com validação'
      email_contato: 'string com validação'
      responsavel_tecnico: 'string'
      observacoes: 'text opcional'
    }

    configuracao_licenca: {
      tipo_licenca: 'mensal|anual|vitalicia'
      nivel_plano: 'basico|profissional|enterprise'
      data_inicio: 'date (padrão: hoje)'
      ambiente_destino: 'producao|homologacao|desenvolvimento'
      recursos_customizados: 'array opcional'
    }

    restricoes_especiais: {
      ip_binding: 'array de IPs permitidos'
      domain_binding: 'array de domínios permitidos'
      limite_instalacoes: 'número máximo de instalações'
    }
  }

  processo_geracao: {
    validacao_dados: 'Validar todos os campos obrigatórios'
    geracao_chave: 'Criar chave única no formato RLPT-XXXX-YYYY-ZZZZ-WWWW-VVVV'
    criptografia_dados: 'Criptografar metadados com AES-256-GCM'
    assinatura_digital: 'Assinar com RSA-PSS'
    criacao_arquivo: 'Gerar arquivo .rllic'
    registro_banco: 'Salvar no banco de dados'
    envio_cliente: 'Email automático ou download manual'
  }
}
```

#### **2. GESTÃO DE CLIENTES:**

```typescript
interface GestaoClientes {
  cadastro_cliente: {
    dados_basicos: 'Nome, CNPJ, contatos'
    configuracoes_conta: 'Preferências, notificações'
    historico_licencas: 'Todas as licenças já geradas'
    status_pagamento: 'Situação financeira'
  }

  dashboard_cliente: {
    licenca_ativa: 'Status atual da licença'
    utilizacao_recursos: 'Gráficos de uso vs limites'
    historico_validacoes: 'Log de validações do sistema'
    alertas_importantes: 'Notificações críticas'
    opcoes_renovacao: 'Links para renovar/upgrade'
  }
}
```

#### **3. MONITORAMENTO E CONTROLE:**

```typescript
interface MonitoramentoLicencas {
  dashboard_admin: {
    estatisticas_gerais: {
      total_licencas_ativas: number
      total_clientes: number
      receita_mensal_recorrente: number
      licencas_expirando_30_dias: number
    }

    alertas_sistema: [
      {
        tipo: 'licenca_expirada|tentativa_violacao|limite_atingido'
        cliente: 'Nome da empresa'
        severidade: 'baixa|media|alta|critica'
        acao_requerida: 'string'
      },
    ]

    metricas_utilizacao: {
      recursos_mais_utilizados: 'Gráfico de barras'
      clientes_proximos_limite: 'Lista ordenada'
      tendencias_crescimento: 'Gráfico temporal'
    }
  }

  ferramentas_admin: {
    busca_licencas: 'Buscar por chave, CNPJ, empresa'
    revogacao_emergencia: 'Revogar licença imediatamente'
    extensao_temporaria: 'Estender licença por X dias'
    migracao_planos: 'Alterar nível de plano'
    backup_licencas: 'Exportar dados para backup'
  }
}
```

---

## 🔒 SEGURANÇA E AUDITORIA

### **🛡️ REQUISITOS DE SEGURANÇA:**

#### **1. AUTENTICAÇÃO E AUTORIZAÇÃO:**

```typescript
interface SegurancaSistema {
  autenticacao: {
    metodo: 'JWT + 2FA obrigatório'
    expiracao_token: '4 horas'
    refresh_token: '30 dias'
    tentativas_login: 'Máximo 5, bloqueio 15 minutos'
  }

  autorizacao: {
    roles: [
      'super_admin', // Acesso total
      'admin', // Gestão de licenças
      'comercial', // Geração de licenças
      'suporte', // Consulta e suporte
      'readonly', // Apenas visualização
    ]

    permissoes_granulares: {
      gerar_licencas: ['super_admin', 'admin', 'comercial']
      revogar_licencas: ['super_admin', 'admin']
      consultar_dados: ['super_admin', 'admin', 'comercial', 'suporte']
      configurar_sistema: ['super_admin']
    }
  }

  criptografia_dados: {
    dados_em_repouso: 'AES-256 para dados sensíveis'
    dados_em_transito: 'TLS 1.3 obrigatório'
    chaves_criptograficas: 'Rotação automática a cada 90 dias'
    backup_chaves: 'HSM ou cofre seguro'
  }
}
```

#### **2. AUDITORIA COMPLETA:**

```typescript
interface AuditoriaSistema {
  logs_obrigatorios: [
    'Geração de licenças',
    'Revogação de licenças',
    'Validações de licenças',
    'Tentativas de violação',
    'Logins e logouts',
    'Alterações de configuração',
    'Acessos a dados sensíveis',
  ]

  estrutura_log: {
    timestamp: 'ISO 8601 com timezone'
    usuario: 'ID e nome do usuário'
    acao: 'Descrição da ação realizada'
    recurso_afetado: 'Licença, cliente, configuração'
    ip_origem: 'IP de onde partiu a ação'
    user_agent: 'Navegador/aplicação'
    resultado: 'sucesso|erro|bloqueado'
    detalhes_adicionais: 'JSON com dados específicos'
  }

  retencao_logs: {
    logs_sistema: '2 anos'
    logs_auditoria: '7 anos'
    logs_financeiros: '10 anos'
    backup_logs: 'Automático diário'
  }
}
```

---

## 📋 BANCO DE DADOS

### **🗄️ ESTRUTURA PRINCIPAL:**

```sql
-- ✅ TABELA DE CLIENTES
CREATE TABLE clientes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome_empresa VARCHAR(255) NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  email_contato VARCHAR(255) NOT NULL,
  responsavel_tecnico VARCHAR(255),
  telefone VARCHAR(20),
  endereco JSONB,

  -- STATUS
  ativo BOOLEAN DEFAULT TRUE,
  data_cadastro TIMESTAMP DEFAULT NOW(),
  ultima_atualizacao TIMESTAMP DEFAULT NOW(),

  -- CONFIGURAÇÕES
  preferencias_notificacao JSONB,
  configuracoes_conta JSONB
);

-- ✅ TABELA DE LICENÇAS GERADAS
CREATE TABLE licencas_geradas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cliente_id UUID NOT NULL REFERENCES clientes(id),

  -- DADOS DA LICENÇA
  chave_licenca VARCHAR(29) UNIQUE NOT NULL,
  chave_hash VARCHAR(255) NOT NULL,
  tipo_licenca VARCHAR(20) NOT NULL,
  nivel_plano VARCHAR(20) NOT NULL,
  ambiente VARCHAR(20) NOT NULL,

  -- LIMITES E RECURSOS
  limites JSONB NOT NULL,
  recursos_inclusos JSONB NOT NULL,
  restricoes_especiais JSONB,

  -- DATAS
  data_inicio DATE NOT NULL,
  data_expiracao DATE,

  -- STATUS
  status_licenca VARCHAR(20) DEFAULT 'ativa',
  motivo_inativacao TEXT,

  -- AUDITORIA
  gerada_por UUID NOT NULL,
  gerada_em TIMESTAMP DEFAULT NOW(),
  revogada_em TIMESTAMP,
  revogada_por UUID,

  -- ARQUIVO
  arquivo_gerado BOOLEAN DEFAULT FALSE,
  caminho_arquivo VARCHAR(500),

  CONSTRAINT chk_tipo_licenca CHECK (tipo_licenca IN ('mensal', 'anual', 'vitalicia')),
  CONSTRAINT chk_nivel_plano CHECK (nivel_plano IN ('basico', 'profissional', 'enterprise')),
  CONSTRAINT chk_status_licenca CHECK (status_licenca IN ('ativa', 'revogada', 'expirada', 'suspensa'))
);

-- ✅ TABELA DE VALIDAÇÕES
CREATE TABLE validacoes_licenca (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  licenca_id UUID NOT NULL REFERENCES licencas_geradas(id),

  -- DADOS DA VALIDAÇÃO
  ip_origem INET NOT NULL,
  user_agent TEXT,
  versao_sistema VARCHAR(20),

  -- RESULTADO
  resultado VARCHAR(20) NOT NULL,
  detalhes_validacao JSONB,

  -- UTILIZAÇÃO NO MOMENTO DA VALIDAÇÃO
  utilizacao_snapshot JSONB,

  -- TIMESTAMP
  validado_em TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_resultado_validacao CHECK (resultado IN ('sucesso', 'erro', 'violacao', 'expirada', 'revogada'))
);

-- ✅ TABELA DE AUDITORIA
CREATE TABLE logs_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- IDENTIFICAÇÃO
  usuario_id UUID,
  usuario_nome VARCHAR(255),
  ip_origem INET NOT NULL,
  user_agent TEXT,

  -- AÇÃO
  acao VARCHAR(100) NOT NULL,
  recurso_tipo VARCHAR(50) NOT NULL,
  recurso_id UUID,

  -- RESULTADO
  resultado VARCHAR(20) NOT NULL,
  detalhes JSONB,

  -- TIMESTAMP
  executado_em TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_resultado_auditoria CHECK (resultado IN ('sucesso', 'erro', 'bloqueado', 'negado'))
);

-- ✅ ÍNDICES PARA PERFORMANCE
CREATE INDEX idx_clientes_cnpj ON clientes(cnpj);
CREATE INDEX idx_clientes_ativo ON clientes(ativo);
CREATE INDEX idx_licencas_cliente ON licencas_geradas(cliente_id);
CREATE INDEX idx_licencas_chave_hash ON licencas_geradas(chave_hash);
CREATE INDEX idx_licencas_status ON licencas_geradas(status_licenca);
CREATE INDEX idx_licencas_expiracao ON licencas_geradas(data_expiracao);
CREATE INDEX idx_validacoes_licenca ON validacoes_licenca(licenca_id);
CREATE INDEX idx_validacoes_timestamp ON validacoes_licenca(validado_em);
CREATE INDEX idx_auditoria_usuario ON logs_auditoria(usuario_id);
CREATE INDEX idx_auditoria_timestamp ON logs_auditoria(executado_em);
CREATE INDEX idx_auditoria_acao ON logs_auditoria(acao);
```

---

## ✅ RESUMO EXECUTIVO

### **🎯 OBJETIVO FINAL:**

Criar um **sistema separado e seguro** para geração, validação e revogação de licenças do RLPONTO, com comunicação via API e controle total sobre todas as licenças ativas.

### **🔑 CARACTERÍSTICAS PRINCIPAIS:**

- ✅ **Criptografia Multicamada**: AES-256 + RSA + HMAC
- ✅ **API Completa**: Validação, revogação, consulta, webhooks
- ✅ **Auditoria Total**: Logs de todas as ações
- ✅ **Segurança Enterprise**: 2FA, roles, criptografia
- ✅ **Monitoramento Real-time**: Dashboard com métricas
- ✅ **Revogação Instantânea**: Comando API ou interface
- ✅ **Backup Seguro**: Chaves em HSM/cofre

### **🚀 RESULTADO:**

Sistema **profissional completo** para controle de licenças do RLPONTO com segurança máxima e operação comercial escalável.

```

```
