import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para alocação de funcionário
const alocacaoSchema = z.object({
  funcionarioId: z.string().uuid("ID do funcionário inválido"),
  dataInicio: z.string().datetime("Data de início inválida"),
  dataFimPrevista: z.string().datetime().optional(),
  valorHoraProjeto: z.number().positive().optional(),
  observacoes: z.string().optional()
})

// GET /api/empresas/[id]/funcionarios - Listar funcionários alocados
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar se empresa existe
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    // Buscar alocações ativas
    const alocacoes = await prisma.funcionarioAlocacao.findMany({
      where: {
        empresaId: params.id,
        status: 'ativo'
      },
      include: {
        funcionario: {
          select: {
            id: true,
            nomeCompleto: true,
            cpf: true,
            cargo: true,
            setor: true,
            dataAdmissao: true,
            ativo: true,
            empresa: {
              select: {
                id: true,
                nomeFantasia: true
              }
            }
          }
        }
      },
      orderBy: {
        dataInicio: 'desc'
      }
    })

    // Calcular estatísticas
    const estatisticas = {
      total_alocados: alocacoes.length,
      valor_total_hora: alocacoes.reduce((sum, a) => sum + (Number(a.valorHoraProjeto) || 0), 0),
      alocacoes_recentes: alocacoes.filter(a => {
        const diasAtras = new Date()
        diasAtras.setDate(diasAtras.getDate() - 30)
        return a.dataInicio >= diasAtras
      }).length
    }

    return NextResponse.json({
      alocados: alocacoes,
      estatisticas
    })

  } catch (error) {
    console.error('Erro ao buscar funcionários alocados:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// POST /api/empresas/[id]/funcionarios - Alocar funcionário
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Verificar se empresa existe
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = alocacaoSchema.parse(body)

    // Verificar se funcionário existe e está ativo
    const funcionario = await prisma.funcionario.findUnique({
      where: { id: data.funcionarioId },
      include: {
        alocacoes: {
          where: { status: 'ativo' }
        }
      }
    })

    if (!funcionario) {
      return NextResponse.json(
        { error: "Funcionário não encontrado" },
        { status: 404 }
      )
    }

    if (!funcionario.ativo) {
      return NextResponse.json(
        { error: "Funcionário está inativo" },
        { status: 400 }
      )
    }

    // Verificar se funcionário já tem alocação ativa
    if (funcionario.alocacoes.length > 0) {
      return NextResponse.json(
        { 
          error: "Funcionário já possui alocação ativa",
          alocacao_atual: funcionario.alocacoes[0]
        },
        { status: 400 }
      )
    }

    // Criar alocação
    const alocacao = await prisma.funcionarioAlocacao.create({
      data: {
        funcionarioId: data.funcionarioId,
        empresaId: params.id,
        dataInicio: new Date(data.dataInicio),
        dataFimPrevista: data.dataFimPrevista ? new Date(data.dataFimPrevista) : null,
        valorHoraProjeto: data.valorHoraProjeto,
        observacoes: data.observacoes,
        createdBy: session.user.id
      },
      include: {
        funcionario: {
          select: {
            id: true,
            nomeCompleto: true,
            cargo: true
          }
        },
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "CREATE_ALOCACAO",
        tabelaAfetada: "funcionario_alocacoes",
        registroId: alocacao.id,
        dadosNovos: alocacao,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(alocacao, { status: 201 })

  } catch (error) {
    console.error('Erro ao alocar funcionário:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
