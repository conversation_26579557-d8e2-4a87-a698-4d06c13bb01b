"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { FileText, Upload, X, Eye } from "lucide-react"

interface DocumentosStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function DocumentosStep({ data, onUpdate }: DocumentosStepProps) {
  const [formData, setFormData] = useState({
    pis: data.pis || '',
    ctps: data.ctps || '',
    ctpsSerie: data.ctpsSerie || '',
    tituloEleitor: data.tituloEleitor || '',
    zonaEleitoral: data.zonaEleitoral || '',
    secaoEleitoral: data.secaoEleitoral || '',
    reservista: data.reservista || '',
    cnh: data.cnh || '',
    cnhCategoria: data.cnhCategoria || '',
    cnhVencimento: data.cnhVencimento || '',
    documentos: data.documentos || []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [uploading, setUploading] = useState(false)

  // Validar PIS
  const validarPIS = (pis: string) => {
    const pisLimpo = pis.replace(/\D/g, '')
    if (pisLimpo.length !== 11) return false
    
    const weights = [3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    let sum = 0
    
    for (let i = 0; i < 10; i++) {
      sum += parseInt(pisLimpo[i]) * weights[i]
    }
    
    const remainder = sum % 11
    const digit = remainder < 2 ? 0 : 11 - remainder
    
    return digit === parseInt(pisLimpo[10])
  }

  // Formatar PIS
  const formatarPIS = (value: string) => {
    const pis = value.replace(/\D/g, '')
    return pis.replace(/(\d{3})(\d{5})(\d{2})(\d{1})/, '$1.$2.$3-$4')
  }

  // Formatar CTPS
  const formatarCTPS = (value: string) => {
    const ctps = value.replace(/\D/g, '')
    return ctps.replace(/(\d{7})/, '$1')
  }

  const handleChange = (field: string, value: any) => {
    let processedValue = value

    // Processar PIS
    if (field === 'pis') {
      const pisLimpo = value.replace(/\D/g, '')
      processedValue = pisLimpo
      
      if (pisLimpo.length === 11) {
        if (!validarPIS(pisLimpo)) {
          setErrors(prev => ({ ...prev, pis: 'PIS inválido' }))
        } else {
          setErrors(prev => ({ ...prev, pis: '' }))
        }
      }
    }

    const newFormData = { ...formData, [field]: processedValue }
    setFormData(newFormData)
    onUpdate(newFormData)
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setUploading(true)

    try {
      const uploadedFiles = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        
        // Validar tipo de arquivo
        const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf']
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({ ...prev, upload: 'Apenas arquivos JPG, PNG e PDF são permitidos' }))
          continue
        }

        // Validar tamanho (5MB)
        if (file.size > 5 * 1024 * 1024) {
          setErrors(prev => ({ ...prev, upload: 'Arquivo muito grande. Máximo 5MB' }))
          continue
        }

        // Simular upload (aqui você implementaria o upload real)
        const fileData = {
          id: Date.now() + i,
          name: file.name,
          type: file.type,
          size: file.size,
          url: URL.createObjectURL(file), // Temporário para preview
          uploadedAt: new Date().toISOString()
        }

        uploadedFiles.push(fileData)
      }

      const newDocumentos = [...formData.documentos, ...uploadedFiles]
      const newFormData = { ...formData, documentos: newDocumentos }
      setFormData(newFormData)
      onUpdate(newFormData)

      setErrors(prev => ({ ...prev, upload: '' }))

    } catch (error) {
      console.error('Erro no upload:', error)
      setErrors(prev => ({ ...prev, upload: 'Erro ao fazer upload dos arquivos' }))
    } finally {
      setUploading(false)
    }
  }

  const removeDocument = (documentId: number) => {
    const newDocumentos = formData.documentos.filter((doc: any) => doc.id !== documentId)
    const newFormData = { ...formData, documentos: newDocumentos }
    setFormData(newFormData)
    onUpdate(newFormData)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* Documentos Obrigatórios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Documentos Obrigatórios
          </CardTitle>
          <CardDescription>
            Documentos necessários para admissão
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pis">PIS/PASEP</Label>
              <Input
                id="pis"
                value={formatarPIS(formData.pis)}
                onChange={(e) => handleChange('pis', e.target.value)}
                placeholder="000.00000.00-0"
                maxLength={14}
                className={errors.pis ? 'border-red-500' : ''}
              />
              {errors.pis && (
                <p className="text-sm text-red-500 mt-1">{errors.pis}</p>
              )}
            </div>

            <div>
              <Label htmlFor="ctps">CTPS (Número)</Label>
              <Input
                id="ctps"
                value={formData.ctps}
                onChange={(e) => handleChange('ctps', e.target.value)}
                placeholder="0000000"
                maxLength={7}
              />
            </div>

            <div>
              <Label htmlFor="ctpsSerie">CTPS (Série)</Label>
              <Input
                id="ctpsSerie"
                value={formData.ctpsSerie}
                onChange={(e) => handleChange('ctpsSerie', e.target.value)}
                placeholder="0000"
                maxLength={4}
              />
            </div>

            <div>
              <Label htmlFor="tituloEleitor">Título de Eleitor</Label>
              <Input
                id="tituloEleitor"
                value={formData.tituloEleitor}
                onChange={(e) => handleChange('tituloEleitor', e.target.value)}
                placeholder="000000000000"
                maxLength={12}
              />
            </div>

            <div>
              <Label htmlFor="zonaEleitoral">Zona Eleitoral</Label>
              <Input
                id="zonaEleitoral"
                value={formData.zonaEleitoral}
                onChange={(e) => handleChange('zonaEleitoral', e.target.value)}
                placeholder="000"
                maxLength={3}
              />
            </div>

            <div>
              <Label htmlFor="secaoEleitoral">Seção Eleitoral</Label>
              <Input
                id="secaoEleitoral"
                value={formData.secaoEleitoral}
                onChange={(e) => handleChange('secaoEleitoral', e.target.value)}
                placeholder="0000"
                maxLength={4}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documentos Opcionais */}
      <Card>
        <CardHeader>
          <CardTitle>Documentos Opcionais</CardTitle>
          <CardDescription>
            Documentos adicionais quando aplicável
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="reservista">Certificado de Reservista</Label>
              <Input
                id="reservista"
                value={formData.reservista}
                onChange={(e) => handleChange('reservista', e.target.value)}
                placeholder="Número do certificado"
              />
            </div>

            <div>
              <Label htmlFor="cnh">CNH (Número)</Label>
              <Input
                id="cnh"
                value={formData.cnh}
                onChange={(e) => handleChange('cnh', e.target.value)}
                placeholder="00000000000"
                maxLength={11}
              />
            </div>

            <div>
              <Label htmlFor="cnhCategoria">CNH (Categoria)</Label>
              <Input
                id="cnhCategoria"
                value={formData.cnhCategoria}
                onChange={(e) => handleChange('cnhCategoria', e.target.value)}
                placeholder="A, B, C, D, E"
                maxLength={2}
              />
            </div>

            <div>
              <Label htmlFor="cnhVencimento">CNH (Vencimento)</Label>
              <Input
                id="cnhVencimento"
                type="date"
                value={formData.cnhVencimento}
                onChange={(e) => handleChange('cnhVencimento', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload de Documentos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload de Documentos
          </CardTitle>
          <CardDescription>
            Faça upload dos documentos digitalizados
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="fileUpload">Selecionar Arquivos</Label>
            <Input
              id="fileUpload"
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.pdf"
              onChange={handleFileUpload}
              disabled={uploading}
            />
            <p className="text-sm text-gray-600 mt-1">
              Formatos aceitos: JPG, PNG, PDF (máximo 5MB cada)
            </p>
            {errors.upload && (
              <p className="text-sm text-red-500 mt-1">{errors.upload}</p>
            )}
          </div>

          {/* Lista de Documentos */}
          {formData.documentos.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Documentos Enviados</h4>
              <div className="space-y-2">
                {formData.documentos.map((doc: any) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">{doc.name}</p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(doc.size)} • {new Date(doc.uploadedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(doc.url, '_blank')}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeDocument(doc.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resumo */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo dos Documentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {formData.pis && (
              <Badge variant="outline">PIS: {formatarPIS(formData.pis)}</Badge>
            )}
            {formData.ctps && (
              <Badge variant="outline">CTPS: {formData.ctps}/{formData.ctpsSerie}</Badge>
            )}
            {formData.tituloEleitor && (
              <Badge variant="outline">Título: {formData.tituloEleitor}</Badge>
            )}
            {formData.cnh && (
              <Badge variant="outline">CNH: {formData.cnh} ({formData.cnhCategoria})</Badge>
            )}
            {formData.documentos.length > 0 && (
              <Badge variant="secondary">{formData.documentos.length} arquivo(s) enviado(s)</Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dicas */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Dicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Mantenha os documentos sempre atualizados</li>
            <li>• Faça upload de cópias legíveis dos documentos</li>
            <li>• Verifique se todos os números estão corretos</li>
            <li>• Documentos vencidos devem ser renovados</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
