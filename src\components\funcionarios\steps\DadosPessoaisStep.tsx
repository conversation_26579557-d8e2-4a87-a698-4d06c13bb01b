"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { User, Calendar, Heart, Globe } from "lucide-react"

interface DadosPessoaisStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function DadosPessoaisStep({ data, onUpdate }: DadosPessoaisStepProps) {
  const [formData, setFormData] = useState({
    nomeCompleto: data.nomeCompleto || '',
    nomeSocial: data.nomeSocial || '',
    cpf: data.cpf || '',
    rg: data.rg || '',
    orgaoExpedidor: data.orgaoExpedidor || '',
    dataExpedicaoRg: data.dataExpedicaoRg || '',
    dataNascimento: data.dataNascimento || '',
    sexo: data.sexo || '',
    estadoCivil: data.estadoCivil || '',
    nacionalidade: data.nacionalidade || 'Brasileira',
    naturalidade: data.naturalidade || '',
    nomePai: data.nomePai || '',
    nomeMae: data.nomeMae || '',
    corRaca: data.corRaca || '',
    tipoSanguineo: data.tipoSanguineo || '',
    possuiDeficiencia: data.possuiDeficiencia || false,
    tipoDeficiencia: data.tipoDeficiencia || '',
    descricaoDeficiencia: data.descricaoDeficiencia || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Validar CPF
  const validarCPF = (cpf: string) => {
    const cpfLimpo = cpf.replace(/\D/g, '')
    if (cpfLimpo.length !== 11) return false
    
    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(cpfLimpo)) return false
    
    // Validar dígitos verificadores
    let soma = 0
    for (let i = 0; i < 9; i++) {
      soma += parseInt(cpfLimpo.charAt(i)) * (10 - i)
    }
    let resto = 11 - (soma % 11)
    if (resto === 10 || resto === 11) resto = 0
    if (resto !== parseInt(cpfLimpo.charAt(9))) return false
    
    soma = 0
    for (let i = 0; i < 10; i++) {
      soma += parseInt(cpfLimpo.charAt(i)) * (11 - i)
    }
    resto = 11 - (soma % 11)
    if (resto === 10 || resto === 11) resto = 0
    if (resto !== parseInt(cpfLimpo.charAt(10))) return false
    
    return true
  }

  // Formatar CPF
  const formatarCPF = (value: string) => {
    const cpf = value.replace(/\D/g, '')
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
  }

  // Calcular idade
  const calcularIdade = (dataNascimento: string) => {
    if (!dataNascimento) return null
    const hoje = new Date()
    const nascimento = new Date(dataNascimento)
    let idade = hoje.getFullYear() - nascimento.getFullYear()
    const mes = hoje.getMonth() - nascimento.getMonth()
    if (mes < 0 || (mes === 0 && hoje.getDate() < nascimento.getDate())) {
      idade--
    }
    return idade
  }

  const handleChange = (field: string, value: any) => {
    let processedValue = value

    // Processar CPF
    if (field === 'cpf') {
      const cpfLimpo = value.replace(/\D/g, '')
      processedValue = cpfLimpo
      
      // Validar CPF
      if (cpfLimpo.length === 11) {
        if (!validarCPF(cpfLimpo)) {
          setErrors(prev => ({ ...prev, cpf: 'CPF inválido' }))
        } else {
          setErrors(prev => ({ ...prev, cpf: '' }))
        }
      }
    }

    // Validar campos obrigatórios
    if (field === 'nomeCompleto' && !value.trim()) {
      setErrors(prev => ({ ...prev, nomeCompleto: 'Nome completo é obrigatório' }))
    } else if (field === 'nomeCompleto') {
      setErrors(prev => ({ ...prev, nomeCompleto: '' }))
    }

    if (field === 'nomeMae' && !value.trim()) {
      setErrors(prev => ({ ...prev, nomeMae: 'Nome da mãe é obrigatório' }))
    } else if (field === 'nomeMae') {
      setErrors(prev => ({ ...prev, nomeMae: '' }))
    }

    const newFormData = { ...formData, [field]: processedValue }
    setFormData(newFormData)
    onUpdate(newFormData)
  }

  const idade = calcularIdade(formData.dataNascimento)

  return (
    <div className="space-y-6">
      {/* Informações Básicas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Informações Básicas
          </CardTitle>
          <CardDescription>
            Dados pessoais fundamentais do funcionário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nomeCompleto">Nome Completo *</Label>
              <Input
                id="nomeCompleto"
                value={formData.nomeCompleto}
                onChange={(e) => handleChange('nomeCompleto', e.target.value)}
                placeholder="Nome completo do funcionário"
                className={errors.nomeCompleto ? 'border-red-500' : ''}
              />
              {errors.nomeCompleto && (
                <p className="text-sm text-red-500 mt-1">{errors.nomeCompleto}</p>
              )}
            </div>

            <div>
              <Label htmlFor="nomeSocial">Nome Social</Label>
              <Input
                id="nomeSocial"
                value={formData.nomeSocial}
                onChange={(e) => handleChange('nomeSocial', e.target.value)}
                placeholder="Nome social (opcional)"
              />
            </div>

            <div>
              <Label htmlFor="cpf">CPF *</Label>
              <Input
                id="cpf"
                value={formatarCPF(formData.cpf)}
                onChange={(e) => handleChange('cpf', e.target.value)}
                placeholder="000.000.000-00"
                maxLength={14}
                className={errors.cpf ? 'border-red-500' : ''}
              />
              {errors.cpf && (
                <p className="text-sm text-red-500 mt-1">{errors.cpf}</p>
              )}
            </div>

            <div>
              <Label htmlFor="rg">RG</Label>
              <Input
                id="rg"
                value={formData.rg}
                onChange={(e) => handleChange('rg', e.target.value)}
                placeholder="Número do RG"
              />
            </div>

            <div>
              <Label htmlFor="orgaoExpedidor">Órgão Expedidor</Label>
              <Input
                id="orgaoExpedidor"
                value={formData.orgaoExpedidor}
                onChange={(e) => handleChange('orgaoExpedidor', e.target.value)}
                placeholder="Ex: SSP/SP"
              />
            </div>

            <div>
              <Label htmlFor="dataExpedicaoRg">Data Expedição RG</Label>
              <Input
                id="dataExpedicaoRg"
                type="date"
                value={formData.dataExpedicaoRg}
                onChange={(e) => handleChange('dataExpedicaoRg', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dados Pessoais */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Dados Pessoais
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="dataNascimento">Data de Nascimento</Label>
              <Input
                id="dataNascimento"
                type="date"
                value={formData.dataNascimento}
                onChange={(e) => handleChange('dataNascimento', e.target.value)}
              />
              {idade !== null && (
                <p className="text-sm text-gray-600 mt-1">
                  {idade} anos
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="sexo">Sexo</Label>
              <Select value={formData.sexo} onValueChange={(value) => handleChange('sexo', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="M">Masculino</SelectItem>
                  <SelectItem value="F">Feminino</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="estadoCivil">Estado Civil</Label>
              <Select value={formData.estadoCivil} onValueChange={(value) => handleChange('estadoCivil', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="solteiro">Solteiro(a)</SelectItem>
                  <SelectItem value="casado">Casado(a)</SelectItem>
                  <SelectItem value="divorciado">Divorciado(a)</SelectItem>
                  <SelectItem value="viuvo">Viúvo(a)</SelectItem>
                  <SelectItem value="uniao_estavel">União Estável</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nacionalidade">Nacionalidade</Label>
              <Input
                id="nacionalidade"
                value={formData.nacionalidade}
                onChange={(e) => handleChange('nacionalidade', e.target.value)}
                placeholder="Brasileira"
              />
            </div>

            <div>
              <Label htmlFor="naturalidade">Naturalidade</Label>
              <Input
                id="naturalidade"
                value={formData.naturalidade}
                onChange={(e) => handleChange('naturalidade', e.target.value)}
                placeholder="Cidade/Estado de nascimento"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filiação */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Filiação
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nomePai">Nome do Pai</Label>
              <Input
                id="nomePai"
                value={formData.nomePai}
                onChange={(e) => handleChange('nomePai', e.target.value)}
                placeholder="Nome completo do pai"
              />
            </div>

            <div>
              <Label htmlFor="nomeMae">Nome da Mãe *</Label>
              <Input
                id="nomeMae"
                value={formData.nomeMae}
                onChange={(e) => handleChange('nomeMae', e.target.value)}
                placeholder="Nome completo da mãe"
                className={errors.nomeMae ? 'border-red-500' : ''}
              />
              {errors.nomeMae && (
                <p className="text-sm text-red-500 mt-1">{errors.nomeMae}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Informações Adicionais */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Informações Adicionais
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="corRaca">Cor/Raça</Label>
              <Select value={formData.corRaca} onValueChange={(value) => handleChange('corRaca', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="branca">Branca</SelectItem>
                  <SelectItem value="preta">Preta</SelectItem>
                  <SelectItem value="parda">Parda</SelectItem>
                  <SelectItem value="amarela">Amarela</SelectItem>
                  <SelectItem value="indigena">Indígena</SelectItem>
                  <SelectItem value="nao_declarado">Não declarado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tipoSanguineo">Tipo Sanguíneo</Label>
              <Select value={formData.tipoSanguineo} onValueChange={(value) => handleChange('tipoSanguineo', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A-">A-</SelectItem>
                  <SelectItem value="B+">B+</SelectItem>
                  <SelectItem value="B-">B-</SelectItem>
                  <SelectItem value="AB+">AB+</SelectItem>
                  <SelectItem value="AB-">AB-</SelectItem>
                  <SelectItem value="O+">O+</SelectItem>
                  <SelectItem value="O-">O-</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Deficiência */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="possuiDeficiencia"
                checked={formData.possuiDeficiencia}
                onChange={(e) => handleChange('possuiDeficiencia', e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="possuiDeficiencia">Possui deficiência</Label>
            </div>

            {formData.possuiDeficiencia && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tipoDeficiencia">Tipo de Deficiência</Label>
                  <Input
                    id="tipoDeficiencia"
                    value={formData.tipoDeficiencia}
                    onChange={(e) => handleChange('tipoDeficiencia', e.target.value)}
                    placeholder="Ex: Visual, Auditiva, Física"
                  />
                </div>

                <div>
                  <Label htmlFor="descricaoDeficiencia">Descrição</Label>
                  <Input
                    id="descricaoDeficiencia"
                    value={formData.descricaoDeficiencia}
                    onChange={(e) => handleChange('descricaoDeficiencia', e.target.value)}
                    placeholder="Descrição detalhada"
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Resumo */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo dos Dados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {formData.nomeCompleto && (
              <Badge variant="outline">Nome: {formData.nomeCompleto}</Badge>
            )}
            {formData.cpf && (
              <Badge variant="outline">CPF: {formatarCPF(formData.cpf)}</Badge>
            )}
            {idade !== null && (
              <Badge variant="outline">{idade} anos</Badge>
            )}
            {formData.sexo && (
              <Badge variant="outline">
                {formData.sexo === 'M' ? 'Masculino' : 'Feminino'}
              </Badge>
            )}
            {formData.estadoCivil && (
              <Badge variant="outline">
                {formData.estadoCivil.replace('_', ' ')}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
