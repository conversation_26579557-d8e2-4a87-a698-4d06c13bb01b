# 🏢 MÓDULO DE EMPRESAS

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar empresa principal (construtora) e empresas clientes
- Controlar alocação de funcionários por projeto/obra
- Configurar jornadas e parâmetros específicos por empresa
- Implementar hierarquia empresa principal → clientes

### **📊 RESPONSABILIDADES:**

- CRUD de empresas (principal e clientes)
- Configuração de jornadas de trabalho
- Alocação de funcionários por projeto
- Herança de configurações
- Relatórios por empresa/projeto
- Controle de custos por obra

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. GESTÃO DE EMPRESA PRINCIPAL:**

- Cadastro completo da construtora
- Configurações globais do sistema
- <PERSON><PERSON><PERSON> padr<PERSON> para herança
- Gestão de usuários administrativos
- Configurações fiscais e contábeis

#### **2. GESTÃO DE EMPRESAS CLIENTES:**

- Cadastro de projetos/obras
- Herança de configurações da principal
- Customizações específicas por projeto
- Controle de vigência de contratos
- Alocação de funcionários

#### **3. ALOCAÇÃO DE FUNCIONÁRIOS:**

- Vinculação funcionário → projeto
- Controle de período de alocação
- Transferência entre projetos
- Histórico de alocações
- Custos por funcionário/projeto

#### **4. CONFIGURAÇÕES HIERÁRQUICAS:**

- Jornadas de trabalho por empresa
- Tolerâncias específicas
- Parâmetros de ponto
- Configurações de relatórios

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE EMPRESAS** (`/empresas`)

```typescript
interface DashboardEmpresasScreen {
  resumo_geral: {
    empresa_principal: {
      nome: string
      cnpj: string
      funcionarios_total: number
      projetos_ativos: number
    }

    estatisticas: {
      total_empresas_clientes: number
      funcionarios_alocados: number
      projetos_em_andamento: number
      receita_mensal_estimada: number
    }
  }

  lista_empresas_clientes: {
    filtros: {
      status: 'ativo' | 'inativo' | 'todos'
      periodo_contrato: DateRange
      busca_texto: string
    }

    colunas: [
      'nome_projeto',
      'empresa_cliente',
      'funcionarios_alocados',
      'data_inicio',
      'data_fim',
      'status',
      'acoes',
    ]

    acoes_rapidas: {
      visualizar: '/empresas/[id]'
      editar: '/empresas/[id]/editar'
      alocar_funcionarios: '/empresas/[id]/funcionarios'
      relatorios: '/relatorios?empresa=[id]'
    }
  }

  acoes_principais: {
    nova_empresa_cliente: '/empresas/nova'
    configurar_empresa_principal: '/empresas/principal'
    relatorio_geral: '/relatorios/empresas'
  }
}
```

### **2. CADASTRO DE EMPRESA PRINCIPAL** (`/empresas/principal`)

```typescript
interface EmpresaPrincipalScreen {
  dados_empresa: {
    // Dados Básicos
    nome_fantasia: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Construtora ABC Ltda'
    }
    razao_social: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Construtora ABC Limitada'
    }
    cnpj: {
      tipo: 'text'
      mascara: 'XX.XXX.XXX/XXXX-XX'
      validacao: 'CNPJ válido'
      obrigatorio: true
    }
    inscricao_estadual: {
      tipo: 'text'
      placeholder: '123.456.789.012'
    }

    // Endereço
    endereco: {
      cep: {
        tipo: 'text'
        mascara: 'XXXXX-XXX'
        autocompletar: 'ViaCEP API'
      }
      logradouro: string
      numero: string
      complemento: string
      bairro: string
      cidade: string
      estado: string
    }

    // Contatos
    contatos: {
      telefone_principal: {
        tipo: 'tel'
        mascara: '(XX) XXXXX-XXXX'
        obrigatorio: true
      }
      telefone_secundario: {
        tipo: 'tel'
        mascara: '(XX) XXXXX-XXXX'
      }
      email_principal: {
        tipo: 'email'
        obrigatorio: true
      }
      email_financeiro: {
        tipo: 'email'
      }
      site: {
        tipo: 'url'
        placeholder: 'https://www.empresa.com.br'
      }
    }
  }

  configuracoes_globais: {
    jornadas_padrao: {
      jornada_normal: {
        entrada: '08:00'
        saida_intervalo: '12:00'
        retorno_intervalo: '13:00'
        saida: '17:00'
        horas_semanais: 44
      }
      jornada_sabado: {
        entrada: '08:00'
        saida: '12:00'
        horas_semanais: 4
      }
    }

    tolerancias_padrao: {
      atraso_tolerancia: 10 // minutos
      saida_antecipada_tolerancia: 10
      intervalo_minimo: 60
      intervalo_maximo: 120
    }

    parametros_sistema: {
      backup_automatico: boolean
      retencao_logs_dias: number
      notificacoes_email: boolean
      integracao_esocial: boolean
    }
  }
}
```

### **3. CADASTRO DE EMPRESA CLIENTE** (`/empresas/nova`)

```typescript
interface EmpresaClienteScreen {
  dados_projeto: {
    // Identificação do Projeto
    nome_projeto: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Residencial Jardim das Flores'
    }
    codigo_projeto: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'PROJ-2024-001'
      validacao: 'Código único'
    }
    descricao: {
      tipo: 'textarea'
      placeholder: 'Descrição detalhada do projeto'
    }

    // Dados da Empresa Cliente
    empresa_cliente: {
      nome_empresa: {
        tipo: 'text'
        obrigatorio: true
        placeholder: 'Incorporadora XYZ Ltda'
      }
      cnpj_cliente: {
        tipo: 'text'
        mascara: 'XX.XXX.XXX/XXXX-XX'
        validacao: 'CNPJ válido'
      }
      contato_responsavel: {
        nome: string
        cargo: string
        telefone: string
        email: string
      }
    }

    // Localização da Obra
    endereco_obra: {
      cep: {
        tipo: 'text'
        mascara: 'XXXXX-XXX'
        autocompletar: 'ViaCEP API'
      }
      logradouro: string
      numero: string
      bairro: string
      cidade: string
      estado: string
      coordenadas: {
        latitude: number
        longitude: number
      }
    }
  }

  configuracoes_projeto: {
    // Vigência
    vigencia: {
      data_inicio: {
        tipo: 'date'
        obrigatorio: true
      }
      data_fim_prevista: {
        tipo: 'date'
        obrigatorio: true
      }
      data_fim_real: {
        tipo: 'date'
        opcional: true
      }
    }

    // Jornadas Específicas
    jornadas_customizadas: {
      herdar_da_principal: {
        tipo: 'checkbox'
        padrao: true
        descricao: 'Usar jornadas da empresa principal'
      }
      jornada_personalizada: {
        entrada: 'time'
        saida_intervalo: 'time'
        retorno_intervalo: 'time'
        saida: 'time'
        trabalha_sabado: boolean
      }
    }

    // Configurações Específicas
    configuracoes_especificas: {
      tolerancia_atraso: {
        tipo: 'number'
        unidade: 'minutos'
        padrao: 'Herdar da principal'
      }
      registro_obrigatorio_gps: {
        tipo: 'checkbox'
        descricao: 'Exigir localização nos registros'
      }
      raio_permitido_metros: {
        tipo: 'number'
        condicional: 'registro_obrigatorio_gps'
        padrao: 100
      }
    }
  }

  configuracoes_financeiras: {
    // Custos
    valor_hora_base: {
      tipo: 'currency'
      descricao: 'Valor/hora padrão para funcionários'
    }
    adicional_noturno_percentual: {
      tipo: 'number'
      unidade: '%'
      padrao: 20
    }
    adicional_hora_extra_percentual: {
      tipo: 'number'
      unidade: '%'
      padrao: 50
    }

    // Faturamento
    modelo_cobranca: {
      tipo: 'select'
      opcoes: [
        'por_funcionario_mes',
        'por_hora_trabalhada',
        'valor_fixo_mensal',
        'percentual_folha',
      ]
    }
    valor_cobranca: {
      tipo: 'currency'
      condicional: 'modelo_cobranca'
    }
  }
}
```

### **4. ALOCAÇÃO DE FUNCIONÁRIOS** (`/empresas/[id]/funcionarios`)

```typescript
interface AlocacaoFuncionariosScreen {
  funcionarios_alocados: {
    lista_atual: {
      funcionario: {
        nome: string
        matricula: string
        cargo: string
        foto: string
      }
      data_alocacao: Date
      data_fim_alocacao: Date | null
      valor_hora: number
      status: 'ativo' | 'finalizado' | 'transferido'
      acoes: ['editar', 'finalizar', 'transferir']
    }[]

    estatisticas: {
      total_alocados: number
      custo_mensal_total: number
      horas_trabalhadas_mes: number
      media_presenca_percentual: number
    }
  }

  nova_alocacao: {
    buscar_funcionario: {
      filtros: {
        nome: string
        cargo: string
        setor: string
        disponivel: boolean
      }
      resultados: {
        funcionario: Funcionario
        alocacao_atual: string | null
        disponivel_a_partir: Date | null
      }[]
    }

    dados_alocacao: {
      funcionario_id: UUID
      data_inicio: Date
      data_fim_prevista: Date
      valor_hora_projeto: number
      observacoes: string
    }
  }

  transferencias: {
    funcionario_origem: Funcionario
    projeto_destino: {
      buscar_projetos: string
      projeto_selecionado: Empresa
    }
    data_transferencia: Date
    motivo_transferencia: string
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface EmpresasAPI {
  // ✅ EMPRESA PRINCIPAL
  'GET /api/empresas/principal': {
    response: EmpresaPrincipal
  }

  'PUT /api/empresas/principal': {
    body: EmpresaPrincipalData
    response: EmpresaPrincipal
  }

  // ✅ EMPRESAS CLIENTES
  'GET /api/empresas': {
    query: {
      status?: 'ativo' | 'inativo'
      search?: string
      page?: number
      limit?: number
    }
    response: {
      empresas: EmpresaCliente[]
      total: number
      page: number
      totalPages: number
    }
  }

  'POST /api/empresas': {
    body: EmpresaClienteData
    response: EmpresaCliente
  }

  'GET /api/empresas/[id]': {
    response: EmpresaCliente
  }

  'PUT /api/empresas/[id]': {
    body: Partial<EmpresaClienteData>
    response: EmpresaCliente
  }

  'DELETE /api/empresas/[id]': {
    response: { message: 'Empresa inativada' }
  }

  // ✅ ALOCAÇÃO DE FUNCIONÁRIOS
  'GET /api/empresas/[id]/funcionarios': {
    response: {
      alocados: AlocacaoFuncionario[]
      estatisticas: EstatisticasAlocacao
    }
  }

  'POST /api/empresas/[id]/funcionarios': {
    body: {
      funcionario_id: UUID
      data_inicio: Date
      data_fim_prevista?: Date
      valor_hora_projeto: number
      observacoes?: string
    }
    response: AlocacaoFuncionario
  }

  'PUT /api/empresas/[id]/funcionarios/[funcionario_id]': {
    body: Partial<AlocacaoFuncionarioData>
    response: AlocacaoFuncionario
  }

  'POST /api/empresas/[id]/funcionarios/[funcionario_id]/finalizar': {
    body: {
      data_fim: Date
      motivo: string
    }
    response: { message: 'Alocação finalizada' }
  }

  'POST /api/empresas/[id]/funcionarios/[funcionario_id]/transferir': {
    body: {
      empresa_destino_id: UUID
      data_transferencia: Date
      motivo: string
    }
    response: { message: 'Funcionário transferido' }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ EMPRESAS
CREATE TABLE empresas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Tipo
  tipo_empresa VARCHAR(20) NOT NULL, -- 'principal', 'cliente'
  empresa_principal_id UUID REFERENCES empresas(id),

  -- Dados Básicos
  nome_fantasia VARCHAR(255) NOT NULL,
  razao_social VARCHAR(255) NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  inscricao_estadual VARCHAR(50),

  -- Projeto (para clientes)
  nome_projeto VARCHAR(255),
  codigo_projeto VARCHAR(100),
  descricao TEXT,

  -- Endereço
  endereco JSONB NOT NULL,

  -- Contatos
  contatos JSONB NOT NULL,

  -- Vigência (para clientes)
  data_inicio DATE,
  data_fim_prevista DATE,
  data_fim_real DATE,

  -- Configurações
  configuracoes JSONB DEFAULT '{}',
  jornadas_trabalho JSONB,
  tolerancias JSONB,

  -- Financeiro
  configuracoes_financeiras JSONB,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),

  CONSTRAINT chk_tipo_empresa CHECK (tipo_empresa IN ('principal', 'cliente')),
  CONSTRAINT chk_empresa_principal CHECK (
    (tipo_empresa = 'principal' AND empresa_principal_id IS NULL) OR
    (tipo_empresa = 'cliente' AND empresa_principal_id IS NOT NULL)
  )
);

-- ✅ ALOCAÇÃO DE FUNCIONÁRIOS
CREATE TABLE funcionario_alocacoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  empresa_id UUID NOT NULL REFERENCES empresas(id),

  -- Período
  data_inicio DATE NOT NULL,
  data_fim_prevista DATE,
  data_fim_real DATE,

  -- Financeiro
  valor_hora_projeto DECIMAL(10,2),

  -- Status
  status VARCHAR(20) DEFAULT 'ativo', -- 'ativo', 'finalizado', 'transferido'
  motivo_finalizacao TEXT,

  -- Observações
  observacoes TEXT,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),

  CONSTRAINT chk_status_alocacao CHECK (
    status IN ('ativo', 'finalizado', 'transferido')
  ),
  CONSTRAINT chk_datas_alocacao CHECK (
    data_fim_prevista IS NULL OR data_fim_prevista >= data_inicio
  )
);

-- ✅ HISTÓRICO DE TRANSFERÊNCIAS
CREATE TABLE funcionario_transferencias (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  empresa_origem_id UUID NOT NULL REFERENCES empresas(id),
  empresa_destino_id UUID NOT NULL REFERENCES empresas(id),

  data_transferencia DATE NOT NULL,
  motivo TEXT,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ ÍNDICES
CREATE INDEX idx_empresas_tipo ON empresas(tipo_empresa);
CREATE INDEX idx_empresas_principal ON empresas(empresa_principal_id);
CREATE INDEX idx_empresas_cnpj ON empresas(cnpj);
CREATE INDEX idx_empresas_ativo ON empresas(ativo);
CREATE INDEX idx_alocacoes_funcionario ON funcionario_alocacoes(funcionario_id);
CREATE INDEX idx_alocacoes_empresa ON funcionario_alocacoes(empresa_id);
CREATE INDEX idx_alocacoes_status ON funcionario_alocacoes(status);
CREATE INDEX idx_alocacoes_periodo ON funcionario_alocacoes(data_inicio, data_fim_real);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🏢 EMPRESA PRINCIPAL:**

1. Apenas uma empresa principal por sistema
2. Não pode ser excluída, apenas editada
3. Configurações servem como padrão para clientes
4. Todos os usuários admin pertencem à principal

### **🏗️ EMPRESAS CLIENTES:**

1. Devem ter empresa principal associada
2. Herdam configurações da principal (opcional)
3. Podem ter configurações específicas
4. Vigência obrigatória (início e fim previsto)

### **👥 ALOCAÇÃO DE FUNCIONÁRIOS:**

1. Funcionário pode estar em apenas um projeto ativo
2. Transferência finaliza alocação atual e cria nova
3. Histórico completo de alocações mantido
4. Valor/hora pode ser específico por projeto

### **💰 CONFIGURAÇÕES FINANCEIRAS:**

1. Valores podem ser herdados ou customizados
2. Adicionais (noturno, extra) por projeto
3. Modelos de cobrança flexíveis
4. Relatórios de custo por projeto

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE EMPRESA:**

```typescript
describe('Gestão de Empresas', () => {
  test('Criar empresa principal', async () => {
    const empresa = await criarEmpresaPrincipal({
      nome_fantasia: 'Construtora ABC',
      cnpj: '12.345.678/0001-90',
    })
    expect(empresa.tipo_empresa).toBe('principal')
    expect(empresa.empresa_principal_id).toBeNull()
  })

  test('Criar empresa cliente', async () => {
    const principal = await criarEmpresaPrincipal()
    const cliente = await criarEmpresaCliente({
      nome_projeto: 'Projeto XYZ',
      empresa_principal_id: principal.id,
    })
    expect(cliente.tipo_empresa).toBe('cliente')
    expect(cliente.empresa_principal_id).toBe(principal.id)
  })
})
```

### **✅ TESTES DE ALOCAÇÃO:**

```typescript
describe('Alocação de Funcionários', () => {
  test('Alocar funcionário em projeto', async () => {
    const funcionario = await criarFuncionario()
    const empresa = await criarEmpresaCliente()

    const alocacao = await alocarFuncionario({
      funcionario_id: funcionario.id,
      empresa_id: empresa.id,
      data_inicio: new Date(),
    })

    expect(alocacao.status).toBe('ativo')
  })

  test('Não permitir alocação dupla', async () => {
    const funcionario = await criarFuncionario()
    const empresa1 = await criarEmpresaCliente()
    const empresa2 = await criarEmpresaCliente()

    await alocarFuncionario({
      funcionario_id: funcionario.id,
      empresa_id: empresa1.id,
    })

    await expect(
      alocarFuncionario({
        funcionario_id: funcionario.id,
        empresa_id: empresa2.id,
      })
    ).rejects.toThrow('Funcionário já alocado')
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**

- Modelo de dados
- APIs básicas CRUD
- Validações de negócio

**Semana 3-4: Telas Principais**

- Dashboard de empresas
- Cadastro empresa principal
- Cadastro empresa cliente

**Semana 5-6: Alocação**

- Sistema de alocação
- Transferências
- Histórico

**Semana 7-8: Configurações**

- Herança de configurações
- Jornadas específicas
- Testes completos

### **🔧 DEPENDÊNCIAS:**

- Módulo de Autenticação (níveis de acesso)
- ViaCEP API (endereços)
- Validação de CNPJ
- Sistema de upload (logos)
