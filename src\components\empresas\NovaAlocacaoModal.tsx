"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Search, User, Calendar, DollarSign } from "lucide-react"

interface Funcionario {
  id: string
  nomeCompleto: string
  cpf: string
  cargo: string
  setor: string
  dataAdmissao: string
  ativo: boolean
  empresa: {
    id: string
    nomeFantasia: string
  }
}

interface NovaAlocacaoModalProps {
  isOpen: boolean
  onClose: () => void
  empresaId: string
  onSuccess: () => void
}

export default function NovaAlocacaoModal({ 
  isOpen, 
  onClose, 
  empresaId, 
  onSuccess 
}: NovaAlocacaoModalProps) {
  const [funcionarios, setFuncionarios] = useState<Funcionario[]>([])
  const [funcionarioSelecionado, setFuncionarioSelecionado] = useState("")
  const [dataInicio, setDataInicio] = useState("")
  const [dataFimPrevista, setDataFimPrevista] = useState("")
  const [valorHora, setValorHora] = useState("")
  const [observacoes, setObservacoes] = useState("")
  const [buscaFuncionario, setBuscaFuncionario] = useState("")
  const [loading, setLoading] = useState(false)
  const [loadingFuncionarios, setLoadingFuncionarios] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Buscar funcionários disponíveis
  useEffect(() => {
    const fetchFuncionarios = async () => {
      try {
        setLoadingFuncionarios(true)
        const params = new URLSearchParams({
          status: 'ativo',
          limit: '100'
        })

        if (buscaFuncionario) {
          params.append('search', buscaFuncionario)
        }

        const response = await fetch(`/api/funcionarios?${params}`)
        if (!response.ok) {
          throw new Error('Erro ao buscar funcionários')
        }

        const data = await response.json()
        // Filtrar funcionários que não têm alocação ativa
        const funcionariosDisponiveis = data.funcionarios.filter((f: Funcionario) => {
          // Aqui você pode adicionar lógica para verificar se o funcionário já tem alocação ativa
          return f.ativo
        })
        
        setFuncionarios(funcionariosDisponiveis)
      } catch (err) {
        console.error('Erro ao buscar funcionários:', err)
      } finally {
        setLoadingFuncionarios(false)
      }
    }

    if (isOpen) {
      fetchFuncionarios()
    }
  }, [isOpen, buscaFuncionario])

  // Resetar formulário quando modal abre/fecha
  useEffect(() => {
    if (!isOpen) {
      setFuncionarioSelecionado("")
      setDataInicio("")
      setDataFimPrevista("")
      setValorHora("")
      setObservacoes("")
      setBuscaFuncionario("")
      setError(null)
    }
  }, [isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const payload = {
        funcionarioId: funcionarioSelecionado,
        dataInicio: new Date(dataInicio).toISOString(),
        dataFimPrevista: dataFimPrevista ? new Date(dataFimPrevista).toISOString() : undefined,
        valorHoraProjeto: valorHora ? parseFloat(valorHora) : undefined,
        observacoes: observacoes || undefined
      }

      const response = await fetch(`/api/empresas/${empresaId}/funcionarios`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro ao criar alocação')
      }

      onSuccess()
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }

  const funcionarioSelecionadoData = funcionarios.find(f => f.id === funcionarioSelecionado)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Nova Alocação de Funcionário
          </DialogTitle>
          <DialogDescription>
            Aloque um funcionário para este projeto. Todos os campos marcados com * são obrigatórios.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Busca e Seleção de Funcionário */}
          <div className="space-y-4">
            <Label htmlFor="busca-funcionario">Buscar Funcionário *</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="busca-funcionario"
                placeholder="Digite nome, CPF ou cargo..."
                value={buscaFuncionario}
                onChange={(e) => setBuscaFuncionario(e.target.value)}
                className="pl-10"
              />
            </div>

            <div>
              <Label htmlFor="funcionario">Selecionar Funcionário *</Label>
              <Select value={funcionarioSelecionado} onValueChange={setFuncionarioSelecionado}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um funcionário" />
                </SelectTrigger>
                <SelectContent>
                  {loadingFuncionarios ? (
                    <SelectItem value="loading" disabled>
                      Carregando funcionários...
                    </SelectItem>
                  ) : funcionarios.length === 0 ? (
                    <SelectItem value="empty" disabled>
                      Nenhum funcionário encontrado
                    </SelectItem>
                  ) : (
                    funcionarios.map((funcionario) => (
                      <SelectItem key={funcionario.id} value={funcionario.id}>
                        {funcionario.nomeCompleto} - {funcionario.cargo} ({funcionario.empresa.nomeFantasia})
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Detalhes do funcionário selecionado */}
            {funcionarioSelecionadoData && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Detalhes do Funcionário</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">CPF:</span> {funcionarioSelecionadoData.cpf}
                  </div>
                  <div>
                    <span className="font-medium">Cargo:</span> {funcionarioSelecionadoData.cargo}
                  </div>
                  <div>
                    <span className="font-medium">Setor:</span> {funcionarioSelecionadoData.setor || 'Não informado'}
                  </div>
                  <div>
                    <span className="font-medium">Empresa:</span> {funcionarioSelecionadoData.empresa.nomeFantasia}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Datas */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="data-inicio" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Data de Início *
              </Label>
              <Input
                id="data-inicio"
                type="date"
                value={dataInicio}
                onChange={(e) => setDataInicio(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="data-fim">Data Fim Prevista</Label>
              <Input
                id="data-fim"
                type="date"
                value={dataFimPrevista}
                onChange={(e) => setDataFimPrevista(e.target.value)}
              />
            </div>
          </div>

          {/* Valor por Hora */}
          <div>
            <Label htmlFor="valor-hora" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Valor por Hora (R$)
            </Label>
            <Input
              id="valor-hora"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={valorHora}
              onChange={(e) => setValorHora(e.target.value)}
            />
          </div>

          {/* Observações */}
          <div>
            <Label htmlFor="observacoes">Observações</Label>
            <Textarea
              id="observacoes"
              placeholder="Informações adicionais sobre a alocação..."
              value={observacoes}
              onChange={(e) => setObservacoes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Erro */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={loading || !funcionarioSelecionado || !dataInicio}
            >
              {loading ? "Criando..." : "Criar Alocação"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
