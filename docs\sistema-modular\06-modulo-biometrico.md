# 🔬 MÓDULO BIOMÉTRICO

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar dispositivos biométricos multi-fabricante
- Controlar templates biométricos com qualidade
- Sincronizar dados em tempo real
- Monitorar status e performance dos dispositivos
- Implementar princípio "NUNCA IMPEDIR" com tentativas infinitas

### **📊 RESPONSABILIDADES:**

- CRUD de dispositivos biométricos
- Gestão de templates com scoring de qualidade
- Sincronização automática bidirecional
- Monitoramento em tempo real
- Configuração de protocolos de comunicação
- Backup e recuperação de templates

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. GESTÃO DE DISPOSITIVOS:**

- Suporte multi-fabricante (Henry, ControlID, ZKTeco, TopData, Generic)
- Configuração de protocolos (TCP/IP, REST API, ZK Protocol)
- Monitoramento de status em tempo real
- Configuração de parâmetros específicos

#### **2. GESTÃO DE TEMPLATES:**

- Captura com qualidade scoring (0-100)
- Categorização por qualidade (Excelente, Boa, Regular, Ruim)
- Backup automático com criptografia AES-256
- Sincronização entre dispositivos

#### **3. CONFIGURAÇÕES AVANÇADAS:**

- Tentativas infinitas (valor 0 = infinito)
- Timeout configurável por dispositivo
- Qualidade mínima por fabricante
- Logs detalhados de operações

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD BIOMÉTRICO** (`/biometria`)

```typescript
interface DashboardBiometricoScreen {
  resumo_geral: {
    dispositivos_online: number
    dispositivos_offline: number
    templates_cadastrados: number
    sincronizacoes_pendentes: number
    ultima_sincronizacao: Date
  }

  status_dispositivos: {
    mapa_localizacao: {
      dispositivo: {
        id: UUID
        nome: string
        fabricante: string
        status: 'online' | 'offline' | 'erro'
        localizacao: { lat: number; lng: number }
        ultimo_ping: Date
      }[]
    }

    lista_dispositivos: {
      colunas: [
        'nome',
        'fabricante',
        'modelo',
        'ip',
        'status',
        'templates',
        'ultima_atividade',
        'acoes',
      ]
      filtros: {
        fabricante: string[]
        status: string[]
        localizacao: string[]
      }
    }
  }

  metricas_performance: {
    reconhecimentos_hoje: number
    tempo_medio_reconhecimento: number // ms
    taxa_sucesso_24h: number // %
    templates_qualidade_baixa: number
  }
}
```

### **2. CADASTRO DE DISPOSITIVO** (`/biometria/dispositivos/novo`)

```typescript
interface CadastroDispositivoScreen {
  dados_basicos: {
    nome: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Nome identificador do dispositivo'
    }
    fabricante: {
      tipo: 'select'
      opcoes: ['Henry', 'ControlID', 'ZKTeco', 'TopData', 'Generic']
      obrigatorio: true
    }
    modelo: {
      tipo: 'text'
      placeholder: 'Modelo específico do dispositivo'
    }
    numero_serie: {
      tipo: 'text'
      placeholder: 'Número de série'
    }
  }

  configuracao_rede: {
    ip_address: {
      tipo: 'text'
      validacao: 'IP válido'
      obrigatorio: true
      placeholder: '***********'
    }
    porta: {
      tipo: 'number'
      padrao: 4370
      min: 1
      max: 65535
    }
    protocolo: {
      tipo: 'select'
      opcoes: ['TCP/IP', 'REST_API', 'ZK_Protocol', 'HTTP']
      padrao: 'TCP/IP'
    }
    timeout_conexao: {
      tipo: 'number'
      unidade: 'segundos'
      padrao: 30
      min: 5
      max: 300
    }
  }

  configuracoes_biometria: {
    qualidade_minima: {
      tipo: 'number'
      min: 0
      max: 100
      padrao: 60
      descricao: 'Qualidade mínima para aceitar template'
    }
    tentativas_captura: {
      tipo: 'number'
      padrao: 0
      descricao: '0 = infinitas tentativas (NUNCA IMPEDIR)'
    }
    tentativas_reconhecimento: {
      tipo: 'number'
      padrao: 0
      descricao: '0 = infinitas tentativas (NUNCA IMPEDIR)'
    }
    timeout_captura: {
      tipo: 'number'
      unidade: 'segundos'
      padrao: 60
    }
  }

  localizacao: {
    empresa_id: {
      tipo: 'select'
      opcoes: 'Empresas cadastradas'
      obrigatorio: true
    }
    descricao_local: {
      tipo: 'text'
      placeholder: 'Ex: Portaria Principal, Refeitório'
    }
    coordenadas: {
      latitude: number
      longitude: number
      capturar_automatico: boolean
    }
  }
}
```

### **3. GESTÃO DE TEMPLATES** (`/biometria/templates`)

```typescript
interface GestaoTemplatesScreen {
  filtros_busca: {
    funcionario: {
      busca: string
      empresa: UUID
    }
    qualidade: {
      minima: number
      maxima: number
      categoria: 'excelente' | 'boa' | 'regular' | 'ruim'
    }
    dispositivo: UUID
    data_cadastro: DateRange
  }

  lista_templates: {
    funcionario: {
      nome: string
      matricula: string
      foto: string
      empresa: string
    }
    templates_cadastrados: {
      dispositivo: string
      qualidade: number
      categoria: 'excelente' | 'boa' | 'regular' | 'ruim'
      data_cadastro: Date
      ultimo_uso: Date
      status: 'ativo' | 'inativo' | 'corrompido'
    }[]
    acoes: ['visualizar', 'recadastrar', 'sincronizar', 'excluir']
  }

  captura_template: {
    funcionario_selecionado: Funcionario
    dispositivo_selecionado: Dispositivo

    processo_captura: {
      etapa_atual: 'preparacao' | 'captura' | 'validacao' | 'salvamento'
      tentativa_atual: number
      tentativas_maximas: 'infinitas'

      instrucoes: {
        preparacao: 'Posicione o dedo no sensor'
        captura: 'Mantenha o dedo firme no sensor'
        validacao: 'Analisando qualidade do template'
        salvamento: 'Salvando template no dispositivo'
      }

      feedback_tempo_real: {
        qualidade_atual: number
        posicionamento: 'correto' | 'ajustar'
        pressao: 'adequada' | 'muito_forte' | 'muito_fraca'
      }
    }

    resultado_captura: {
      sucesso: boolean
      qualidade_final: number
      categoria_qualidade: string
      template_id: UUID
      recomendacoes: string[]
    }
  }

  sincronizacao_templates: {
    dispositivos_origem: Dispositivo[]
    dispositivos_destino: Dispositivo[]
    funcionarios_selecionados: UUID[]

    opcoes_sincronizacao: {
      sobrescrever_existentes: boolean
      apenas_qualidade_superior: boolean
      backup_antes_sincronizar: boolean
      validar_apos_sincronizar: boolean
    }

    progresso_sincronizacao: {
      total_templates: number
      processados: number
      sucessos: number
      erros: number
      tempo_estimado: string
    }
  }
}
```

### **4. MONITORAMENTO EM TEMPO REAL** (`/biometria/monitoramento`)

```typescript
interface MonitoramentoTempoRealScreen {
  status_dispositivos: {
    grid_dispositivos: {
      dispositivo: {
        nome: string
        status: 'online' | 'offline' | 'erro' | 'manutencao'
        ultimo_ping: Date
        reconhecimentos_hoje: number
        tempo_resposta_medio: number // ms
        temperatura: number // °C
        memoria_livre: number // %
      }

      indicadores_visuais: {
        cor_status: string
        icone_fabricante: string
        barra_memoria: number
        grafico_atividade: number[]
      }
    }[]
  }

  atividade_tempo_real: {
    eventos_recentes: {
      timestamp: Date
      dispositivo: string
      funcionario: string
      tipo_evento:
        | 'reconhecimento'
        | 'falha'
        | 'template_cadastrado'
        | 'erro_conexao'
      detalhes: string
      tempo_processamento: number
    }[]

    filtros_eventos: {
      dispositivo: UUID[]
      tipo_evento: string[]
      periodo: 'ultima_hora' | 'hoje' | 'personalizado'
    }
  }

  metricas_performance: {
    graficos_tempo_real: {
      reconhecimentos_por_minuto: {
        dados: { timestamp: Date; quantidade: number }[]
        atualizacao: 'a cada 30 segundos'
      }

      tempo_resposta_dispositivos: {
        dados: { dispositivo: string; tempo_medio: number }[]
        meta_tempo: 2000 // ms
      }

      taxa_sucesso_reconhecimento: {
        dados: { hora: string; taxa: number }[]
        meta_taxa: 95 // %
      }
    }

    alertas_automaticos: {
      dispositivo_offline: {
        ativo: boolean
        tempo_limite: 300 // segundos
        notificar: string[]
      }

      tempo_resposta_alto: {
        ativo: boolean
        limite_ms: 5000
        notificar: string[]
      }

      taxa_sucesso_baixa: {
        ativo: boolean
        limite_percentual: 90
        periodo_analise: 60 // minutos
        notificar: string[]
      }
    }
  }

  manutencao_dispositivos: {
    agendamentos: {
      dispositivo: string
      tipo_manutencao: 'limpeza' | 'calibracao' | 'atualizacao' | 'substituicao'
      data_agendada: Date
      responsavel: string
      observacoes: string
    }[]

    historico_manutencoes: {
      data: Date
      dispositivo: string
      tipo: string
      executado_por: string
      tempo_inatividade: number // minutos
      resultado: 'sucesso' | 'falha' | 'parcial'
    }[]
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface BiometricoAPI {
  // ✅ DISPOSITIVOS
  'GET /api/biometria/dispositivos': {
    response: Dispositivo[]
  }

  'POST /api/biometria/dispositivos': {
    body: DispositivoData
    response: Dispositivo
  }

  'POST /api/biometria/dispositivos/[id]/testar-conexao': {
    response: {
      conectado: boolean
      tempo_resposta: number
      versao_firmware: string
      erro?: string
    }
  }

  // ✅ TEMPLATES
  'POST /api/biometria/templates/capturar': {
    body: {
      funcionario_id: UUID
      dispositivo_id: UUID
      qualidade_minima?: number
    }
    response: {
      template_id: UUID
      qualidade: number
      categoria: string
      template_data: string // encrypted
    }
  }

  'POST /api/biometria/templates/sincronizar': {
    body: {
      origem_dispositivo_id: UUID
      destino_dispositivos_ids: UUID[]
      funcionarios_ids?: UUID[]
      opcoes: SincronizacaoOpcoes
    }
    response: {
      job_id: UUID
      total_templates: number
      tempo_estimado: number
    }
  }

  'GET /api/biometria/templates/sincronizacao/[job_id]/status': {
    response: {
      status: 'processando' | 'concluido' | 'erro'
      progresso: number
      sucessos: number
      erros: number
      detalhes_erro?: string[]
    }
  }

  // ✅ RECONHECIMENTO
  'POST /api/biometria/reconhecer': {
    body: {
      dispositivo_id: UUID
      template_capturado: string
      qualidade_minima?: number
    }
    response: {
      funcionario_id?: UUID
      confianca: number
      tempo_processamento: number
      reconhecido: boolean
    }
  }

  // ✅ MONITORAMENTO
  'GET /api/biometria/status': {
    response: {
      dispositivos_online: number
      dispositivos_offline: number
      reconhecimentos_hoje: number
      tempo_medio_resposta: number
    }
  }

  'GET /api/biometria/eventos': {
    query: {
      dispositivo_id?: UUID
      tipo_evento?: string
      desde?: Date
      limite?: number
    }
    response: EventoBiometrico[]
  }

  'GET /api/biometria/dispositivos/[id]/metricas': {
    query: {
      periodo: 'hora' | 'dia' | 'semana'
    }
    response: {
      reconhecimentos: number[]
      tempos_resposta: number[]
      taxa_sucesso: number[]
      timestamps: Date[]
    }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ DISPOSITIVOS BIOMÉTRICOS
CREATE TABLE dispositivos_biometricos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  empresa_id UUID NOT NULL REFERENCES empresas(id),

  -- Identificação
  nome VARCHAR(255) NOT NULL,
  fabricante VARCHAR(50) NOT NULL,
  modelo VARCHAR(100),
  numero_serie VARCHAR(100),

  -- Configuração de Rede
  ip_address INET NOT NULL,
  porta INTEGER DEFAULT 4370,
  protocolo VARCHAR(20) DEFAULT 'TCP/IP',
  timeout_conexao INTEGER DEFAULT 30,

  -- Configurações Biométricas
  qualidade_minima INTEGER DEFAULT 60,
  tentativas_captura INTEGER DEFAULT 0, -- 0 = infinitas
  tentativas_reconhecimento INTEGER DEFAULT 0, -- 0 = infinitas
  timeout_captura INTEGER DEFAULT 60,

  -- Localização
  descricao_local VARCHAR(255),
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),

  -- Status
  status VARCHAR(20) DEFAULT 'offline',
  ultimo_ping TIMESTAMP,
  versao_firmware VARCHAR(50),

  -- Configurações Específicas do Fabricante
  configuracoes_fabricante JSONB DEFAULT '{}',

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_fabricante CHECK (
    fabricante IN ('Henry', 'ControlID', 'ZKTeco', 'TopData', 'Generic')
  ),
  CONSTRAINT chk_protocolo CHECK (
    protocolo IN ('TCP/IP', 'REST_API', 'ZK_Protocol', 'HTTP')
  )
);

-- ✅ TEMPLATES BIOMÉTRICOS
CREATE TABLE templates_biometricos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  dispositivo_id UUID NOT NULL REFERENCES dispositivos_biometricos(id),

  -- Template
  template_data TEXT NOT NULL, -- Encrypted template
  qualidade INTEGER NOT NULL,
  categoria VARCHAR(20) GENERATED ALWAYS AS (
    CASE
      WHEN qualidade >= 85 THEN 'excelente'
      WHEN qualidade >= 70 THEN 'boa'
      WHEN qualidade >= 50 THEN 'regular'
      ELSE 'ruim'
    END
  ) STORED,

  -- Metadados
  algoritmo_captura VARCHAR(50),
  versao_algoritmo VARCHAR(20),
  hash_template VARCHAR(64),

  -- Uso
  ultimo_reconhecimento TIMESTAMP,
  total_reconhecimentos INTEGER DEFAULT 0,

  -- Status
  status VARCHAR(20) DEFAULT 'ativo',

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_qualidade CHECK (qualidade >= 0 AND qualidade <= 100),
  CONSTRAINT chk_status_template CHECK (
    status IN ('ativo', 'inativo', 'corrompido')
  ),

  -- Único template por funcionário por dispositivo
  UNIQUE(funcionario_id, dispositivo_id)
);

-- ✅ EVENTOS BIOMÉTRICOS
CREATE TABLE eventos_biometricos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dispositivo_id UUID NOT NULL REFERENCES dispositivos_biometricos(id),
  funcionario_id UUID REFERENCES funcionarios(id),

  -- Evento
  tipo_evento VARCHAR(50) NOT NULL,
  timestamp_evento TIMESTAMP DEFAULT NOW(),

  -- Dados do Evento
  template_qualidade INTEGER,
  confianca_reconhecimento DECIMAL(5,2),
  tempo_processamento INTEGER, -- ms

  -- Resultado
  sucesso BOOLEAN NOT NULL,
  erro_codigo VARCHAR(20),
  erro_descricao TEXT,

  -- Contexto
  ip_origem INET,
  dados_adicionais JSONB,

  CONSTRAINT chk_tipo_evento CHECK (
    tipo_evento IN (
      'reconhecimento', 'falha_reconhecimento', 'template_cadastrado',
      'template_atualizado', 'template_removido', 'erro_conexao',
      'dispositivo_online', 'dispositivo_offline', 'manutencao'
    )
  )
);

-- ✅ SINCRONIZAÇÕES
CREATE TABLE sincronizacoes_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Origem e Destino
  dispositivo_origem_id UUID NOT NULL REFERENCES dispositivos_biometricos(id),
  dispositivos_destino_ids UUID[] NOT NULL,
  funcionarios_ids UUID[],

  -- Configuração
  opcoes JSONB NOT NULL,

  -- Progresso
  status VARCHAR(20) DEFAULT 'iniciado',
  total_templates INTEGER,
  templates_processados INTEGER DEFAULT 0,
  templates_sucesso INTEGER DEFAULT 0,
  templates_erro INTEGER DEFAULT 0,

  -- Resultado
  detalhes_erro JSONB,
  tempo_inicio TIMESTAMP DEFAULT NOW(),
  tempo_fim TIMESTAMP,

  -- Auditoria
  created_by UUID REFERENCES users(id),

  CONSTRAINT chk_status_sincronizacao CHECK (
    status IN ('iniciado', 'processando', 'concluido', 'erro', 'cancelado')
  )
);

-- ✅ ÍNDICES
CREATE INDEX idx_dispositivos_empresa ON dispositivos_biometricos(empresa_id);
CREATE INDEX idx_dispositivos_status ON dispositivos_biometricos(status);
CREATE INDEX idx_dispositivos_ip ON dispositivos_biometricos(ip_address);
CREATE INDEX idx_templates_funcionario ON templates_biometricos(funcionario_id);
CREATE INDEX idx_templates_dispositivo ON templates_biometricos(dispositivo_id);
CREATE INDEX idx_templates_qualidade ON templates_biometricos(qualidade);
CREATE INDEX idx_eventos_dispositivo_timestamp ON eventos_biometricos(dispositivo_id, timestamp_evento);
CREATE INDEX idx_eventos_tipo ON eventos_biometricos(tipo_evento);
CREATE INDEX idx_sincronizacoes_status ON sincronizacoes_templates(status);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🔬 PRINCÍPIO FUNDAMENTAL:**

**"NUNCA IMPEDIR FUNCIONÁRIO ATIVO"**

- Tentativas infinitas (0 = infinito)
- Sistema sempre permite nova tentativa
- Logs detalhados para análise posterior

### **📊 QUALIDADE DE TEMPLATES:**

- **Excelente (85-100)**: Uso preferencial
- **Boa (70-84)**: Uso normal
- **Regular (50-69)**: Uso com alerta
- **Ruim (0-49)**: Recadastro recomendado

### **🔄 SINCRONIZAÇÃO:**

- Automática a cada 4 horas
- Manual sob demanda
- Backup antes de sincronizar
- Validação pós-sincronização

### **📡 MONITORAMENTO:**

- Ping a cada 30 segundos
- Alertas automáticos
- Métricas em tempo real
- Histórico de 90 dias

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE DISPOSITIVOS:**

```typescript
describe('Dispositivos Biométricos', () => {
  test('Conectar dispositivo Henry', async () => {
    const dispositivo = await criarDispositivo({
      fabricante: 'Henry',
      ip_address: '*************',
    })

    const conexao = await testarConexao(dispositivo.id)
    expect(conexao.conectado).toBe(true)
  })

  test('Configurar tentativas infinitas', async () => {
    const dispositivo = await criarDispositivo({
      tentativas_captura: 0,
      tentativas_reconhecimento: 0,
    })

    expect(dispositivo.tentativas_captura).toBe(0)
    expect(dispositivo.tentativas_reconhecimento).toBe(0)
  })
})
```

### **✅ TESTES DE TEMPLATES:**

```typescript
describe('Templates Biométricos', () => {
  test('Capturar template com qualidade', async () => {
    const template = await capturarTemplate({
      funcionario_id: 'uuid',
      dispositivo_id: 'uuid',
      qualidade_minima: 60,
    })

    expect(template.qualidade).toBeGreaterThanOrEqual(60)
    expect(template.categoria).toBeDefined()
  })

  test('Sincronizar templates entre dispositivos', async () => {
    const job = await sincronizarTemplates({
      origem: 'dispositivo1',
      destinos: ['dispositivo2', 'dispositivo3'],
    })

    expect(job.job_id).toBeDefined()
    expect(job.total_templates).toBeGreaterThan(0)
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Dispositivos**

- Modelo de dados
- APIs de comunicação
- Testes de conexão

**Semana 3-4: Templates**

- Captura e qualidade
- Criptografia e backup
- Sincronização

**Semana 5-6: Monitoramento**

- Dashboard tempo real
- Alertas automáticos
- Métricas de performance

**Semana 7-8: Integrações**

- Protocolos específicos
- Otimizações
- Testes completos

### **🔧 DEPENDÊNCIAS:**

- Módulo de Funcionários
- Módulo de Empresas
- Protocolos TCP/IP, REST, ZK
- Criptografia AES-256
- Sistema de alertas
- WebSockets (tempo real)
