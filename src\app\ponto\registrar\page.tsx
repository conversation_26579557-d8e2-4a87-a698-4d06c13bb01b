"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

export default function RegistrarPontoPage() {
  const router = useRouter()
  const [step, setStep] = useState<'identificacao' | 'confirmacao' | 'sucesso'>('identificacao')
  const [funcionario, setFuncionario] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [busca, setBusca] = useState('')
  const [tipoRegistro, setTipoRegistro] = useState<'entrada' | 'saida' | 'intervalo_inicio' | 'intervalo_fim'>('entrada')
  const [observacoes, setObservacoes] = useState('')

  const buscarFuncionario = async () => {
    if (!busca.trim()) return

    setLoading(true)
    try {
      // Simular busca de funcionário
      // Em produção, seria uma chamada para API
      const funcionarioMock = {
        id: '1',
        nomeCompleto: '<PERSON>',
        matricula: '001',
        cpf: '123.456.789-00',
        cargo: 'Pedreiro',
        empresa: 'Construtora ABC',
        foto: null,
        ultimoRegistro: {
          tipo: 'saida',
          dataHora: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 horas atrás
        }
      }
      
      setFuncionario(funcionarioMock)
      setStep('confirmacao')
      
      // Sugerir próximo tipo de registro baseado no último
      if (funcionarioMock.ultimoRegistro?.tipo === 'saida') {
        setTipoRegistro('entrada')
      } else if (funcionarioMock.ultimoRegistro?.tipo === 'entrada') {
        setTipoRegistro('intervalo_inicio')
      } else if (funcionarioMock.ultimoRegistro?.tipo === 'intervalo_inicio') {
        setTipoRegistro('intervalo_fim')
      } else {
        setTipoRegistro('saida')
      }
    } catch (error) {
      alert('Funcionário não encontrado')
    } finally {
      setLoading(false)
    }
  }

  const confirmarRegistro = async () => {
    setLoading(true)
    try {
      // Simular registro de ponto
      // Em produção, seria uma chamada para API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStep('sucesso')
      
      // Voltar para identificação após 3 segundos
      setTimeout(() => {
        setStep('identificacao')
        setFuncionario(null)
        setBusca('')
        setObservacoes('')
      }, 3000)
    } catch (error) {
      alert('Erro ao registrar ponto')
    } finally {
      setLoading(false)
    }
  }

  const voltarIdentificacao = () => {
    setStep('identificacao')
    setFuncionario(null)
    setBusca('')
    setObservacoes('')
  }

  const getTipoRegistroLabel = (tipo: string) => {
    const labels = {
      'entrada': 'Entrada',
      'saida': 'Saída',
      'intervalo_inicio': 'Início do Intervalo',
      'intervalo_fim': 'Fim do Intervalo'
    }
    return labels[tipo as keyof typeof labels] || tipo
  }

  const getTipoRegistroColor = (tipo: string) => {
    const colors = {
      'entrada': 'bg-green-500',
      'saida': 'bg-red-500',
      'intervalo_inicio': 'bg-yellow-500',
      'intervalo_fim': 'bg-blue-500'
    }
    return colors[tipo as keyof typeof colors] || 'bg-gray-500'
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 text-gray-600 hover:text-gray-900"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-3xl font-bold text-gray-900">Registrar Ponto</h1>
            </div>
            <div className="text-right">
              <div className="text-lg font-mono font-bold text-gray-900">
                {new Date().toLocaleTimeString('pt-BR')}
              </div>
              <div className="text-sm text-gray-600">
                {new Date().toLocaleDateString('pt-BR')}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-6">
        <div className="max-w-md w-full">
          
          {/* Etapa 1: Identificação */}
          {step === 'identificacao' && (
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Identificar Funcionário</h2>
                <p className="text-gray-600">Digite a matrícula, nome ou CPF</p>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Buscar Funcionário
                  </label>
                  <input
                    type="text"
                    value={busca}
                    onChange={(e) => setBusca(e.target.value)}
                    placeholder="Matrícula, nome ou CPF..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                    onKeyPress={(e) => e.key === 'Enter' && buscarFuncionario()}
                  />
                </div>

                <button
                  onClick={buscarFuncionario}
                  disabled={!busca.trim() || loading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'Buscando...' : 'Buscar Funcionário'}
                </button>

                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-4">ou</p>
                  <button className="text-blue-600 hover:text-blue-700 font-medium">
                    Usar Biometria
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Etapa 2: Confirmação */}
          {step === 'confirmacao' && funcionario && (
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-gray-700">
                    {funcionario.nomeCompleto.split(' ').map((n: string) => n.charAt(0)).slice(0, 2).join('')}
                  </span>
                </div>
                <h2 className="text-xl font-bold text-gray-900">{funcionario.nomeCompleto}</h2>
                <p className="text-gray-600">Matrícula: {funcionario.matricula}</p>
                <p className="text-gray-600">{funcionario.cargo} • {funcionario.empresa}</p>
              </div>

              {/* Último Registro */}
              {funcionario.ultimoRegistro && (
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Último Registro</h3>
                  <div className="flex justify-between items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      funcionario.ultimoRegistro.tipo === 'entrada' ? 'bg-green-100 text-green-800' :
                      funcionario.ultimoRegistro.tipo === 'saida' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {getTipoRegistroLabel(funcionario.ultimoRegistro.tipo)}
                    </span>
                    <span className="text-sm text-gray-600">
                      {new Date(funcionario.ultimoRegistro.dataHora).toLocaleString('pt-BR')}
                    </span>
                  </div>
                </div>
              )}

              {/* Tipo de Registro */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Tipo de Registro
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {['entrada', 'saida', 'intervalo_inicio', 'intervalo_fim'].map((tipo) => (
                    <button
                      key={tipo}
                      onClick={() => setTipoRegistro(tipo as any)}
                      className={`p-3 rounded-lg border-2 transition-colors ${
                        tipoRegistro === tipo
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`w-4 h-4 rounded-full mx-auto mb-2 ${getTipoRegistroColor(tipo)}`}></div>
                      <div className="text-sm font-medium text-gray-900">
                        {getTipoRegistroLabel(tipo)}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Observações */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Observações (opcional)
                </label>
                <textarea
                  value={observacoes}
                  onChange={(e) => setObservacoes(e.target.value)}
                  placeholder="Adicione observações sobre este registro..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Ações */}
              <div className="flex space-x-4">
                <button
                  onClick={voltarIdentificacao}
                  className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  Voltar
                </button>
                <button
                  onClick={confirmarRegistro}
                  disabled={loading}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'Registrando...' : 'Confirmar Registro'}
                </button>
              </div>
            </div>
          )}

          {/* Etapa 3: Sucesso */}
          {step === 'sucesso' && (
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ponto Registrado!</h2>
              <p className="text-gray-600 mb-4">
                {getTipoRegistroLabel(tipoRegistro)} registrada com sucesso
              </p>
              <div className="text-lg font-mono font-bold text-gray-900 mb-2">
                {new Date().toLocaleTimeString('pt-BR')}
              </div>
              <div className="text-sm text-gray-600">
                {new Date().toLocaleDateString('pt-BR')}
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Redirecionando automaticamente...
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
