// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ✅ AUTENTICAÇÃO E USUÁRIOS
model User {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String   @unique @db.VarChar(255)
  passwordHash  String?  @map("password_hash") @db.VarChar(255)
  nome          String   @db.VarChar(255)
  nivelAcesso   String   @map("nivel_acesso") @db.VarChar(50)
  ativo         Boolean  @default(true)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")

  // Relacionamentos
  registrosPontoValidados RegistroPonto[] @relation("ValidadorRegistros")
  registrosPontoCriados   RegistroPonto[] @relation("CriadorRegistros")
  funcionarioEpisEntregues FuncionarioEpi[] @relation("EntregadorEpis")
  funcionarioEpisRecebidos FuncionarioEpi[] @relation("RecebedorEpis")
  alocacoesCriadas   FuncionarioAlocacao[] @relation("AlocacoesCriadas")
  empresasCriadas    Empresa[]
  notificacoes       Notificacao[]
  logsAuditoria      LogAuditoria[]
  configuracoesSistema ConfiguracaoSistema[]

  @@map("users")
}

// ✅ EMPRESAS
model Empresa {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  // Tipo e hierarquia
  tipoEmpresa           String   @map("tipo_empresa") @db.VarChar(20) // 'principal', 'cliente'
  empresaPrincipalId    String?  @map("empresa_principal_id") @db.Uuid

  // Dados básicos
  nomeFantasia          String   @map("nome_fantasia") @db.VarChar(255)
  razaoSocial           String   @map("razao_social") @db.VarChar(255)
  cnpj                  String   @unique @db.VarChar(18)
  inscricaoEstadual     String?  @map("inscricao_estadual") @db.VarChar(50)

  // Projeto (para clientes)
  nomeProjeto           String?  @map("nome_projeto") @db.VarChar(255)
  codigoProjeto         String?  @map("codigo_projeto") @db.VarChar(100)
  descricao             String?  @db.Text

  // Endereço (JSON)
  endereco              Json     @default("{}")

  // Contatos (JSON)
  contatos              Json     @default("{}")

  // Vigência (para clientes)
  dataInicio            DateTime? @map("data_inicio") @db.Date
  dataFimPrevista       DateTime? @map("data_fim_prevista") @db.Date
  dataFimReal           DateTime? @map("data_fim_real") @db.Date

  // Configurações (JSON)
  configuracoes         Json     @default("{}")
  jornadasTrabalhoConfig Json?   @map("jornadas_trabalho")
  tolerancias           Json?    @default("{}")

  // Financeiro (JSON)
  configuracoesFin      Json?    @map("configuracoes_financeiras") @default("{}")

  // Status
  ativo                 Boolean  @default(true)

  // Auditoria
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")
  createdBy             String?  @map("created_by") @db.Uuid

  // Relacionamentos
  empresaPrincipal      Empresa? @relation("EmpresaHierarquia", fields: [empresaPrincipalId], references: [id])
  empresasClientes      Empresa[] @relation("EmpresaHierarquia")
  funcionarios          Funcionario[]
  funcionarioAlocacoes  FuncionarioAlocacao[]
  jornadasTrabalho      JornadaTrabalho[]
  turnos                Turno[]
  configuracoesSistema  ConfiguracaoSistema[]
  criadoPor             User?    @relation(fields: [createdBy], references: [id])

  @@map("empresas")
}

// ✅ FUNCIONÁRIOS
model Funcionario {
  id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  empresaId         String    @map("empresa_id") @db.Uuid
  
  // Dados Pessoais
  nomeCompleto      String    @map("nome_completo") @db.VarChar(255)
  cpf               String    @unique @db.VarChar(14)
  rg                String?   @db.VarChar(20)
  dataNascimento    DateTime? @map("data_nascimento") @db.Date
  sexo              String?   @db.VarChar(1)
  estadoCivil       String?   @map("estado_civil") @db.VarChar(20)
  
  // Contato
  telefone          String?   @db.VarChar(20)
  email             String?   @db.VarChar(255)
  endereco          String?   @db.Text
  
  // Dados Profissionais
  matricula         String    @unique @db.VarChar(50)
  cargo             String    @db.VarChar(255)
  setor             String?   @db.VarChar(255)
  dataAdmissao      DateTime  @map("data_admissao") @db.Date
  dataDemissao      DateTime? @map("data_demissao") @db.Date
  salario           Decimal?  @db.Decimal(10, 2)
  
  // Biometria
  templateBiometrico String?  @map("template_biometrico") @db.Text
  
  // Status
  ativo             Boolean   @default(true)
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")

  // Relacionamentos
  empresa           Empresa   @relation(fields: [empresaId], references: [id])
  registrosPonto    RegistroPonto[]
  funcionarioEpis   FuncionarioEpi[]
  alocacoes         FuncionarioAlocacao[]
  escalas           Escala[]
  afastamentos      Afastamento[]
  bancoHoras        BancoHora[]

  @@map("funcionarios")
}

// ✅ REGISTROS DE PONTO
model RegistroPonto {
  id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId     String    @map("funcionario_id") @db.Uuid
  
  // Dados do Registro
  dataHora          DateTime  @map("data_hora")
  tipoRegistro      String    @map("tipo_registro") @db.VarChar(20) // 'entrada', 'saida', 'pausa_inicio', 'pausa_fim'
  localizacao       String?   @db.VarChar(255)
  
  // Origem do Registro
  origemRegistro    String    @map("origem_registro") @db.VarChar(50) // 'biometria', 'manual', 'app_mobile'
  dispositivoId     String?   @map("dispositivo_id") @db.Uuid
  
  // Validação
  validado          Boolean   @default(false)
  validadoPor       String?   @map("validado_por") @db.Uuid
  dataValidacao     DateTime? @map("data_validacao")
  
  // Observações
  observacoes       String?   @db.Text
  
  // Auditoria
  createdAt         DateTime  @default(now()) @map("created_at")
  createdBy         String?   @map("created_by") @db.Uuid

  // Relacionamentos
  funcionario       Funcionario @relation(fields: [funcionarioId], references: [id])
  validador         User?       @relation("ValidadorRegistros", fields: [validadoPor], references: [id])
  criador           User?       @relation("CriadorRegistros", fields: [createdBy], references: [id])
  dispositivo       DispositivoBiometrico? @relation(fields: [dispositivoId], references: [id])

  @@map("registros_ponto")
}

// ✅ EPIs
model Epi {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nome          String   @db.VarChar(255)
  categoria     String   @db.VarChar(100)
  descricao     String?  @db.Text
  numeroCa      String?  @map("numero_ca") @db.VarChar(20)
  validadeCa    DateTime? @map("validade_ca") @db.Date
  ativo         Boolean  @default(true)

  // Relacionamentos
  funcionarioEpis FuncionarioEpi[]

  @@map("epis")
}

model FuncionarioEpi {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId   String    @map("funcionario_id") @db.Uuid
  epiId           String    @map("epi_id") @db.Uuid
  dataEntrega     DateTime  @map("data_entrega") @db.Date
  dataDevolucao   DateTime? @map("data_devolucao") @db.Date
  quantidade      Int       @default(1)
  observacoes     String?   @db.Text
  entreguePor     String?   @map("entregue_por") @db.Uuid
  recebidoPor     String?   @map("recebido_por") @db.Uuid

  // Relacionamentos
  funcionario     Funcionario @relation(fields: [funcionarioId], references: [id])
  epi             Epi         @relation(fields: [epiId], references: [id])
  entregador      User?       @relation("EntregadorEpis", fields: [entreguePor], references: [id])
  recebedor       User?       @relation("RecebedorEpis", fields: [recebidoPor], references: [id])

  @@map("funcionario_epis")
}

// ✅ ALOCAÇÃO DE FUNCIONÁRIOS
model FuncionarioAlocacao {
  id                  String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId       String    @map("funcionario_id") @db.Uuid
  empresaId           String    @map("empresa_id") @db.Uuid

  // Período
  dataInicio          DateTime  @map("data_inicio") @db.Date
  dataFimPrevista     DateTime? @map("data_fim_prevista") @db.Date
  dataFimReal         DateTime? @map("data_fim_real") @db.Date

  // Financeiro
  valorHoraProjeto    Decimal?  @map("valor_hora_projeto") @db.Decimal(10,2)

  // Status
  status              String    @default("ativo") @db.VarChar(20) // 'ativo', 'finalizado', 'transferido'
  motivoFinalizacao   String?   @map("motivo_finalizacao") @db.Text

  // Observações
  observacoes         String?   @db.Text

  // Auditoria
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @default(now()) @updatedAt @map("updated_at")
  createdBy           String?   @map("created_by") @db.Uuid

  // Relacionamentos
  funcionario         Funcionario @relation(fields: [funcionarioId], references: [id])
  empresa             Empresa     @relation(fields: [empresaId], references: [id])
  criadoPor           User?       @relation("AlocacoesCriadas", fields: [createdBy], references: [id])

  @@map("funcionario_alocacoes")
}

// ✅ DISPOSITIVOS BIOMÉTRICOS
model DispositivoBiometrico {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nome            String   @db.VarChar(255)
  fabricante      String   @db.VarChar(100)
  modelo          String?  @db.VarChar(100)
  numeroSerie     String?  @map("numero_serie") @db.VarChar(100)
  ipAddress       String?  @map("ip_address") @db.VarChar(15)
  porta           Int?
  ativo           Boolean  @default(true)
  ultimaConexao   DateTime? @map("ultima_conexao")
  configuracoes   Json?
  createdAt       DateTime @default(now()) @map("created_at")

  // Relacionamentos
  registrosPonto  RegistroPonto[]

  @@map("dispositivos_biometricos")
}

// ✅ LICENÇA ATIVA
model LicencaAtiva {
  id              Int      @id @default(autoincrement())
  chaveLicenca    String   @unique @map("chave_licenca") @db.VarChar(29)
  tipoLicenca     String   @map("tipo_licenca") @db.VarChar(20)
  nivelPlano      String   @map("nivel_plano") @db.VarChar(20)
  dataInicio      DateTime @map("data_inicio") @db.Date
  dataVencimento  DateTime @map("data_vencimento") @db.Date
  maxFuncionarios Int      @map("max_funcionarios")
  statusLicenca   String   @default("ativa") @map("status_licenca") @db.VarChar(20)
  instaladaEm     DateTime @default(now()) @map("instalada_em")

  @@map("licenca_ativa")
}

// ✅ JORNADAS DE TRABALHO
model JornadaTrabalho {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nome                  String   @db.VarChar(255)
  empresaId             String   @map("empresa_id") @db.Uuid
  tipoJornada           String   @map("tipo_jornada") @db.VarChar(50) // 'normal', 'flexivel', 'escala'
  cargaHorariaSemanal   Int      @map("carga_horaria_semanal") // em minutos
  toleranciaEntrada     Int?     @map("tolerancia_entrada") // em minutos
  toleranciaSaida       Int?     @map("tolerancia_saida") // em minutos
  configuracoes         Json?
  ativo                 Boolean  @default(true)
  createdAt             DateTime @default(now()) @map("created_at")

  // Relacionamentos
  empresa               Empresa  @relation(fields: [empresaId], references: [id])

  @@map("jornadas_trabalho")
}

// ✅ TURNOS
model Turno {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  nome            String   @db.VarChar(255)
  empresaId       String   @map("empresa_id") @db.Uuid
  horarioInicio   String   @map("horario_inicio") @db.VarChar(8) // HH:MM:SS
  horarioFim      String   @map("horario_fim") @db.VarChar(8) // HH:MM:SS
  intervalos      Json?
  configuracoes   Json?
  ativo           Boolean  @default(true)
  createdAt       DateTime @default(now()) @map("created_at")

  // Relacionamentos
  empresa         Empresa  @relation(fields: [empresaId], references: [id])
  escalas         Escala[]

  @@map("turnos")
}

// ✅ ESCALAS
model Escala {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId String    @map("funcionario_id") @db.Uuid
  turnoId       String    @map("turno_id") @db.Uuid
  dataInicio    DateTime  @map("data_inicio") @db.Date
  dataFim       DateTime? @map("data_fim") @db.Date
  observacoes   String?   @db.Text
  ativo         Boolean   @default(true)
  createdAt     DateTime  @default(now()) @map("created_at")

  // Relacionamentos
  funcionario   Funcionario @relation(fields: [funcionarioId], references: [id])
  turno         Turno       @relation(fields: [turnoId], references: [id])

  @@map("escalas")
}

// ✅ CONFIGURAÇÕES DO SISTEMA
model ConfiguracaoSistema {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  chave       String    @unique @db.VarChar(255)
  valor       Json
  tipo        String    @db.VarChar(50) // 'sistema', 'empresa', 'usuario'
  empresaId   String?   @map("empresa_id") @db.Uuid
  usuarioId   String?   @map("usuario_id") @db.Uuid
  descricao   String?   @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")

  // Relacionamentos
  empresa     Empresa?  @relation(fields: [empresaId], references: [id])
  usuario     User?     @relation(fields: [usuarioId], references: [id])

  @@map("configuracoes_sistema")
}

// ✅ NOTIFICAÇÕES
model Notificacao {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  usuarioId       String    @map("usuario_id") @db.Uuid
  tipo            String    @db.VarChar(50) // 'info', 'warning', 'error', 'success'
  titulo          String    @db.VarChar(255)
  mensagem        String    @db.Text
  lida            Boolean   @default(false)
  dataLeitura     DateTime? @map("data_leitura")
  dadosContexto   Json?     @map("dados_contexto")
  createdAt       DateTime  @default(now()) @map("created_at")

  // Relacionamentos
  usuario         User      @relation(fields: [usuarioId], references: [id])

  @@map("notificacoes")
}

// ✅ AFASTAMENTOS
model Afastamento {
  id                String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId     String    @map("funcionario_id") @db.Uuid
  tipoAfastamento   String    @map("tipo_afastamento") @db.VarChar(50) // 'ferias', 'licenca_medica', etc.
  dataInicio        DateTime  @map("data_inicio") @db.Date
  dataFim           DateTime  @map("data_fim") @db.Date
  dataRetorno       DateTime? @map("data_retorno") @db.Date
  motivo            String?   @db.Text
  documentoAnexo    String?   @map("documento_anexo") @db.VarChar(255)
  aprovado          Boolean   @default(false)
  aprovadoPor       String?   @map("aprovado_por") @db.Uuid
  dataAprovacao     DateTime? @map("data_aprovacao")
  observacoes       String?   @db.Text
  createdAt         DateTime  @default(now()) @map("created_at")

  // Relacionamentos
  funcionario       Funcionario @relation(fields: [funcionarioId], references: [id])

  @@map("afastamentos")
}

// ✅ BANCO DE HORAS
model BancoHora {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  funcionarioId   String    @map("funcionario_id") @db.Uuid
  dataReferencia  DateTime  @map("data_referencia") @db.Date
  saldoAnterior   Int       @default(0) @map("saldo_anterior") // em minutos
  credito         Int       @default(0) // em minutos
  debito          Int       @default(0) // em minutos
  saldoAtual      Int       @default(0) @map("saldo_atual") // em minutos
  observacoes     String?   @db.Text
  createdAt       DateTime  @default(now()) @map("created_at")

  // Relacionamentos
  funcionario     Funcionario @relation(fields: [funcionarioId], references: [id])

  @@map("banco_horas")
}

// ✅ AUDITORIA (IMUTÁVEL)
model LogAuditoria {
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  usuarioId       String?   @map("usuario_id") @db.Uuid
  acao            String    @db.VarChar(100)
  tabelaAfetada   String?   @map("tabela_afetada") @db.VarChar(100)
  registroId      String?   @map("registro_id") @db.Uuid
  dadosAnteriores Json?     @map("dados_anteriores")
  dadosNovos      Json?     @map("dados_novos")
  ipAddress       String?   @map("ip_address") @db.VarChar(45)
  userAgent       String?   @map("user_agent") @db.Text
  timestamp       DateTime  @default(now())

  // Relacionamentos
  usuario         User?     @relation(fields: [usuarioId], references: [id])

  @@map("logs_auditoria")
}
