"use client"

import { <PERSON>ertCircle, ArrowLeft, Home } from "lucide-react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { Suspense } from "react"

function ErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get("error")

  const getErrorMessage = (errorType: string | null) => {
    switch (errorType) {
      case "Configuration":
        return {
          title: "Erro de Configuração",
          message: "Há um problema na configuração do sistema de autenticação. Entre em contato com o administrador.",
          details: "Erro interno de configuração do NextAuth"
        }
      case "AccessDenied":
        return {
          title: "Acesso Negado",
          message: "Você não tem permissão para acessar este recurso.",
          details: "Suas credenciais não têm o nível de acesso necessário"
        }
      case "Verification":
        return {
          title: "Erro de Verificação",
          message: "Não foi possível verificar sua identidade.",
          details: "Token de verificação inválido ou expirado"
        }
      case "Default":
      default:
        return {
          title: "Erro de Autenticação",
          message: "Ocorreu um erro durante o processo de autenticação. Tente novamente.",
          details: "Erro interno do sistema de autenticação"
        }
    }
  }

  const errorInfo = getErrorMessage(error)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {/* Ícone de erro */}
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-10 w-10 text-red-600" />
          </div>
          
          {/* Título */}
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {errorInfo.title}
          </h2>
          
          {/* Mensagem principal */}
          <p className="mt-2 text-center text-sm text-gray-600">
            {errorInfo.message}
          </p>
          
          {/* Detalhes técnicos */}
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-xs text-red-800">
                <strong>Código do erro:</strong> {error}
              </p>
              <p className="text-xs text-red-600 mt-1">
                {errorInfo.details}
              </p>
            </div>
          )}
        </div>

        {/* Ações */}
        <div className="space-y-4">
          <Link
            href="/auth/login"
            className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar ao Login
          </Link>
          
          <Link
            href="/"
            className="group relative w-full flex justify-center py-3 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <Home className="h-4 w-4 mr-2" />
            Página Inicial
          </Link>
        </div>

        {/* Informações de suporte */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Se o problema persistir, entre em contato com o suporte técnico.
          </p>
        </div>
      </div>
    </div>
  )
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Carregando...</p>
        </div>
      </div>
    }>
      <ErrorContent />
    </Suspense>
  )
}
