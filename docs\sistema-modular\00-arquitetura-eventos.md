# 🔄 ARQUITETURA DE EVENTOS - RLPONTO

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Eliminar dependências circulares entre módulos
- Implementar comunicação assíncrona
- Garantir consistência de dados
- Facilitar manutenção e escalabilidade
- Permitir rollback de operações complexas

### **📊 RESPONSABILIDADES:**

- Event Bus central para comunicação
- Event Sourcing para operações críticas
- Saga Pattern para transações distribuídas
- Dead Letter Queue para eventos falhados
- Replay de eventos para recuperação

---

## 🚀 ARQUITETURA DE EVENTOS

### **1. EVENT BUS CENTRAL:**

```typescript
interface EventBus {
  // Publicar evento
  publish<T>(event: DomainEvent<T>): Promise<void>

  // Subscrever a eventos
  subscribe<T>(eventType: string, handler: EventHandler<T>): void

  // Subscrever com filtros
  subscribeWithFilter<T>(
    eventType: string,
    filter: EventFilter<T>,
    handler: <PERSON><PERSON>andler<T>
  ): void
}

interface DomainEvent<T = any> {
  id: string
  type: string
  aggregateId: string
  aggregateType: string
  version: number
  timestamp: Date
  data: T
  metadata: {
    userId?: string
    correlationId: string
    causationId?: string
  }
}
```

### **2. EVENTOS DO SISTEMA:**

#### **👥 EVENTOS DE FUNCIONÁRIOS:**

```typescript
// Funcionário criado
interface FuncionarioCriadoEvent {
  funcionarioId: string
  empresaId: string
  dadosPessoais: DadosPessoais
  dadosProfissionais: DadosProfissionais
}

// Funcionário atualizado
interface FuncionarioAtualizadoEvent {
  funcionarioId: string
  camposAlterados: string[]
  valoresAnteriores: Record<string, any>
  valoresNovos: Record<string, any>
}

// Funcionário inativado
interface FuncionarioInativadoEvent {
  funcionarioId: string
  empresaId: string
  dataInativacao: Date
  motivo: string
}
```

#### **🏢 EVENTOS DE EMPRESAS:**

```typescript
// Empresa criada
interface EmpresaCriadaEvent {
  empresaId: string
  dadosEmpresa: DadosEmpresa
  configuracoesPadrao: ConfiguracoesEmpresa
}

// Funcionário alocado
interface FuncionarioAlocadoEvent {
  funcionarioId: string
  empresaOrigemId: string
  empresaDestinoId: string
  dataAlocacao: Date
  observacoes?: string
}
```

#### **⏰ EVENTOS DE PONTO:**

```typescript
// Ponto registrado
interface PontoRegistradoEvent {
  registroId: string
  funcionarioId: string
  empresaId: string
  tipoRegistro: TipoRegistro
  dataHora: Date
  origem: 'manual' | 'biometrico' | 'app'
  localizacao?: Coordenadas
}

// Ponto justificado
interface PontoJustificadoEvent {
  registroId: string
  funcionarioId: string
  justificativa: string
  aprovadoPor: string
  dataAprovacao: Date
}
```

### **3. HANDLERS DE EVENTOS:**

#### **📊 HANDLER DE RELATÓRIOS:**

```typescript
@EventHandler('FuncionarioCriado')
class AtualizarEstatisticasHandler {
  async handle(event: FuncionarioCriadoEvent): Promise<void> {
    // Atualizar contadores de funcionários
    await this.estatisticasService.incrementarFuncionarios(event.empresaId)

    // Atualizar dashboard em tempo real
    await this.websocketService.broadcast(`empresa:${event.empresaId}`, {
      type: 'funcionario_adicionado',
      data: event,
    })
  }
}

@EventHandler('FuncionarioInativado')
class LimparDadosHandler {
  async handle(event: FuncionarioInativadoEvent): Promise<void> {
    // Remover templates biométricos
    await this.biometriaService.removerTemplates(event.funcionarioId)

    // Bloquear novos registros de ponto
    await this.pontoService.bloquearRegistros(event.funcionarioId)

    // Arquivar dados pessoais (LGPD)
    await this.lgpdService.arquivarDados(event.funcionarioId)
  }
}
```

### **4. SAGA PATTERN PARA TRANSAÇÕES:**

```typescript
// Saga para transferência de funcionário
class TransferenciaFuncionarioSaga {
  private steps = [
    'validarTransferencia',
    'removerEmpresaOrigem',
    'adicionarEmpresaDestino',
    'atualizarConfiguracoes',
    'notificarPartes',
  ]

  async execute(command: TransferirFuncionarioCommand): Promise<void> {
    const sagaId = uuid()

    try {
      // Passo 1: Validar transferência
      await this.validarTransferencia(command)
      await this.salvarCheckpoint(sagaId, 'validarTransferencia')

      // Passo 2: Remover da empresa origem
      await this.removerEmpresaOrigem(command)
      await this.salvarCheckpoint(sagaId, 'removerEmpresaOrigem')

      // Passo 3: Adicionar na empresa destino
      await this.adicionarEmpresaDestino(command)
      await this.salvarCheckpoint(sagaId, 'adicionarEmpresaDestino')

      // Passo 4: Atualizar configurações
      await this.atualizarConfiguracoes(command)
      await this.salvarCheckpoint(sagaId, 'atualizarConfiguracoes')

      // Passo 5: Notificar partes interessadas
      await this.notificarPartes(command)
      await this.completarSaga(sagaId)
    } catch (error) {
      await this.rollback(sagaId, error)
      throw error
    }
  }

  private async rollback(sagaId: string, error: Error): Promise<void> {
    const checkpoint = await this.obterUltimoCheckpoint(sagaId)

    switch (checkpoint) {
      case 'notificarPartes':
        await this.desfazerNotificacoes(sagaId)
      case 'atualizarConfiguracoes':
        await this.desfazerConfiguracoes(sagaId)
      case 'adicionarEmpresaDestino':
        await this.desfazerAdicaoEmpresa(sagaId)
      case 'removerEmpresaOrigem':
        await this.desfazerRemocaoEmpresa(sagaId)
      case 'validarTransferencia':
        // Nada a desfazer
        break
    }

    await this.marcarSagaComoFalhada(sagaId, error)
  }
}
```

### **5. CONTRATOS ENTRE MÓDULOS:**

```typescript
// Contrato do Módulo de Funcionários
interface FuncionariosModuleContract {
  // Eventos que PUBLICA
  publishes: [
    'FuncionarioCriado',
    'FuncionarioAtualizado',
    'FuncionarioInativado',
    'FuncionarioTransferido',
  ]

  // Eventos que CONSOME
  subscribes: [
    'EmpresaInativada', // Para inativar funcionários
    'LicencaExpirada', // Para bloquear operações
  ]

  // APIs que EXPÕE
  apis: {
    obterFuncionario(id: string): Promise<Funcionario>
    validarCPF(cpf: string): Promise<boolean>
    contarFuncionariosAtivos(empresaId: string): Promise<number>
  }
}

// Contrato do Módulo de Ponto
interface PontoModuleContract {
  publishes: ['PontoRegistrado', 'PontoJustificado', 'IrregularidadeDetectada']

  subscribes: [
    'FuncionarioInativado', // Para bloquear registros
    'JornadaAlterada', // Para recalcular horas
  ]

  apis: {
    registrarPonto(dados: RegistroPonto): Promise<void>
    obterRegistrosDia(funcionarioId: string, data: Date): Promise<Registro[]>
  }
}
```

---

## 🔧 IMPLEMENTAÇÃO TÉCNICA

### **1. EVENT STORE:**

```sql
-- Tabela para armazenar eventos
CREATE TABLE event_store (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  aggregate_id UUID NOT NULL,
  aggregate_type VARCHAR(100) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  event_version INTEGER NOT NULL,
  event_data JSONB NOT NULL,
  metadata JSONB NOT NULL,
  timestamp TIMESTAMP DEFAULT NOW(),

  UNIQUE(aggregate_id, event_version)
);

-- Índices para performance
CREATE INDEX idx_event_store_aggregate ON event_store(aggregate_id, event_version);
CREATE INDEX idx_event_store_type ON event_store(event_type, timestamp);
```

### **2. SAGA STATE:**

```sql
-- Tabela para controlar estado das sagas
CREATE TABLE saga_state (
  saga_id UUID PRIMARY KEY,
  saga_type VARCHAR(100) NOT NULL,
  current_step VARCHAR(100) NOT NULL,
  status VARCHAR(20) NOT NULL, -- 'running', 'completed', 'failed'
  data JSONB NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### **3. DEAD LETTER QUEUE:**

```typescript
// Fila para eventos que falharam
interface DeadLetterQueue {
  addFailedEvent(
    event: DomainEvent,
    error: Error,
    retryCount: number
  ): Promise<void>

  retryFailedEvents(): Promise<void>

  getFailedEvents(limit: number, offset: number): Promise<FailedEvent[]>
}
```

---

## 📊 MONITORAMENTO

### **1. MÉTRICAS DE EVENTOS:**

- Eventos publicados por segundo
- Latência média de processamento
- Taxa de falha por tipo de evento
- Tamanho da fila de eventos

### **2. ALERTAS:**

- Eventos falhando consistentemente
- Sagas travadas por muito tempo
- Dead letter queue crescendo
- Latência acima do limite

### **3. DASHBOARD:**

- Fluxo de eventos em tempo real
- Status das sagas ativas
- Estatísticas de performance
- Logs de eventos críticos

---

## 🔄 ELIMINAÇÃO DE DEPENDÊNCIAS CIRCULARES

### **ANTES (Problemático - Dependências Diretas):**

```mermaid
graph TD
    A[Autenticação] --> B[Empresas]
    A --> C[Funcionários]
    B --> C
    C --> E[Registro de Ponto]
    C --> F[Biométrico]
    E --> G[Relatórios]
    F --> E

    style A fill:#ff9999
    style B fill:#ff9999
    style C fill:#ff9999
    style E fill:#ff9999
    style F fill:#ff9999
    style G fill:#ff9999
```

### **DEPOIS (Correto - Event-Driven):**

```mermaid
graph TD
    EB[Event Bus Central]

    A[Autenticação] -.-> EB
    B[Empresas] -.-> EB
    C[Funcionários] -.-> EB
    E[Registro de Ponto] -.-> EB
    F[Biométrico] -.-> EB
    G[Relatórios] -.-> EB
    H[Licenciamento] -.-> EB
    I[Auditoria] -.-> EB

    EB -.-> A
    EB -.-> B
    EB -.-> C
    EB -.-> E
    EB -.-> F
    EB -.-> G
    EB -.-> H
    EB -.-> I

    style EB fill:#90EE90
    style A fill:#87CEEB
    style B fill:#87CEEB
    style C fill:#87CEEB
    style E fill:#87CEEB
    style F fill:#87CEEB
    style G fill:#87CEEB
    style H fill:#87CEEB
    style I fill:#87CEEB
```

### **CONTRATOS DE EVENTOS POR MÓDULO:**

```typescript
interface ModuleEventContracts {
  // MÓDULO DE FUNCIONÁRIOS
  funcionarios: {
    publishes: [
      'FuncionarioCriado',
      'FuncionarioAtualizado',
      'FuncionarioInativado',
      'TemplateCapturado',
    ]
    subscribes: ['EmpresaInativada', 'LicencaExpirada', 'DispositivoOffline']
  }

  // MÓDULO DE PONTO
  ponto: {
    publishes: ['PontoRegistrado', 'IrregularidadeDetectada', 'JornadaCompleta']
    subscribes: ['FuncionarioInativado', 'JornadaAlterada', 'TurnoAlterado']
  }

  // MÓDULO BIOMÉTRICO
  biometria: {
    publishes: [
      'TemplateCapturado',
      'DispositivoOffline',
      'SincronizacaoCompleta',
    ]
    subscribes: ['FuncionarioCriado', 'FuncionarioInativado']
  }
}
```
