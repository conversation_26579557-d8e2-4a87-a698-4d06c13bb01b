const fs = require('fs')
const path = require('path')

// Função para atualizar imports em um arquivo
function updateImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const updatedContent = content.replace(
      /import\s+{([^}]+)}\s+from\s+["']@\/lib\/auth-simple["']/g,
      'import {$1} from "@/lib/auth-basic"'
    )
    
    if (content !== updatedContent) {
      fs.writeFileSync(filePath, updatedContent, 'utf8')
      console.log(`✅ Updated: ${filePath}`)
      return true
    }
    return false
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message)
    return false
  }
}

// Função para percorrer diretórios recursivamente
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // Pular node_modules e .next
      if (!['node_modules', '.next', '.git'].includes(file)) {
        walkDir(filePath, callback)
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      callback(filePath)
    }
  })
}

console.log('🔄 Atualizando imports de @/lib/auth-simple para @/lib/auth-basic...')

let updatedCount = 0

// Percorrer todos os arquivos TypeScript
walkDir('./src', (filePath) => {
  if (updateImportsInFile(filePath)) {
    updatedCount++
  }
})

console.log(`\n✅ Processo concluído! ${updatedCount} arquivos atualizados.`)

// Lista de arquivos que devem ser verificados manualmente
const filesToCheck = [
  './src/app/funcionarios/page.tsx',
  './src/app/ponto/page.tsx',
  './src/app/ponto/espelho/page.tsx',
  './src/app/api/empresas/principal/route.ts',
  './src/app/api/empresas/dashboard/stats/route.ts',
  './src/app/api/empresas/dashboard/projetos-recentes/route.ts',
  './src/app/api/empresas/[id]/configuracoes/route.ts',
  './src/app/api/empresas/[id]/route.ts',
  './src/app/api/empresas/[id]/funcionarios/route.ts',
  './src/app/api/ponto/registrar/route.ts'
]

console.log('\n📋 Verificando arquivos específicos...')
filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    updateImportsInFile(file)
  }
})
