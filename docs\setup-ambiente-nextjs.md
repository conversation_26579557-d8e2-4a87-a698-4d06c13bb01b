# 🚀 G<PERSON>a <PERSON>to: Setup de Ambiente Next.js Profissional

> **Documentação técnica para configuração de ambiente de desenvolvimento Next.js moderno, escalável e otimizado para produtividade máxima.**

## 📋 Índice

- [Pré-requisitos](#-pré-requisitos)
- [Configuração Inicial](#-configuração-inicial)
- [Estrutura do Projeto](#-estrutura-do-projeto)
- [Ferramentas de Qualidade](#-ferramentas-de-qualidade)
- [Bibliotecas Essenciais](#-bibliotecas-essenciais)
- [Configuração do VS Code](#-configuração-do-vs-code)
- [Variáveis de Ambiente](#-variáveis-de-ambiente)
- [Scripts e Comandos](#-scripts-e-comandos)
- [Verificação Final](#-verificação-final)

---

## 🔧 Pré-requisitos

Certifique-se de ter instalado:

- **Node.js** (versão 18.17 ou superior)
- **npm** (versão 9 ou superior) ou **pnpm** (recomendado)
- **VS Code** com extensões essenciais

```bash
# Verificar versões
node --version
npm --version
```

---

## 🏗️ Configuração Inicial

### 1. Criar Projeto Next.js com TypeScript

```bash
# Usando npx (recomendado)
npx create-next-app@latest meu-projeto --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Ou usando pnpm (mais rápido)
pnpm create next-app meu-projeto --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

cd meu-projeto
```

### 2. Configurar Package Manager (Opcional - pnpm)

```bash
# Se escolher usar pnpm
npm install -g pnpm
echo "node-linker=hoisted" > .npmrc
```

---

## 📁 Estrutura do Projeto

### Organização de Diretórios

```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/             # Componentes reutilizáveis
│   ├── ui/                # Componentes base (Button, Input, etc.)
│   ├── layout/            # Componentes de layout
│   └── common/            # Componentes comuns
├── features/              # Funcionalidades por domínio
│   └── [feature-name]/
│       ├── components/
│       ├── hooks/
│       ├── services/
│       └── types/
├── hooks/                 # Custom hooks globais
├── lib/                   # Configurações e utilitários
│   ├── utils.ts
│   ├── validations.ts
│   └── constants.ts
├── services/              # APIs e serviços externos
├── stores/                # Gerenciamento de estado
├── styles/                # Estilos globais
└── types/                 # Definições TypeScript globais
```

### Criar Estrutura Automaticamente

```bash
# Criar diretórios
mkdir -p src/{components/{ui,layout,common},features,hooks,lib,services,stores,styles,types}

# Criar arquivos base
touch src/lib/{utils.ts,validations.ts,constants.ts}
touch src/types/index.ts
touch src/styles/globals.css
```

---

## 🛠️ Ferramentas de Qualidade

### 1. Configurar ESLint Avançado

```bash
npm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser eslint-config-next
```

**`.eslintrc.json`:**

```json
{
  "extends": ["next/core-web-vitals", "@typescript-eslint/recommended"],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### 2. Configurar Prettier

```bash
npm install -D prettier eslint-config-prettier eslint-plugin-prettier
```

**`.prettierrc`:**

```json
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

**`.prettierignore`:**

```
.next
node_modules
*.log
.env*
```

### 3. Configurar TypeScript Estrito

**`tsconfig.json` (melhorado):**

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

---

## 📦 Bibliotecas Essenciais

### 1. Instalar Dependências de Produção

```bash
# Gerenciamento de estado e dados
npm install zustand @tanstack/react-query axios

# UI e estilização
npm install clsx tailwind-merge class-variance-authority
npm install @headlessui/react @heroicons/react
npm install framer-motion

# Formulários e validação
npm install react-hook-form @hookform/resolvers zod

# Utilitários
npm install date-fns lodash-es
npm install @types/lodash-es -D
```

### 2. Configurar Bibliotecas Base

**`src/lib/utils.ts`:**

```typescript
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('pt-BR').format(date)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
```

**`src/lib/validations.ts`:**

```typescript
import { z } from 'zod'

export const emailSchema = z.string().email('Email inválido')

export const passwordSchema = z
  .string()
  .min(8, 'Senha deve ter pelo menos 8 caracteres')
  .regex(/[A-Z]/, 'Senha deve conter pelo menos uma letra maiúscula')
  .regex(/[0-9]/, 'Senha deve conter pelo menos um número')

export const userSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: emailSchema,
  password: passwordSchema,
})

export type User = z.infer<typeof userSchema>
```

### 3. Configurar TailwindCSS Avançado

**`tailwind.config.ts`:**

```typescript
import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: ['var(--font-sans)', 'system-ui', 'sans-serif'],
        mono: ['var(--font-mono)', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}

export default config
```

**Instalar plugins do Tailwind:**

```bash
npm install -D @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio
```

---

## ⚙️ Configuração do VS Code

### 1. Configurações do Workspace

**`.vscode/settings.json`:**

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "editor.quickSuggestions": {
    "strings": true
  },
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false
}
```

### 2. Extensões Recomendadas

**`.vscode/extensions.json`:**

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "formulahendry.auto-close-tag",
    "wix.vscode-import-cost",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 3. Snippets Personalizados

**`.vscode/snippets.code-snippets`:**

```json
{
  "React Functional Component": {
    "prefix": "rfc",
    "body": [
      "interface ${1:ComponentName}Props {",
      "  $2",
      "}",
      "",
      "export default function ${1:ComponentName}({ $3 }: ${1:ComponentName}Props) {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  )",
      "}"
    ],
    "description": "Create a React functional component with TypeScript"
  },
  "Custom Hook": {
    "prefix": "hook",
    "body": [
      "import { useState, useEffect } from 'react'",
      "",
      "export function use${1:HookName}() {",
      "  const [${2:state}, set${2/(.*)/${1:/capitalize}/}] = useState($3)",
      "",
      "  useEffect(() => {",
      "    $4",
      "  }, [])",
      "",
      "  return {",
      "    ${2:state},",
      "    set${2/(.*)/${1:/capitalize}/}",
      "  }",
      "}"
    ],
    "description": "Create a custom React hook"
  }
}
```

---

## 🌍 Variáveis de Ambiente

### 1. Configurar Arquivos de Ambiente

**`.env.local`:**

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://***********/api
NEXT_PUBLIC_APP_URL=http://***********

# Database (se necessário)
DATABASE_URL="postgresql://user:password@localhost:5432/mydb"

# Authentication (se necessário)
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://***********

# External Services
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NEXT_PUBLIC_SENTRY_DSN=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_DEBUG=true
```

### 2. Validação de Variáveis de Ambiente

**`src/lib/env.ts`:**

```typescript
import { z } from 'zod'

const envSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().url(),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NODE_ENV: z.enum(['development', 'production', 'test']),
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(val => val === 'true'),
  NEXT_PUBLIC_ENABLE_DEBUG: z.string().transform(val => val === 'true'),
})

export const env = envSchema.parse(process.env)
```

---

## 📜 Scripts e Comandos

### 1. Scripts do Package.json

**Adicionar ao `package.json`:**

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "type-check": "tsc --noEmit",
    "clean": "rm -rf .next out",
    "analyze": "cross-env ANALYZE=true next build",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

### 2. Comandos Úteis para Desenvolvimento

```bash
# Desenvolvimento
npm run dev              # Iniciar servidor de desenvolvimento
npm run build           # Build para produção
npm run start           # Iniciar servidor de produção

# Qualidade de código
npm run lint            # Verificar problemas de lint
npm run lint:fix        # Corrigir problemas automaticamente
npm run format          # Formatar código
npm run type-check      # Verificar tipos TypeScript

# Utilitários
npm run clean           # Limpar cache do Next.js
npm run analyze         # Analisar bundle size
```

---

## ✅ Verificação Final

### 1. Checklist de Configuração

- [ ] Projeto Next.js criado com TypeScript
- [ ] Estrutura de pastas organizada
- [ ] ESLint e Prettier configurados
- [ ] TailwindCSS instalado e configurado
- [ ] Bibliotecas essenciais instaladas
- [ ] VS Code configurado com extensões
- [ ] Variáveis de ambiente configuradas
- [ ] Scripts do package.json atualizados

### 2. Teste de Funcionamento

```bash
# 1. Verificar se não há erros de lint
npm run lint

# 2. Verificar se não há erros de tipo
npm run type-check

# 3. Verificar se o build funciona
npm run build

# 4. Iniciar o projeto
npm run dev
```

### 3. Verificar no Navegador

Acesse `http://***********` e confirme:

- [ ] Página carrega sem erros
- [ ] TailwindCSS está funcionando
- [ ] Hot reload está ativo
- [ ] Console sem erros

---

## 🎯 Próximos Passos

Após a configuração, você pode:

1. **Criar componentes base** na pasta `src/components/ui/`
2. **Configurar autenticação** (se necessário)
3. **Implementar roteamento** usando App Router
4. **Configurar banco de dados** (se necessário)
5. **Adicionar testes** com Jest e Testing Library

---

## 📚 Recursos Adicionais

### Documentação Oficial

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)

### Ferramentas Recomendadas

- [Shadcn/ui](https://ui.shadcn.com/) - Componentes UI
- [React Hook Form](https://react-hook-form.com/) - Formulários
- [Zustand](https://zustand-demo.pmnd.rs/) - Estado global
- [TanStack Query](https://tanstack.com/query) - Data fetching

---

**✨ Ambiente configurado com sucesso! Pronto para desenvolvimento profissional com Next.js.**
