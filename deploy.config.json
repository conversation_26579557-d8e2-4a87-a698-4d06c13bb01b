{"server": {"host": "***********", "user": "root", "alias": "rlp-server", "deployPath": "/var/www/rlp-v2", "port": 3000}, "app": {"name": "rlp-v2", "script": "npm", "args": "start", "instances": 1, "maxMemoryRestart": "1G", "env": {"NODE_ENV": "production", "PORT": 3000}}, "deploy": {"files": [".next", "public", "package.json", "package-lock.json", "next.config.ts"], "exclude": ["node_modules", ".git", "src", "docs", "*.md", "*.log"]}}