"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Building2, 
  Users, 
  TrendingUp, 
  Calendar, 
  DollarSign, 
  Activity,
  ArrowUp,
  ArrowDown,
  BarChart3,
  PieChart
} from "lucide-react"

interface DashboardStats {
  empresas: {
    total: number
    ativas: number
    clientes: number
    crescimento_mensal: number
  }
  funcionarios: {
    total: number
    alocados: number
    taxa_alocacao: number
    crescimento_mensal: number
  }
  projetos: {
    ativos: number
    finalizados_mes: number
    valor_total_hora: number
    crescimento_valor: number
  }
  atividade_recente: {
    novas_alocacoes: number
    funcionarios_admitidos: number
    projetos_iniciados: number
  }
}

interface ProjetoRecente {
  id: string
  nomeFantasia: string
  nomeProjeto?: string
  dataInicio: string
  funcionarios_alocados: number
  valor_total_hora: number
  status: string
}

export default function DashboardEmpresasPage() {
  const { data: session, status } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [projetosRecentes, setProjetosRecentes] = useState<ProjetoRecente[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  if (status === "loading") {
    return <div className="flex items-center justify-center min-h-screen">Carregando...</div>
  }

  if (!session?.user) {
    redirect("/auth/login")
  }

  // Buscar dados do dashboard
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)

        // Buscar estatísticas
        const statsResponse = await fetch('/api/empresas/dashboard/stats')
        if (!statsResponse.ok) {
          throw new Error('Erro ao buscar estatísticas')
        }
        const statsData = await statsResponse.json()
        setStats(statsData)

        // Buscar projetos recentes
        const projetosResponse = await fetch('/api/empresas/dashboard/projetos-recentes')
        if (!projetosResponse.ok) {
          throw new Error('Erro ao buscar projetos recentes')
        }
        const projetosData = await projetosResponse.json()
        setProjetosRecentes(projetosData.projetos)

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Tentar novamente</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard - Empresas</h1>
              <p className="mt-1 text-sm text-gray-500">
                Visão geral das empresas e projetos
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline">
                <BarChart3 className="h-4 w-4 mr-2" />
                Relatórios
              </Button>
              <Button>
                <Building2 className="h-4 w-4 mr-2" />
                Nova Empresa
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Estatísticas Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total de Empresas */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Empresas</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.empresas.total || 0}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center">
                  <ArrowUp className="h-3 w-3 mr-1" />
                  {stats?.empresas.crescimento_mensal || 0}%
                </span>
                <span className="ml-1">vs mês anterior</span>
              </div>
            </CardContent>
          </Card>

          {/* Funcionários */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Funcionários</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.funcionarios.total || 0}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className="text-blue-600">
                  {stats?.funcionarios.alocados || 0} alocados
                </span>
                <span className="ml-1">
                  ({stats?.funcionarios.taxa_alocacao || 0}%)
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Projetos Ativos */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Projetos Ativos</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.projetos.ativos || 0}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className="text-green-600">
                  {stats?.projetos.finalizados_mes || 0} finalizados
                </span>
                <span className="ml-1">este mês</span>
              </div>
            </CardContent>
          </Card>

          {/* Valor Total/Hora */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valor Total/Hora</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                R$ {stats?.projetos.valor_total_hora.toFixed(2) || '0.00'}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className={`flex items-center ${
                  (stats?.projetos.crescimento_valor || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {(stats?.projetos.crescimento_valor || 0) >= 0 ? (
                    <ArrowUp className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(stats?.projetos.crescimento_valor || 0)}%
                </span>
                <span className="ml-1">vs mês anterior</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Atividade Recente e Projetos */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Atividade Recente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Atividade Recente (30 dias)
              </CardTitle>
              <CardDescription>
                Resumo das atividades dos últimos 30 dias
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Novas Alocações</span>
                <Badge variant="default">
                  {stats?.atividade_recente.novas_alocacoes || 0}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Funcionários Admitidos</span>
                <Badge variant="secondary">
                  {stats?.atividade_recente.funcionarios_admitidos || 0}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Projetos Iniciados</span>
                <Badge variant="outline">
                  {stats?.atividade_recente.projetos_iniciados || 0}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Projetos Recentes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Projetos Recentes
              </CardTitle>
              <CardDescription>
                Últimos projetos criados ou atualizados
              </CardDescription>
            </CardHeader>
            <CardContent>
              {projetosRecentes.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  Nenhum projeto recente encontrado
                </p>
              ) : (
                <div className="space-y-3">
                  {projetosRecentes.slice(0, 5).map((projeto) => (
                    <div key={projeto.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">
                          {projeto.nomeProjeto || projeto.nomeFantasia}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(projeto.dataInicio).toLocaleDateString('pt-BR')} • 
                          {projeto.funcionarios_alocados} funcionários
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          R$ {projeto.valor_total_hora.toFixed(2)}/h
                        </p>
                        <Badge 
                          variant={projeto.status === 'ativo' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {projeto.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Ações Rápidas */}
        <Card>
          <CardHeader>
            <CardTitle>Ações Rápidas</CardTitle>
            <CardDescription>
              Acesso rápido às principais funcionalidades
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex-col">
                <Building2 className="h-6 w-6 mb-2" />
                <span className="text-sm">Nova Empresa</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Users className="h-6 w-6 mb-2" />
                <span className="text-sm">Alocar Funcionário</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <BarChart3 className="h-6 w-6 mb-2" />
                <span className="text-sm">Relatórios</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <PieChart className="h-6 w-6 mb-2" />
                <span className="text-sm">Análises</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
