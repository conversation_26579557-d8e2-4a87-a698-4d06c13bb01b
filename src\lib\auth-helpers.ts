import { NextRequest } from "next/server"

// Função para verificar autenticação básica em APIs
export function checkApiAuth(request: NextRequest) {
  // Verificar se tem token de sessão
  const sessionToken = request.cookies.get("next-auth.session-token") || 
                      request.cookies.get("__Secure-next-auth.session-token")
  
  if (!sessionToken) {
    return null
  }

  // Para autenticação básica, retornar usuário mock
  // Em produção, isso seria decodificado do token JWT
  return {
    id: 'api-user',
    email: '<EMAIL>',
    name: 'API User',
    nivelAcesso: 'admin_total',
    ativo: true
  }
}

// Função para verificar permissões
export function hasPermission(userLevel: string, requiredLevel: string): boolean {
  const levels = [
    'readonly',
    'operador_biometria', 
    'cliente_vinculado',
    'supervisor_obra',
    'gerente_rh',
    'admin_limitado',
    'admin_total'
  ]
  
  const userIndex = levels.indexOf(userLevel)
  const requiredIndex = levels.indexOf(requiredLevel)
  
  return userIndex >= requiredIndex
}

// Função para verificar se usuário está ativo
export function isUserActive(user: { ativo?: boolean } | null | undefined): boolean {
  return user?.ativo === true
}

// Middleware de autenticação para API routes
export function requireApiAuth(request: NextRequest) {
  const user = checkApiAuth(request)
  
  if (!user) {
    throw new Error("Não autenticado")
  }
  
  if (!isUserActive(user)) {
    throw new Error("Usuário inativo")
  }
  
  return user
}

// Middleware de autorização para API routes
export function requireApiPermission(request: NextRequest, requiredLevel: string) {
  const user = requireApiAuth(request)
  
  if (!hasPermission(user.nivelAcesso, requiredLevel)) {
    throw new Error("Permissão insuficiente")
  }
  
  return user
}
