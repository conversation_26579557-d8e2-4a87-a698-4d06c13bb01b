import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para atualização de funcionário
const updateFuncionarioSchema = z.object({
  nomeCompleto: z.string().min(2).optional(),
  cpf: z.string().regex(/^\d{11}$/).optional(),
  rg: z.string().optional(),
  dataNascimento: z.string().datetime().optional(),
  sexo: z.enum(['M', 'F']).optional(),
  estadoCivil: z.string().optional(),
  telefone: z.string().optional(),
  email: z.string().email().optional(),
  endereco: z.string().optional(),
  matricula: z.string().min(1).optional(),
  cargo: z.string().min(1).optional(),
  setor: z.string().optional(),
  dataAdmissao: z.string().datetime().optional(),
  dataDemissao: z.string().datetime().optional(),
  salario: z.number().positive().optional(),
  ativo: z.boolean().optional()
})

// GET /api/funcionarios/[id] - Obter funcionário específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const funcionario = await prisma.funcionario.findUnique({
      where: { id: params.id },
      include: {
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            razaoSocial: true,
            cnpj: true,
            tipoEmpresa: true
          }
        },
        registrosPonto: {
          take: 10,
          orderBy: { dataHora: 'desc' },
          select: {
            id: true,
            dataHora: true,
            tipo: true,
            localizacao: true,
            observacoes: true
          }
        },
        funcionarioEpis: {
          where: { dataDevolucao: null },
          include: {
            epi: {
              select: {
                id: true,
                nome: true,
                categoria: true,
                numeroCa: true,
                validadeCa: true
              }
            }
          }
        },
        alocacoes: {
          where: { status: 'ativo' },
          include: {
            empresa: {
              select: {
                id: true,
                nomeFantasia: true
              }
            }
          }
        },
        _count: {
          select: {
            registrosPonto: true,
            funcionarioEpis: true,
            alocacoes: true
          }
        }
      }
    })

    if (!funcionario) {
      return NextResponse.json(
        { error: "Funcionário não encontrado" },
        { status: 404 }
      )
    }

    return NextResponse.json(funcionario)

  } catch (error) {
    console.error('Erro ao buscar funcionário:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// PUT /api/funcionarios/[id] - Atualizar funcionário
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Verificar se funcionário existe
    const funcionarioExistente = await prisma.funcionario.findUnique({
      where: { id: params.id }
    })

    if (!funcionarioExistente) {
      return NextResponse.json(
        { error: "Funcionário não encontrado" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = updateFuncionarioSchema.parse(body)

    // Se CPF está sendo alterado, verificar se já existe
    if (data.cpf && data.cpf !== funcionarioExistente.cpf) {
      const cpfExistente = await prisma.funcionario.findUnique({
        where: { cpf: data.cpf }
      })

      if (cpfExistente) {
        return NextResponse.json(
          { error: "CPF já cadastrado" },
          { status: 409 }
        )
      }
    }

    // Se matrícula está sendo alterada, verificar se já existe na empresa
    if (data.matricula && data.matricula !== funcionarioExistente.matricula) {
      const matriculaExistente = await prisma.funcionario.findFirst({
        where: {
          matricula: data.matricula,
          empresaId: funcionarioExistente.empresaId,
          id: { not: params.id }
        }
      })

      if (matriculaExistente) {
        return NextResponse.json(
          { error: "Matrícula já existe nesta empresa" },
          { status: 409 }
        )
      }
    }

    // Preparar dados para atualização
    const updateData: any = { ...data }
    
    if (data.dataNascimento) {
      updateData.dataNascimento = new Date(data.dataNascimento)
    }
    
    if (data.dataAdmissao) {
      updateData.dataAdmissao = new Date(data.dataAdmissao)
    }
    
    if (data.dataDemissao) {
      updateData.dataDemissao = new Date(data.dataDemissao)
    }

    // Atualizar funcionário
    const funcionario = await prisma.funcionario.update({
      where: { id: params.id },
      data: updateData,
      include: {
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            tipoEmpresa: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "UPDATE_FUNCIONARIO",
        tabelaAfetada: "funcionarios",
        registroId: funcionario.id,
        dadosAntigos: funcionarioExistente,
        dadosNovos: funcionario,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(funcionario)

  } catch (error) {
    console.error('Erro ao atualizar funcionário:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// DELETE /api/funcionarios/[id] - Inativar funcionário
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Verificar se funcionário existe
    const funcionario = await prisma.funcionario.findUnique({
      where: { id: params.id }
    })

    if (!funcionario) {
      return NextResponse.json(
        { error: "Funcionário não encontrado" },
        { status: 404 }
      )
    }

    // Inativar funcionário (soft delete)
    const funcionarioInativado = await prisma.funcionario.update({
      where: { id: params.id },
      data: { 
        ativo: false,
        dataDemissao: new Date()
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "DELETE_FUNCIONARIO",
        tabelaAfetada: "funcionarios",
        registroId: funcionario.id,
        dadosAntigos: funcionario,
        dadosNovos: funcionarioInativado,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({ 
      message: "Funcionário inativado com sucesso",
      funcionario: funcionarioInativado
    })

  } catch (error) {
    console.error('Erro ao inativar funcionário:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
