# 🚀 Projeto Next.js Profissional

Ambiente Next.js completo configurado seguindo as melhores práticas de desenvolvimento moderno.

## ✨ Características

- **Next.js 15** com App Router
- **TypeScript** com configuração estrita
- **TailwindCSS** com sistema de design personalizado
- **ESLint + Prettier** para qualidade de código
- **Estrutura de pastas** organizada e escalável
- **Componentes UI** com class-variance-authority
- **Bibliotecas essenciais** pré-configuradas

## 📦 Tecnologias Incluídas

### Core

- Next.js 15.4.4
- React 19.1.0
- TypeScript 5+

### Estilização

- TailwindCSS 4
- class-variance-authority
- clsx + tailwind-merge

### Formulários e Validação

- React Hook Form
- Zod

### Estado e Dados

- Zustand
- TanStack Query
- Axios

### UI e Animações

- Headless UI
- Heroicons
- Framer Motion

### Utilitários

- date-fns
- lodash-es

## 🏗️ Estrutura do Projeto

```
src/
├── app/                    # App Router (Next.js 13+)
├── components/             # Componentes reutilizáveis
│   ├── ui/                # Componentes base
│   ├── layout/            # Componentes de layout
│   └── common/            # Componentes comuns
├── features/              # Funcionalidades por domínio
├── hooks/                 # Custom hooks globais
├── lib/                   # Configurações e utilitários
├── services/              # APIs e serviços externos
├── stores/                # Gerenciamento de estado
├── styles/                # Estilos globais
└── types/                 # Definições TypeScript globais
```

## 🚀 Comandos Disponíveis

```bash
# Desenvolvimento
npm run dev              # Iniciar servidor de desenvolvimento
npm run build           # Build para produção
npm run start           # Iniciar servidor de produção

# Qualidade de código
npm run lint            # Verificar problemas de lint
npm run lint:fix        # Corrigir problemas automaticamente
npm run format          # Formatar código
npm run type-check      # Verificar tipos TypeScript

# Utilitários
npm run clean           # Limpar cache do Next.js
npm run analyze         # Analisar bundle size
```

## 🛠️ Configurações

### ESLint

- Configuração estrita com TypeScript
- Regras personalizadas para qualidade de código
- Integração com Prettier

### Prettier

- Formatação automática
- Configuração otimizada para React/TypeScript

### TailwindCSS

- Sistema de design com variáveis CSS
- Plugins para forms, typography e aspect-ratio
- Animações personalizadas

### TypeScript

- Configuração estrita
- Paths absolutos configurados
- Tipos globais organizados

## 🌍 Variáveis de Ambiente

Copie `.env.local` e configure as variáveis necessárias:

```env
NEXT_PUBLIC_API_URL=http://***********/api
NEXT_PUBLIC_APP_URL=http://***********
NEXT_PUBLIC_ENABLE_DEBUG=true
```

## 🎯 Próximos Passos

1. Configurar autenticação (se necessário)
2. Implementar roteamento específico
3. Configurar banco de dados
4. Adicionar testes com Jest
5. Configurar CI/CD

## 📚 Documentação

- [Next.js Documentation](https://nextjs.org/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

---

**✨ Ambiente configurado com sucesso! Pronto para desenvolvimento profissional.**
