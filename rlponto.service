[Unit]
Description=RLPONTO - Sistema de Controle de Ponto
Documentation=https://github.com/rlponto/docs
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service
Requires=network.target

[Service]
Type=forking
User=rlponto
Group=rlponto
WorkingDirectory=/opt/rlponto
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=DATABASE_URL=postgresql://rlponto_user:rlponto123@localhost:5432/rlponto_db
Environment=AUTH_URL=http://***********
Environment=AUTH_SECRET=PxxYju+eBVgr2sOTXL9zBPmBSkH7lVYG2e9+WxnLbUg=
Environment=REDIS_URL=redis://localhost:6379/0
Environment=LOG_LEVEL=info
Environment=UPLOAD_MAX_SIZE=10485760
Environment=UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf
Environment=SYSTEM_NAME=RLPONTO
Environment=SYSTEM_VERSION=1.0.0

# PM2 como gerenciador de processos
ExecStart=/usr/bin/pm2 start /opt/rlponto/ecosystem.config.js --no-daemon
ExecReload=/usr/bin/pm2 reload /opt/rlponto/ecosystem.config.js
ExecStop=/usr/bin/pm2 stop /opt/rlponto/ecosystem.config.js

# Restart automático
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Segurança
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/rlponto/logs /opt/rlponto/uploads /opt/rlponto/temp
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Recursos
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Logs
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rlponto

# Timeout
TimeoutStartSec=60
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
