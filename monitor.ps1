# Script de Monitoramento RLP-V2
Write-Host "=== Monitor RLP-V2 ===" -ForegroundColor Green

# Status do PM2
Write-Host "`nStatus do PM2:" -ForegroundColor Yellow
ssh rlp-server "pm2 status"

# Logs da aplicacao
Write-Host "`nUltimos logs:" -ForegroundColor Yellow
ssh rlp-server "pm2 logs rlp-v2 --lines 10"

# Status HTTP
Write-Host "`nTeste HTTP:" -ForegroundColor Yellow
$response = ssh rlp-server "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000"
if ($response -eq "200") {
    Write-Host "OK - Aplicacao respondendo (HTTP $response)" -ForegroundColor Green
} else {
    Write-Host "ERRO - Aplicacao com problema (HTTP $response)" -ForegroundColor Red
}

# Uso de memoria
Write-Host "`nUso de recursos:" -ForegroundColor Yellow
ssh rlp-server "pm2 monit --no-daemon | head -20"

Write-Host "`nAcesse: http://***********:3000" -ForegroundColor Cyan
