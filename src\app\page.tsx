import Link from "next/link"

export default function HomePage() {

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto px-4">
        {/* Logo/Ícone */}
        <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-blue-100 mb-6">
          <svg className="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>

        {/* Título */}
        <h1 className="text-4xl font-bold text-gray-900 mb-4">RLPONTO</h1>
        <p className="text-gray-600 mb-8">Sistema de Controle de Ponto Eletrônico</p>

        {/* Descrição */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Bem-vindo!</h2>
          <p className="text-sm text-gray-600 mb-4">
            Acesse o sistema para gerenciar registros de ponto, funcionários e relatórios.
          </p>
          <div className="text-xs text-gray-500">
            <p>✓ Controle de ponto em tempo real</p>
            <p>✓ Gestão de funcionários e empresas</p>
            <p>✓ Relatórios detalhados</p>
          </div>
        </div>

        {/* Botões */}
        <div className="space-y-4">
          <Link
            href="/auth/login"
            className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Fazer Login
          </Link>
          <Link
            href="/auth/login"
            className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Acessar Sistema
          </Link>
        </div>

        {/* Footer */}
        <div className="mt-8 text-xs text-gray-500">
          <p>RLPONTO v1.0.0 - Sistema de Controle de Ponto</p>
        </div>
      </div>
    </div>
  )
}
