import NextAuth from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"

export const { handlers, auth, signIn, signOut } = NextAuth({
  secret: process.env.AUTH_SECRET || "PxxYju+eBVgr2sOTXL9zBPmBSkH7lVYG2e9+WxnLbUg=",
  trustHost: true,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>"
        },
        password: {
          label: "Senha",
          type: "password"
        }
      },
      async authorize(credentials) {
        try {
          console.log("🔄 Tentativa de login básico")

          if (!credentials?.email || !credentials?.password) {
            console.log("❌ Credenciais não fornecidas")
            return null
          }

          const { email, password } = credentials as { email: string, password: string }

          // Autenticação básica para teste
          if (email === '<EMAIL>' && password === 'admin123') {
            console.log("✅ Login admin bem-sucedido")
            return {
              id: 'admin-001',
              email: '<EMAIL>',
              name: '<PERSON><PERSON><PERSON><PERSON>',
              nivelAcesso: 'admin_total',
              ativo: true
            }
          }

          if (email === '<EMAIL>' && password === 'teste123') {
            console.log("✅ Login teste bem-sucedido")
            return {
              id: 'teste-001',
              email: '<EMAIL>',
              name: 'Usuário Teste',
              nivelAcesso: 'gerente_rh',
              ativo: true
            }
          }

          console.log("❌ Credenciais inválidas:", email)
          return null

        } catch (error) {
          console.error("❌ Erro na autenticação básica:", error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 horas
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.nivelAcesso = (user as any).nivelAcesso
        token.ativo = (user as any).ativo
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as any).id = token.id as string
        ;(session.user as any).nivelAcesso = token.nivelAcesso as string
        ;(session.user as any).ativo = token.ativo as boolean
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  debug: process.env.NODE_ENV === 'development',
  secret: process.env.AUTH_SECRET,
  trustHost: true
})

// Tipos para TypeScript
declare module "next-auth" {
  interface User {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name: string
      nivelAcesso: string
      ativo: boolean
    }
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
}

// Função helper para verificar permissões
export function hasPermission(userLevel: string, requiredLevel: string): boolean {
  const levels = [
    'readonly',
    'operador_biometria', 
    'cliente_vinculado',
    'supervisor_obra',
    'gerente_rh',
    'admin_limitado',
    'admin_total'
  ]
  
  const userIndex = levels.indexOf(userLevel)
  const requiredIndex = levels.indexOf(requiredLevel)
  
  return userIndex >= requiredIndex
}

// Função helper para verificar se usuário está ativo
export function isUserActive(user: { ativo?: boolean } | null | undefined): boolean {
  return user?.ativo === true
}

// Middleware de autenticação para API routes
export async function requireAuth(_req: Request) {
  const session = await auth()
  
  if (!session?.user) {
    throw new Error("Não autenticado")
  }
  
  if (!isUserActive(session.user)) {
    throw new Error("Usuário inativo")
  }
  
  return session.user
}

// Middleware de autorização para API routes
export async function requirePermission(_req: Request, requiredLevel: string) {
  const user = await requireAuth(_req)
  
  if (!hasPermission(user.nivelAcesso, requiredLevel)) {
    throw new Error("Permissão insuficiente")
  }
  
  return user
}
