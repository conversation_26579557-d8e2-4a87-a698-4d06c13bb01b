import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para query parameters
const querySchema = z.object({
  search: z.string().optional(),
  empresa_id: z.string().uuid().optional(),
  cargo: z.string().optional(),
  status: z.enum(['ativo', 'inativo', 'todos']).default('ativo'),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20)
})

// Schema para criação de funcionário
const funcionarioSchema = z.object({
  empresaId: z.string().uuid("ID da empresa inválido"),
  nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  cpf: z.string().regex(/^\d{11}$/, "CPF deve ter 11 dígitos"),
  rg: z.string().optional(),
  dataNascimento: z.string().datetime().optional(),
  sexo: z.enum(['M', 'F']).optional(),
  estadoCivil: z.string().optional(),
  telefone: z.string().optional(),
  email: z.string().email().optional(),
  endereco: z.string().optional(),
  matricula: z.string().min(1, "Matrícula é obrigatória"),
  cargo: z.string().min(1, "Cargo é obrigatório"),
  setor: z.string().optional(),
  dataAdmissao: z.string().datetime("Data de admissão inválida"),
  salario: z.number().positive().optional()
})

// GET /api/funcionarios - Listar funcionários
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const query = querySchema.parse({
      search: searchParams.get('search'),
      empresa_id: searchParams.get('empresa_id'),
      cargo: searchParams.get('cargo'),
      status: searchParams.get('status'),
      page: searchParams.get('page'),
      limit: searchParams.get('limit')
    })

    // Construir filtros
    const where: any = {}

    // Filtro por status
    if (query.status !== 'todos') {
      where.ativo = query.status === 'ativo'
    }

    // Filtro por empresa
    if (query.empresa_id) {
      where.empresaId = query.empresa_id
    }

    // Filtro por cargo
    if (query.cargo) {
      where.cargo = { contains: query.cargo, mode: 'insensitive' }
    }

    // Busca geral
    if (query.search) {
      const searchTerm = query.search.replace(/\D/g, '') // Remove caracteres não numéricos para CPF
      where.OR = [
        { nomeCompleto: { contains: query.search, mode: 'insensitive' } },
        { cpf: { contains: searchTerm, mode: 'insensitive' } },
        { matricula: { contains: query.search, mode: 'insensitive' } },
        { email: { contains: query.search, mode: 'insensitive' } }
      ]
    }

    // Calcular offset para paginação
    const offset = (query.page - 1) * query.limit

    // Buscar funcionários
    const [funcionarios, total] = await Promise.all([
      prisma.funcionario.findMany({
        where,
        include: {
          empresa: {
            select: {
              id: true,
              nomeFantasia: true,
              tipoEmpresa: true
            }
          },
          _count: {
            select: {
              registrosPonto: true,
              funcionarioEpis: true,
              alocacoes: true
            }
          }
        },
        orderBy: [
          { ativo: 'desc' },
          { nomeCompleto: 'asc' }
        ],
        skip: offset,
        take: query.limit
      }),
      prisma.funcionario.count({ where })
    ])

    const totalPages = Math.ceil(total / query.limit)

    return NextResponse.json({
      funcionarios,
      total,
      page: query.page,
      totalPages,
      limit: query.limit
    })

  } catch (error) {
    console.error('Erro ao buscar funcionários:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Parâmetros inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// POST /api/funcionarios - Criar funcionário
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const data = funcionarioSchema.parse(body)

    // Verificar se empresa existe
    const empresa = await prisma.empresa.findUnique({
      where: { id: data.empresaId }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se CPF já existe
    const cpfExistente = await prisma.funcionario.findUnique({
      where: { cpf: data.cpf }
    })

    if (cpfExistente) {
      return NextResponse.json(
        { error: "CPF já cadastrado" },
        { status: 409 }
      )
    }

    // Verificar se matrícula já existe na empresa
    const matriculaExistente = await prisma.funcionario.findFirst({
      where: {
        matricula: data.matricula,
        empresaId: data.empresaId
      }
    })

    if (matriculaExistente) {
      return NextResponse.json(
        { error: "Matrícula já existe nesta empresa" },
        { status: 409 }
      )
    }

    // Criar funcionário
    const funcionario = await prisma.funcionario.create({
      data: {
        ...data,
        dataNascimento: data.dataNascimento ? new Date(data.dataNascimento) : null,
        dataAdmissao: new Date(data.dataAdmissao)
      },
      include: {
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            tipoEmpresa: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "CREATE_FUNCIONARIO",
        tabelaAfetada: "funcionarios",
        registroId: funcionario.id,
        dadosNovos: funcionario,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(funcionario, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar funcionário:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
