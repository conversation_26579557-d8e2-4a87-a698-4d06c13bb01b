#!/bin/bash

# Script de Deploy para RLP-V2
# Servidor: *********** (rlp-server)

set -e

echo "🚀 Iniciando deploy do RLP-V2..."

# Configurações
SERVER="rlp-server"
DEPLOY_PATH="/var/www/rlp-v2"
APP_NAME="rlp-v2"

# 1. Build da aplicação
echo "📦 Fazendo build da aplicação..."
npm run build

# 2. Criar arquivo tar com os arquivos necessários
echo "📁 Criando pacote para deploy..."
tar -czf deploy.tar.gz \
    .next \
    public \
    package.json \
    package-lock.json \
    next.config.ts \
    --exclude=node_modules

# 3. Enviar arquivos para o servidor
echo "📤 Enviando arquivos para o servidor..."
scp deploy.tar.gz $SERVER:$DEPLOY_PATH/

# 4. Executar comandos no servidor
echo "🔧 Configurando aplicação no servidor..."
ssh $SERVER << EOF
    cd $DEPLOY_PATH
    
    # Parar aplicação se estiver rodando
    pm2 stop $APP_NAME || true
    pm2 delete $APP_NAME || true
    
    # Extrair arquivos
    tar -xzf deploy.tar.gz
    
    # Instalar dependências de produção
    npm ci --only=production
    
    # Configurar PM2
    cat > ecosystem.config.js << EOL
module.exports = {
  apps: [{
    name: '$APP_NAME',
    script: 'npm',
    args: 'start',
    cwd: '$DEPLOY_PATH',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOL
    
    # Iniciar aplicação
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    # Limpar arquivo temporário
    rm -f deploy.tar.gz
    
    echo "✅ Deploy concluído com sucesso!"
    echo "🌐 Aplicação rodando em: http://***********:3000"
EOF

# 5. Limpar arquivo local
rm -f deploy.tar.gz

echo "🎉 Deploy finalizado!"
echo "🔗 Acesse: http://***********:3000"
