import { auth } from "@/lib/auth-basic"
import { redirect } from "next/navigation"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export default async function FuncionariosPage() {
  const session = await auth()

  if (!session?.user) {
    redirect("/auth/login")
  }

  // Buscar funcionários com dados relacionados
  const funcionarios = await prisma.funcionario.findMany({
    include: {
      empresa: {
        select: {
          id: true,
          nomeFantasia: true,
          tipoEmpresa: true
        }
      },
      _count: {
        select: {
          registrosPonto: true,
          funcionarioEpis: true
        }
      }
    },
    orderBy: [
      { ativo: 'desc' },
      { nomeCompleto: 'asc' }
    ]
  })

  const funcionariosAtivos = funcionarios.filter(f => f.ativo)
  const funcionariosInativos = funcionarios.filter(f => !f.ativo)

  // Estatísticas
  const totalRegistrosPonto = funcionarios.reduce((acc, f) => acc + f._count.registrosPonto, 0)
  const totalEpis = funcionarios.reduce((acc, f) => acc + f._count.funcionarioEpis, 0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Funcionários</h1>
              <span className="ml-4 px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">
                {funcionariosAtivos.length} ativo{funcionariosAtivos.length !== 1 ? 's' : ''}
              </span>
              {funcionariosInativos.length > 0 && (
                <span className="ml-2 px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded-full">
                  {funcionariosInativos.length} inativo{funcionariosInativos.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Novo Funcionário
              </button>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Importar CSV
              </button>
              <form action="/api/auth/signout" method="post">
                <button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Sair
                </button>
              </form>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Estatísticas */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Funcionários Ativos
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {funcionariosAtivos.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Registros de Ponto
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalRegistrosPonto.toLocaleString()}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          EPIs Entregues
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalEpis}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Empresas
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {new Set(funcionarios.map(f => f.empresaId)).size}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filtros */}
          <div className="mb-6">
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <input
                    type="text"
                    placeholder="Buscar por nome, CPF, matrícula ou email..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Todas as empresas</option>
                  {Array.from(new Set(funcionarios.map(f => f.empresa.nomeFantasia))).map(empresa => (
                    <option key={empresa} value={empresa}>{empresa}</option>
                  ))}
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Todos os status</option>
                  <option value="ativo">Ativo</option>
                  <option value="inativo">Inativo</option>
                </select>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Filtrar
                </button>
              </div>
            </div>
          </div>

          {/* Lista de Funcionários */}
          {funcionarios.length > 0 ? (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {funcionarios.map((funcionario) => (
                  <li key={funcionario.id}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                              <span className="text-lg font-medium text-gray-700">
                                {funcionario.nomeCompleto.split(' ').map(n => n.charAt(0)).slice(0, 2).join('')}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {funcionario.nomeCompleto}
                            </div>
                            <div className="text-sm text-gray-500">
                              {funcionario.cargo} • {funcionario.empresa.nomeFantasia}
                            </div>
                            <div className="text-sm text-gray-500">
                              Matrícula: {funcionario.matricula} • CPF: {funcionario.cpf}
                            </div>
                            {funcionario.setor && (
                              <div className="text-sm text-gray-500">
                                Setor: {funcionario.setor}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="text-sm text-gray-500">
                              Admissão: {new Date(funcionario.dataAdmissao).toLocaleDateString('pt-BR')}
                            </div>
                            <div className="text-sm text-gray-500">
                              {funcionario._count.registrosPonto} registros de ponto
                            </div>
                            <div className="text-sm text-gray-500">
                              {funcionario._count.funcionarioEpis} EPIs
                            </div>
                          </div>
                          <div className="flex flex-col space-y-1">
                            {funcionario.ativo ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Ativo
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inativo
                              </span>
                            )}
                            {funcionario.empresa.tipoEmpresa === 'principal' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Principal
                              </span>
                            )}
                          </div>
                          <div className="flex flex-col space-y-1">
                            <button className="text-blue-600 hover:text-blue-900 text-sm font-medium">
                              Editar
                            </button>
                            <button className="text-green-600 hover:text-green-900 text-sm font-medium">
                              Ponto
                            </button>
                            <button className="text-yellow-600 hover:text-yellow-900 text-sm font-medium">
                              EPIs
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum funcionário cadastrado</h3>
              <p className="mt-1 text-sm text-gray-500">
                Comece adicionando o primeiro funcionário ao sistema.
              </p>
              <div className="mt-6">
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                  Novo Funcionário
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
