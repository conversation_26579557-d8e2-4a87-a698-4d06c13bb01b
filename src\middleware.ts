import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"

// Rotas de API que requerem autenticação
const protectedApiRoutes = [
  "/api/empresas",
  "/api/funcionarios",
  "/api/ponto",
  "/api/relatorios"
]

// Rotas pública<PERSON> (não requerem autenticação)
const publicRoutes = [
  "/",
  "/auth/login",
  "/auth/error",
  "/auth/forgot-password",
  "/api/auth"
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Permitir rotas públicas
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next()
  }

  // Verificar se é uma API protegida
  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route))

  if (isProtectedApiRoute) {
    // Para APIs, verificar se tem token de sessão
    const sessionToken = request.cookies.get("next-auth.session-token") ||
                        request.cookies.get("__Secure-next-auth.session-token")

    if (!sessionToken) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public).*)",
  ],
}
