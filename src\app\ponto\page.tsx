import { auth } from "@/lib/auth-basic"
import { redirect } from "next/navigation"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export default async function PontoPage() {
  const session = await auth()

  if (!session?.user) {
    redirect("/auth/login")
  }

  // Buscar registros de ponto recentes
  const registrosRecentes = await prisma.registroPonto.findMany({
    include: {
      funcionario: {
        select: {
          id: true,
          nomeCompleto: true,
          matricula: true,
          cargo: true,
          empresa: {
            select: {
              nomeFantasia: true
            }
          }
        }
      }
    },
    orderBy: {
      dataHora: 'desc'
    },
    take: 20
  })

  // Estatísticas do dia
  const hoje = new Date()
  hoje.setHours(0, 0, 0, 0)
  const amanha = new Date(hoje)
  amanha.setDate(amanha.getDate() + 1)

  const registrosHoje = await prisma.registroPonto.count({
    where: {
      dataHora: {
        gte: hoje,
        lt: amanha
      }
    }
  })

  const funcionariosComRegistroHoje = await prisma.registroPonto.groupBy({
    by: ['funcionarioId'],
    where: {
      dataHora: {
        gte: hoje,
        lt: amanha
      }
    }
  })

  const totalFuncionarios = await prisma.funcionario.count({
    where: { ativo: true }
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Controle de Ponto</h1>
              <span className="ml-4 px-2 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">
                {registrosHoje} registros hoje
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                {new Date().toLocaleDateString('pt-BR', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </div>
              <div className="text-lg font-mono font-bold text-gray-900">
                {new Date().toLocaleTimeString('pt-BR')}
              </div>
              <form action="/api/auth/signout" method="post">
                <button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Sair
                </button>
              </form>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Estatísticas do Dia */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Resumo do Dia</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Registros Hoje
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {registrosHoje}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Funcionários Presentes
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {funcionariosComRegistroHoje.length}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Taxa de Presença
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalFuncionarios > 0 ? Math.round((funcionariosComRegistroHoje.length / totalFuncionarios) * 100) : 0}%
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Sistema
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          Online
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ações Rápidas */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Registrar Ponto</h3>
                    <p className="text-sm text-gray-500">Biometria ou manual</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Espelho de Ponto</h3>
                    <p className="text-sm text-gray-500">Consultar registros</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Justificativas</h3>
                    <p className="text-sm text-gray-500">Corrigir irregularidades</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Registros Recentes */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Registros Recentes</h2>
            {registrosRecentes.length > 0 ? (
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <ul className="divide-y divide-gray-200">
                  {registrosRecentes.map((registro) => (
                    <li key={registro.id}>
                      <div className="px-4 py-4 sm:px-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {registro.funcionario.nomeCompleto.split(' ').map(n => n.charAt(0)).slice(0, 2).join('')}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {registro.funcionario.nomeCompleto}
                              </div>
                              <div className="text-sm text-gray-500">
                                {registro.funcionario.cargo} • {registro.funcionario.empresa.nomeFantasia}
                              </div>
                              <div className="text-sm text-gray-500">
                                Matrícula: {registro.funcionario.matricula}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="text-sm font-medium text-gray-900">
                                {new Date(registro.dataHora).toLocaleTimeString('pt-BR')}
                              </div>
                              <div className="text-sm text-gray-500">
                                {new Date(registro.dataHora).toLocaleDateString('pt-BR')}
                              </div>
                              <div className="text-sm text-gray-500">
                                {registro.origemRegistro}
                              </div>
                            </div>
                            <div className="flex flex-col items-end space-y-1">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                registro.tipoRegistro === 'entrada' ? 'bg-green-100 text-green-800' :
                                registro.tipoRegistro === 'saida' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {registro.tipoRegistro.toUpperCase()}
                              </span>
                              {registro.validado ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Validado
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                  Pendente
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        {registro.observacoes && (
                          <div className="mt-2 text-sm text-gray-600">
                            💬 {registro.observacoes}
                          </div>
                        )}
                        {registro.localizacao && (
                          <div className="mt-1 text-sm text-gray-500">
                            📍 {registro.localizacao}
                          </div>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum registro encontrado</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Os registros de ponto aparecerão aqui conforme forem realizados.
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
