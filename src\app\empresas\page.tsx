"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Building2, Calendar, MapPin, Plus, Search, Users } from "lucide-react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

interface Empresa {
  id: string
  tipoEmpresa: string
  nomeFantasia: string
  razaoSocial: string
  cnpj: string
  nomeProjeto?: string
  endereco: any
  contatos: any
  dataInicio?: string
  dataFimPrevista?: string
  ativo: boolean
  empresaPrincipal?: {
    id: string
    nomeFantasia: string
  }
  _count: {
    funcionarios: number
    funcionarioAlocacoes: number
  }
}

interface EmpresasResponse {
  empresas: Empresa[]
  total: number
  page: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function EmpresasPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [empresas, setEmpresas] = useState<Empresa[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("ativo")
  const [tipoFilter, setTipoFilter] = useState("todos")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    if (status === "loading") return

    if (!session?.user) {
      router.push("/auth/login")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return <div className="flex items-center justify-center min-h-screen">Carregando...</div>
  }

  if (!session?.user) {
    return null
  }

  // Função para buscar empresas
  const fetchEmpresas = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        status: statusFilter,
        tipo: tipoFilter,
        page: currentPage.toString(),
        limit: '10'
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`/api/empresas?${params}`)

      if (!response.ok) {
        throw new Error('Erro ao buscar empresas')
      }

      const data: EmpresasResponse = await response.json()
      setEmpresas(data.empresas)
      setTotalPages(data.totalPages)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }

  // Carregar empresas ao montar o componente e quando filtros mudarem
  useEffect(() => {
    fetchEmpresas()
  }, [statusFilter, tipoFilter, currentPage, searchTerm])

  const empresaPrincipal = empresas.find(e => e.tipoEmpresa === 'principal')
  const empresasClientes = empresas.filter(e => e.tipoEmpresa === 'cliente')

  const totalFuncionarios = empresas.reduce((acc, emp) => acc + emp._count.funcionarios, 0)
  const totalAlocacoes = empresas.reduce((acc, emp) => acc + emp._count.funcionarioAlocacoes, 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando empresas...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchEmpresas}>Tentar novamente</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Empresas</h1>
              <p className="mt-1 text-sm text-gray-500">
                Gerencie empresas e projetos
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/empresas/principal">
                  <Building2 className="h-4 w-4 mr-2" />
                  Empresa Principal
                </Link>
              </Button>
              <Button asChild>
                <Link href="/empresas/nova">
                  <Plus className="h-4 w-4 mr-2" />
                  Nova Empresa
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Empresas</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{empresas.length}</div>
              <p className="text-xs text-muted-foreground">
                {empresasClientes.length} projetos ativos
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Funcionários</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalFuncionarios}</div>
              <p className="text-xs text-muted-foreground">
                {totalAlocacoes} alocados em projetos
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Projetos Ativos</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{empresasClientes.length}</div>
              <p className="text-xs text-muted-foreground">
                Em andamento
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taxa de Alocação</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalFuncionarios > 0 ? Math.round((totalAlocacoes / totalFuncionarios) * 100) : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Funcionários alocados
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Filtros</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar por nome, CNPJ ou projeto..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="ativo">Ativo</SelectItem>
                  <SelectItem value="inativo">Inativo</SelectItem>
                </SelectContent>
              </Select>
              <Select value={tipoFilter} onValueChange={setTipoFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="principal">Principal</SelectItem>
                  <SelectItem value="cliente">Clientes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Empresas */}
        <div className="space-y-6">
          {/* Empresa Principal */}
          {empresaPrincipal && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5 text-blue-600" />
                      {empresaPrincipal.nomeFantasia}
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        Principal
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      {empresaPrincipal.razaoSocial} • CNPJ: {empresaPrincipal.cnpj}
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    Configurar
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-600">Funcionários</p>
                    <p className="text-lg font-semibold">{empresaPrincipal._count.funcionarios}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Projetos</p>
                    <p className="text-lg font-semibold">{empresasClientes.length}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Contato</p>
                    <p className="text-sm">{empresaPrincipal.contatos?.telefone || 'Não informado'}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Status</p>
                    <Badge variant={empresaPrincipal.ativo ? "default" : "secondary"}>
                      {empresaPrincipal.ativo ? "Ativo" : "Inativo"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Empresas Clientes */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Projetos ({empresasClientes.length})
            </h2>
            <div className="grid gap-4">
              {empresasClientes.map((empresa) => (
                <Card key={empresa.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {empresa.nomeProjeto || empresa.nomeFantasia}
                          <Badge variant={empresa.ativo ? "default" : "secondary"}>
                            {empresa.ativo ? "Ativo" : "Inativo"}
                          </Badge>
                        </CardTitle>
                        <CardDescription>
                          {empresa.razaoSocial} • CNPJ: {empresa.cnpj}
                        </CardDescription>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Ver Detalhes
                        </Button>
                        <Button variant="outline" size="sm">
                          Funcionários
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Funcionários</p>
                        <p className="text-lg font-semibold">{empresa._count.funcionarios}</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Alocados</p>
                        <p className="text-lg font-semibold">{empresa._count.funcionarioAlocacoes}</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Início</p>
                        <p className="text-sm">
                          {empresa.dataInicio ? new Date(empresa.dataInicio).toLocaleDateString('pt-BR') : 'Não informado'}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Previsão Fim</p>
                        <p className="text-sm">
                          {empresa.dataFimPrevista ? new Date(empresa.dataFimPrevista).toLocaleDateString('pt-BR') : 'Não informado'}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Contato</p>
                        <p className="text-sm">{empresa.contatos?.telefone || 'Não informado'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Paginação */}
          {totalPages > 1 && (
            <div className="flex justify-center space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Anterior
              </Button>
              <span className="flex items-center px-4 py-2 text-sm text-gray-700">
                Página {currentPage} de {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Próxima
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

