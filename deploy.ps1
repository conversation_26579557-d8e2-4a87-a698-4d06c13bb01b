# Script de Deploy para RLP-V2 (PowerShell)
Write-Host "Iniciando deploy do RLP-V2..." -ForegroundColor Green

# 1. Build da aplicação
Write-Host "Fazendo build da aplicacao..." -ForegroundColor Yellow
npm run build

# 2. Criar arquivo tar
Write-Host "Criando pacote para deploy..." -ForegroundColor Yellow
tar -czf deploy.tar.gz .next public package.json package-lock.json next.config.ts .env.local

# 3. Enviar para servidor
Write-Host "Enviando arquivos para o servidor..." -ForegroundColor Yellow
scp deploy.tar.gz rlp-server:/var/www/rlp-v2/

# 4. Configurar no servidor
Write-Host "Configurando aplicacao no servidor..." -ForegroundColor Yellow
ssh rlp-server "cd /var/www/rlp-v2 && tar -xzf deploy.tar.gz && npm ci --only=production"

# 5. Configurar PM2
ssh rlp-server "cd /var/www/rlp-v2 && pm2 stop rlp-v2 || true && pm2 delete rlp-v2 || true"

$pm2Config = @"
module.exports = {
  apps: [{
    name: 'rlp-v2',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/rlp-v2',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
"@

$pm2Config | ssh rlp-server "cd /var/www/rlp-v2 && cat > ecosystem.config.js"

# 6. Iniciar aplicação
ssh rlp-server "cd /var/www/rlp-v2 && pm2 start ecosystem.config.js && pm2 save"

# 7. Limpar
Remove-Item "deploy.tar.gz" -ErrorAction SilentlyContinue
ssh rlp-server "cd /var/www/rlp-v2 && rm -f deploy.tar.gz"

Write-Host "Deploy finalizado com sucesso!" -ForegroundColor Green
Write-Host "Acesse: http://***********:3000" -ForegroundColor Cyan
