{
  "React Functional Component": {
    "prefix": "rfc",
    "body": [
      "interface ${1:ComponentName}Props {",
      "  $2",
      "}",
      "",
      "export default function ${1:ComponentName}({ $3 }: ${1:ComponentName}Props) {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  )",
      "}",
    ],
    "description": "Create a React functional component with TypeScript",
  },
  "Custom Hook": {
    "prefix": "hook",
    "body": [
      "import { useState, useEffect } from 'react'",
      "",
      "export function use${1:HookName}() {",
      "  const [${2:state}, set${2/(.*)/${1:/capitalize}/}] = useState($3)",
      "",
      "  useEffect(() => {",
      "    $4",
      "  }, [])",
      "",
      "  return {",
      "    ${2:state},",
      "    set${2/(.*)/${1:/capitalize}/}",
      "  }",
      "}",
    ],
    "description": "Create a custom React hook",
  },
}
