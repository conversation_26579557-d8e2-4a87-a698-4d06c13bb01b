"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Users, Plus, Calendar, DollarSign, ArrowLeft, Search } from "lucide-react"

interface Funcionario {
  id: string
  nomeCompleto: string
  cpf: string
  cargo: string
  setor: string
  dataAdmissao: string
  ativo: boolean
  empresa: {
    id: string
    nomeFantasia: string
  }
}

interface Alocacao {
  id: string
  dataInicio: string
  dataFimPrevista?: string
  dataFimReal?: string
  valorHoraProjeto?: number
  status: string
  observacoes?: string
  funcionario: Funcionario
}

interface AlocacoesResponse {
  alocados: Alocacao[]
  estatisticas: {
    total_alocados: number
    valor_total_hora: number
    alocacoes_recentes: number
  }
}

interface Empresa {
  id: string
  nomeFantasia: string
  nomeProjeto?: string
  tipoEmpresa: string
}

export default function AlocacoesPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const empresaId = params.id as string

  const [empresa, setEmpresa] = useState<Empresa | null>(null)
  const [alocacoes, setAlocacoes] = useState<Alocacao[]>([])
  const [estatisticas, setEstatisticas] = useState({
    total_alocados: 0,
    valor_total_hora: 0,
    alocacoes_recentes: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")

  // Buscar dados da empresa e alocações
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // Buscar dados da empresa
        const empresaResponse = await fetch(`/api/empresas/${empresaId}`)
        if (!empresaResponse.ok) {
          throw new Error('Empresa não encontrada')
        }
        const empresaData = await empresaResponse.json()
        setEmpresa(empresaData)

        // Buscar alocações
        const alocacoesResponse = await fetch(`/api/empresas/${empresaId}/funcionarios`)
        if (!alocacoesResponse.ok) {
          throw new Error('Erro ao buscar alocações')
        }
        const alocacoesData: AlocacoesResponse = await alocacoesResponse.json()
        setAlocacoes(alocacoesData.alocados)
        setEstatisticas(alocacoesData.estatisticas)

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido')
      } finally {
        setLoading(false)
      }
    }

    if (empresaId) {
      fetchData()
    }
  }, [empresaId])

  // Filtrar alocações por busca
  const alocacoesFiltradas = alocacoes.filter(alocacao =>
    alocacao.funcionario.nomeCompleto.toLowerCase().includes(searchTerm.toLowerCase()) ||
    alocacao.funcionario.cpf.includes(searchTerm) ||
    alocacao.funcionario.cargo.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando alocações...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => router.back()}>Voltar</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Alocações - {empresa?.nomeProjeto || empresa?.nomeFantasia}
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  Gerencie funcionários alocados no projeto
                </p>
              </div>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nova Alocação
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Alocados</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{estatisticas.total_alocados}</div>
              <p className="text-xs text-muted-foreground">
                Funcionários ativos no projeto
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valor Total/Hora</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                R$ {estatisticas.valor_total_hora.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                Soma dos valores por hora
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Alocações Recentes</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{estatisticas.alocacoes_recentes}</div>
              <p className="text-xs text-muted-foreground">
                Últimos 30 dias
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Buscar Funcionários</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar por nome, CPF ou cargo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Lista de Alocações */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Funcionários Alocados ({alocacoesFiltradas.length})
          </h2>

          {alocacoesFiltradas.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum funcionário encontrado
                </h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm ? 'Tente ajustar os filtros de busca.' : 'Não há funcionários alocados neste projeto.'}
                </p>
                {!searchTerm && (
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Alocar Funcionário
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {alocacoesFiltradas.map((alocacao) => (
                <Card key={alocacao.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {alocacao.funcionario.nomeCompleto}
                          <Badge variant={alocacao.status === 'ativo' ? "default" : "secondary"}>
                            {alocacao.status}
                          </Badge>
                        </CardTitle>
                        <CardDescription>
                          {alocacao.funcionario.cargo} • CPF: {alocacao.funcionario.cpf}
                        </CardDescription>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Editar
                        </Button>
                        <Button variant="outline" size="sm">
                          Finalizar
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-600">Data Início</p>
                        <p className="text-sm">
                          {new Date(alocacao.dataInicio).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Previsão Fim</p>
                        <p className="text-sm">
                          {alocacao.dataFimPrevista 
                            ? new Date(alocacao.dataFimPrevista).toLocaleDateString('pt-BR')
                            : 'Não definida'
                          }
                        </p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Valor/Hora</p>
                        <p className="text-sm font-semibold">
                          {alocacao.valorHoraProjeto 
                            ? `R$ ${Number(alocacao.valorHoraProjeto).toFixed(2)}`
                            : 'Não definido'
                          }
                        </p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-600">Empresa Origem</p>
                        <p className="text-sm">{alocacao.funcionario.empresa.nomeFantasia}</p>
                      </div>
                    </div>
                    {alocacao.observacoes && (
                      <div className="mt-4 pt-4 border-t">
                        <p className="font-medium text-gray-600 text-sm">Observações:</p>
                        <p className="text-sm text-gray-700">{alocacao.observacoes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
