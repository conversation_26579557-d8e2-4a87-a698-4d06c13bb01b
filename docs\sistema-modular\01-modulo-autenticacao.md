# 🔐 MÓDULO DE AUTENTICAÇÃO

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar autenticação e autorização de usuários
- Controlar sessões e tokens de acesso
- Implementar níveis granulares de permissão
- Garantir segurança e auditoria de acessos

### **📊 RESPONSABILIDADES:**

- Login/logout de usuários
- Controle de sessões JWT
- Gestão de permissões por nível
- Recuperação de senhas
- Auditoria de acessos
- Integração com 2FA (futuro)

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. AUTENTICAÇÃO:**

- Login com email/senha
- Logout com invalidação de sessão
- Recuperação de senha via email
- Bloqueio por tentativas excessivas
- Sessões com timeout configurável

#### **2. AUTORIZAÇÃO:**

- 7 níveis de acesso granulares
- Controle de permissões por tela/ação
- Middleware de proteção de rotas
- Validação de permissões em tempo real

#### **3. GESTÃO DE SESSÕES:**

- Tokens JWT seguros
- Refresh tokens automáticos
- Controle de sessões múltiplas
- Logout forçado por administrador

#### **4. AUDITORIA:**

- Log de todos os acessos
- Tentativas de login falhadas
- Histórico de ações por usuário
- Relatórios de segurança

---

## 👥 NÍVEIS DE ACESSO

### **🔑 HIERARQUIA DE PERMISSÕES:**

```typescript
enum NivelAcesso {
  ADMIN_TOTAL = 'admin_total', // Acesso completo
  ADMIN_LIMITADO = 'admin_limitado', // Admin sem configs críticas
  GERENTE_RH = 'gerente_rh', // Gestão de funcionários
  SUPERVISOR_OBRA = 'supervisor_obra', // Supervisão específica
  CLIENTE_VINCULADO = 'cliente_vinculado', // Apenas funcionários alocados
  OPERADOR_BIOMETRIA = 'operador_biometria', // Dispositivos biométricos
  READONLY = 'readonly', // Apenas visualização
}
```

### **📋 MATRIZ DE PERMISSÕES COMPLETA:**

| Funcionalidade            | admin_total | admin_limitado | gerente_rh | supervisor_obra | cliente_vinculado | operador_biometria | readonly |
| ------------------------- | ----------- | -------------- | ---------- | --------------- | ----------------- | ------------------ | -------- |
| **Gestão de Empresas**    | ✅          | ❌             | ❌         | ❌              | ❌                | ❌                 | 👁️       |
| **CRUD Funcionários**     | ✅          | ✅             | ✅         | 🔒\*\*\*        | 🔒\*              | ❌                 | 👁️       |
| **Registro de Ponto**     | ✅          | ✅             | ✅         | ✅              | 🔒\*              | ✅                 | 👁️       |
| **Relatórios Gerais**     | ✅          | ✅             | ✅         | 🔒\*\*\*        | 🔒\*              | ❌                 | 👁️       |
| **Configurações Sistema** | ✅          | ❌             | ❌         | ❌              | ❌                | ❌                 | ❌       |
| **Configurações Empresa** | ✅          | 🔒\*\*         | ❌         | ❌              | ❌                | ❌                 | ❌       |
| **Biometria Gestão**      | ✅          | ✅             | ✅         | ❌              | ❌                | ✅                 | 👁️       |
| **Biometria Operação**    | ✅          | ✅             | ✅         | ✅              | ❌                | ✅                 | ❌       |
| **APIs Gestão**           | ✅          | ❌             | ❌         | ❌              | ❌                | ❌                 | ❌       |
| **APIs Uso**              | ✅          | ✅             | ✅         | 🔒\*\*\*        | 🔒\*              | ❌                 | ❌       |
| **Auditoria Completa**    | ✅          | ❌             | ❌         | ❌              | ❌                | ❌                 | ❌       |
| **Auditoria Limitada**    | ✅          | ✅             | 🔒\*\*\*\* | ❌              | ❌                | ❌                 | ❌       |
| **Licenciamento**         | ✅          | 👁️             | 👁️         | 👁️              | 👁️                | 👁️                 | 👁️       |
| **Escalas e Turnos**      | ✅          | ✅             | ✅         | 🔒\*\*\*        | ❌                | ❌                 | 👁️       |
| **Afastamentos**          | ✅          | ✅             | ✅         | 🔒\*\*\*        | 🔒\*              | ❌                 | 👁️       |
| **Banco de Horas**        | ✅          | ✅             | ✅         | 🔒\*\*\*        | 🔒\*              | ❌                 | 👁️       |

**Legenda:**

- ✅ = Acesso completo
- ❌ = Sem acesso
- 👁️ = Apenas visualização
- 🔒\* = Apenas funcionários alocados à sua empresa
- 🔒\*\* = Configurações não críticas (sem licenciamento, sem usuários)
- 🔒\*\*\* = Apenas funcionários da sua obra/projeto
- 🔒\*\*\*\* = Apenas logs relacionados aos seus funcionários

---

## 📱 TELAS E INTERFACES

### **1. TELA DE LOGIN** (`/auth/login`)

```typescript
interface LoginScreen {
  campos: {
    email: {
      tipo: 'email'
      obrigatorio: true
      validacao: 'Email válido'
      placeholder: '<EMAIL>'
    }
    senha: {
      tipo: 'password'
      obrigatorio: true
      minimo: 6
      placeholder: 'Sua senha'
      mostrar_senha: boolean
    }
    lembrar_me: {
      tipo: 'checkbox'
      padrao: false
      descricao: 'Manter-me conectado'
    }
  }

  acoes: {
    entrar: {
      validacao: 'Campos obrigatórios preenchidos'
      loading: 'Verificando credenciais...'
      sucesso: 'Redirecionamento para dashboard'
      erro: 'Credenciais inválidas'
    }
    esqueci_senha: {
      link: '/auth/forgot-password'
      texto: 'Esqueci minha senha'
    }
  }

  seguranca: {
    tentativas_maximas: 5
    bloqueio_tempo: '15 minutos'
    captcha_apos: 3
  }
}
```

### **2. TELA DE RECUPERAÇÃO** (`/auth/forgot-password`)

```typescript
interface ForgotPasswordScreen {
  etapa_1_email: {
    email: {
      tipo: 'email'
      obrigatorio: true
      placeholder: 'Digite seu email cadastrado'
    }
    enviar: {
      acao: 'Enviar link de recuperação'
      validacao: 'Email existe no sistema'
      sucesso: 'Email enviado com instruções'
    }
  }

  etapa_2_reset: {
    nova_senha: {
      tipo: 'password'
      minimo: 8
      validacao: 'Senha forte obrigatória'
    }
    confirmar_senha: {
      tipo: 'password'
      validacao: 'Senhas devem coincidir'
    }
    token_validade: '24 horas'
  }
}
```

### **3. TELA DE PERFIL** (`/auth/profile`)

```typescript
interface ProfileScreen {
  informacoes_usuario: {
    nome: string
    email: string
    nivel_acesso: NivelAcesso
    ultimo_login: Date
    sessoes_ativas: number
  }

  alterar_senha: {
    senha_atual: string
    nova_senha: string
    confirmar_nova: string
  }

  preferencias: {
    tema: 'light' | 'dark' | 'auto'
    idioma: 'pt-BR' | 'en-US'
    notificacoes_email: boolean
    timeout_sessao: number // minutos
  }

  sessoes_ativas: {
    dispositivo: string
    ip: string
    ultimo_acesso: Date
    acao_encerrar: boolean
  }[]
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface AuthAPI {
  // ✅ AUTENTICAÇÃO
  'POST /api/auth/login': {
    body: {
      email: string
      password: string
      remember_me?: boolean
    }
    response: {
      user: User
      access_token: string
      refresh_token: string
      expires_in: number
    }
  }

  'POST /api/auth/logout': {
    headers: { Authorization: 'Bearer token' }
    response: { message: 'Logout realizado com sucesso' }
  }

  'POST /api/auth/refresh': {
    body: { refresh_token: string }
    response: {
      access_token: string
      expires_in: number
    }
  }

  // ✅ RECUPERAÇÃO DE SENHA
  'POST /api/auth/forgot-password': {
    body: { email: string }
    response: { message: 'Email enviado' }
  }

  'POST /api/auth/reset-password': {
    body: {
      token: string
      new_password: string
    }
    response: { message: 'Senha alterada com sucesso' }
  }

  // ✅ PERFIL
  'GET /api/auth/me': {
    headers: { Authorization: 'Bearer token' }
    response: {
      user: User
      permissions: Permission[]
      active_sessions: Session[]
    }
  }

  'PUT /api/auth/profile': {
    headers: { Authorization: 'Bearer token' }
    body: {
      name?: string
      preferences?: UserPreferences
    }
    response: { user: User }
  }

  'POST /api/auth/change-password': {
    headers: { Authorization: 'Bearer token' }
    body: {
      current_password: string
      new_password: string
    }
    response: { message: 'Senha alterada' }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ USUÁRIOS
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  nivel_acesso VARCHAR(50) NOT NULL,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  email_verificado BOOLEAN DEFAULT FALSE,
  primeiro_login BOOLEAN DEFAULT TRUE,

  -- Segurança
  tentativas_login INTEGER DEFAULT 0,
  bloqueado_ate TIMESTAMP,
  ultimo_login TIMESTAMP,

  -- Preferências
  preferencias JSONB DEFAULT '{}',

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_nivel_acesso CHECK (
    nivel_acesso IN (
      'admin_total', 'admin_limitado', 'gerente_rh',
      'supervisor_obra', 'cliente_vinculado',
      'operador_biometria', 'readonly'
    )
  )
);

-- ✅ SESSÕES
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- Token
  refresh_token_hash VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,

  -- Dispositivo
  device_info JSONB,
  ip_address INET,
  user_agent TEXT,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  revogado_em TIMESTAMP,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ TENTATIVAS DE LOGIN
CREATE TABLE login_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  ip_address INET NOT NULL,
  user_agent TEXT,
  sucesso BOOLEAN NOT NULL,
  motivo_falha VARCHAR(100),
  tentativa_em TIMESTAMP DEFAULT NOW()
);

-- ✅ TOKENS DE RECUPERAÇÃO
CREATE TABLE password_reset_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token_hash VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  usado BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ✅ ÍNDICES
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_nivel_acesso ON users(nivel_acesso);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_login_attempts_email ON login_attempts(email);
CREATE INDEX idx_login_attempts_ip ON login_attempts(ip_address);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🛡️ SEGURANÇA:**

1. **Senhas:**
   - Mínimo 8 caracteres
   - Pelo menos 1 maiúscula, 1 minúscula, 1 número
   - Hash com bcrypt (12 rounds)
   - Não reutilizar últimas 5 senhas

2. **Bloqueios:**
   - 5 tentativas falhadas = bloqueio 15 min
   - 10 tentativas falhadas = bloqueio 1 hora
   - 20 tentativas falhadas = bloqueio 24 horas

3. **Sessões:**
   - JWT com expiração 4 horas
   - Refresh token válido por 30 dias
   - Máximo 3 sessões simultâneas
   - Logout automático por inatividade

4. **Tokens de Recuperação:**
   - Válidos por 24 horas
   - Uso único
   - Invalidados após uso ou expiração

### **🔑 PERMISSÕES:**

1. **Herança:**
   - admin_total herda todas as permissões
   - Níveis inferiores têm subconjunto de permissões

2. **Isolamento:**
   - cliente_vinculado vê apenas funcionários alocados
   - supervisor_obra vê apenas sua obra
   - operador_biometria acessa apenas dispositivos

3. **Auditoria:**
   - Todas as ações são logadas
   - Tentativas de acesso negado são registradas
   - Relatórios de segurança para admin_total

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE AUTENTICAÇÃO:**

```typescript
describe('Autenticação', () => {
  test('Login com credenciais válidas', async () => {
    const response = await login('<EMAIL>', 'password123')
    expect(response.status).toBe(200)
    expect(response.data.access_token).toBeDefined()
  })

  test('Login com credenciais inválidas', async () => {
    const response = await login('<EMAIL>', 'wrongpassword')
    expect(response.status).toBe(401)
    expect(response.data.error).toBe('Credenciais inválidas')
  })

  test('Bloqueio após 5 tentativas', async () => {
    for (let i = 0; i < 5; i++) {
      await login('<EMAIL>', 'wrongpassword')
    }
    const response = await login('<EMAIL>', 'password123')
    expect(response.status).toBe(423)
    expect(response.data.error).toContain('bloqueado')
  })
})
```

### **✅ TESTES DE AUTORIZAÇÃO:**

```typescript
describe('Autorização', () => {
  test('admin_total acessa todas as rotas', async () => {
    const token = await getToken('admin_total')
    const routes = ['/empresas', '/funcionarios', '/configuracoes']

    for (const route of routes) {
      const response = await request(route, {
        headers: { Authorization: token },
      })
      expect(response.status).toBe(200)
    }
  })

  test('readonly não acessa rotas de escrita', async () => {
    const token = await getToken('readonly')
    const response = await request('POST', '/funcionarios', {
      headers: { Authorization: token },
    })
    expect(response.status).toBe(403)
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**

- Setup NextAuth.js v5 (latest stable)
- Configuração JWT com Auth.js v5
- Middleware de autenticação Next.js 14+

**Semana 3-4: Telas de Auth**

- Tela de login
- Recuperação de senha
- Perfil do usuário

**Semana 5-6: Permissões**

- Sistema de níveis
- Middleware de autorização
- Testes de permissões

**Semana 7-8: Segurança**

- Auditoria de acessos
- Bloqueios e tentativas
- Testes de segurança

### **🔧 DEPENDÊNCIAS:**

- NextAuth.js v5 (Auth.js)
- bcryptjs (password hashing)
- @auth/prisma-adapter (database adapter)
- zod (validation)
- nodemailer (email service)
- @types/bcryptjs (TypeScript types)
