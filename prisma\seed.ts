import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...')

  // 1. Criar empresa principal (construtora)
  const empresaPrincipal = await prisma.empresa.upsert({
    where: { cnpj: '12.345.678/0001-90' },
    update: {},
    create: {
      tipoEmpresa: 'principal',
      nomeFantasia: 'Construtora RLPONTO',
      razaoSocial: 'RLPONTO Construções e Incorporações LTDA',
      cnpj: '12.345.678/0001-90',
      endereco: 'Rua das Construções, 123 - Centro',
      telefone: '(11) 3456-7890',
      email: '<EMAIL>',
      ativo: true
    }
  })

  console.log('✅ Empresa principal criada:', empresaPrincipal.nomeFantasia)

  // 2. Criar usuário administrador
  const senhaHash = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: senhaHash,
      nome: 'Administrador do Sistema',
      nivelAcesso: 'admin_total',
      ativo: true
    }
  })

  console.log('✅ Usuário administrador criado:', adminUser.email)

  // 3. Criar jornada de trabalho padrão
  const jornadaPadrao = await prisma.jornadaTrabalho.upsert({
    where: { id: '00000000-0000-0000-0000-000000000001' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      nome: 'Jornada Padrão 8h',
      empresaId: empresaPrincipal.id,
      tipoJornada: 'normal',
      cargaHorariaSemanal: 2400, // 40h * 60min = 2400min
      toleranciaEntrada: 15, // 15 minutos
      toleranciaSaida: 15, // 15 minutos
      configuracoes: {
        diasSemana: ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
        horaInicio: '08:00',
        horaFim: '17:00',
        intervaloAlmoco: {
          inicio: '12:00',
          fim: '13:00'
        }
      },
      ativo: true
    }
  })

  console.log('✅ Jornada de trabalho criada:', jornadaPadrao.nome)

  // 4. Criar turno padrão
  const turnoPadrao = await prisma.turno.upsert({
    where: { id: '00000000-0000-0000-0000-000000000001' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      nome: 'Turno Comercial',
      empresaId: empresaPrincipal.id,
      horarioInicio: '08:00:00',
      horarioFim: '17:00:00',
      intervalos: {
        almoco: {
          inicio: '12:00:00',
          fim: '13:00:00'
        }
      },
      configuracoes: {
        tolerancia: 15,
        obrigatorioRegistrarSaida: true
      },
      ativo: true
    }
  })

  console.log('✅ Turno padrão criado:', turnoPadrao.nome)

  // 5. Criar EPIs básicos
  const episBasicos = [
    {
      nome: 'Capacete de Segurança',
      categoria: 'Proteção da Cabeça',
      descricao: 'Capacete de segurança classe A',
      numeroCa: '12345'
    },
    {
      nome: 'Óculos de Proteção',
      categoria: 'Proteção dos Olhos',
      descricao: 'Óculos de proteção contra impactos',
      numeroCa: '23456'
    },
    {
      nome: 'Luvas de Segurança',
      categoria: 'Proteção das Mãos',
      descricao: 'Luvas de vaqueta para proteção',
      numeroCa: '34567'
    },
    {
      nome: 'Botina de Segurança',
      categoria: 'Proteção dos Pés',
      descricao: 'Botina com bico de aço',
      numeroCa: '45678'
    }
  ]

  for (const epi of episBasicos) {
    const existingEpi = await prisma.epi.findFirst({
      where: { numeroCa: epi.numeroCa }
    })

    if (!existingEpi) {
      await prisma.epi.create({
        data: epi
      })
    }
  }

  console.log('✅ EPIs básicos criados')

  // 6. Criar licença vitalícia para desenvolvimento
  await prisma.licencaAtiva.upsert({
    where: { chaveLicenca: 'DEV-RLPONTO-VITALICIO-2025' },
    update: {},
    create: {
      chaveLicenca: 'DEV-RLPONTO-VITALICIO-2025',
      tipoLicenca: 'vitalicia',
      nivelPlano: 'vitalicio',
      dataInicio: new Date('2025-01-01'),
      dataVencimento: new Date('2099-12-31'),
      maxFuncionarios: 9999,
      statusLicenca: 'ativa'
    }
  })

  console.log('✅ Licença vitalícia criada para desenvolvimento')

  // 7. Criar configurações básicas do sistema
  const configuracoesBasicas = [
    {
      chave: 'sistema.nome',
      valor: { valor: 'RLPONTO' },
      tipo: 'sistema',
      descricao: 'Nome do sistema'
    },
    {
      chave: 'sistema.versao',
      valor: { valor: '1.0.0' },
      tipo: 'sistema',
      descricao: 'Versão do sistema'
    },
    {
      chave: 'ponto.tolerancia_entrada',
      valor: { valor: 15 },
      tipo: 'sistema',
      descricao: 'Tolerância para entrada em minutos'
    },
    {
      chave: 'ponto.tolerancia_saida',
      valor: { valor: 15 },
      tipo: 'sistema',
      descricao: 'Tolerância para saída em minutos'
    },
    {
      chave: 'ponto.nunca_impedir',
      valor: { valor: true },
      tipo: 'sistema',
      descricao: 'Princípio NUNCA IMPEDIR - funcionário ativo sempre pode bater ponto'
    }
  ]

  for (const config of configuracoesBasicas) {
    await prisma.configuracaoSistema.upsert({
      where: { chave: config.chave },
      update: {},
      create: config
    })
  }

  console.log('✅ Configurações básicas criadas')

  // 8. Criar funcionário de exemplo
  const funcionarioExemplo = await prisma.funcionario.upsert({
    where: { cpf: '123.456.789-00' },
    update: {},
    create: {
      empresaId: empresaPrincipal.id,
      nomeCompleto: 'João da Silva Santos',
      cpf: '123.456.789-00',
      rg: '12.345.678-9',
      dataNascimento: new Date('1990-05-15'),
      sexo: 'M',
      estadoCivil: 'solteiro',
      telefone: '(11) 98765-4321',
      email: '<EMAIL>',
      endereco: 'Rua dos Funcionários, 456 - Vila Operária',
      matricula: 'FUNC001',
      cargo: 'Pedreiro',
      setor: 'Construção Civil',
      dataAdmissao: new Date('2025-01-15'),
      salario: 2500.00,
      ativo: true
    }
  })

  console.log('✅ Funcionário de exemplo criado:', funcionarioExemplo.nomeCompleto)

  // 9. Log de auditoria inicial
  await prisma.logAuditoria.create({
    data: {
      usuarioId: adminUser.id,
      acao: 'SEED_DATABASE',
      tabelaAfetada: 'SISTEMA',
      dadosNovos: {
        evento: 'Seed inicial do banco de dados executado',
        timestamp: new Date().toISOString(),
        versao: '1.0.0'
      },
      ipAddress: '127.0.0.1',
      userAgent: 'Prisma Seed Script'
    }
  })

  console.log('✅ Log de auditoria criado')

  console.log('🎉 Seed concluído com sucesso!')
  console.log('')
  console.log('📋 Dados criados:')
  console.log('   👤 Admin: <EMAIL> / admin123')
  console.log('   🏢 Empresa: Construtora RLPONTO')
  console.log('   👷 Funcionário: João da Silva Santos (FUNC001)')
  console.log('   🔑 Licença: DEV-RLPONTO-VITALICIO-2025')
  console.log('')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Erro no seed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
