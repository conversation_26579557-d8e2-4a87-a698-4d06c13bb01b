import { auth } from "@/lib/auth-basic"
import { PrismaClient } from "@prisma/client"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para empresa principal
const empresaPrincipalSchema = z.object({
  nomeFantasia: z.string().min(2, "Nome fantasia deve ter pelo menos 2 caracteres"),
  razaoSocial: z.string().min(2, "Razão social deve ter pelo menos 2 caracteres"),
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, "CNPJ deve estar no formato XX.XXX.XXX/XXXX-XX"),
  inscricaoEstadual: z.string().optional(),
  endereco: z.object({
    cep: z.string().regex(/^\d{5}-\d{3}$/, "CEP deve estar no formato XXXXX-XXX"),
    logradouro: z.string().min(5, "Logradouro deve ter pelo menos 5 caracteres"),
    numero: z.string().min(1, "Número é obrigatório"),
    complemento: z.string().optional(),
    bairro: z.string().min(2, "Bairro deve ter pelo menos 2 caracteres"),
    cidade: z.string().min(2, "Cidade deve ter pelo menos 2 caracteres"),
    estado: z.string().length(2, "Estado deve ter 2 caracteres"),
  }),
  contatos: z.object({
    telefone: z.string().optional(),
    celular: z.string().optional(),
    email: z.string().email("Email inválido").optional(),
    site: z.string().url("Site deve ser uma URL válida").optional(),
  }),
  configuracoes: z.object({
    jornadaPadrao: z.string().optional(),
    toleranciaEntrada: z.number().min(0).max(60).default(10),
    toleranciaSaida: z.number().min(0).max(60).default(10),
    horasSemanais: z.number().min(20).max(60).default(44),
  })
})

// GET /api/empresas/principal - Obter empresa principal
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const empresaPrincipal = await prisma.empresa.findFirst({
      where: { tipoEmpresa: 'principal' },
      include: {
        empresasClientes: {
          where: { ativo: true },
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true,
            dataInicio: true,
            dataFimPrevista: true,
            ativo: true
          }
        },
        funcionarios: {
          where: { ativo: true },
          select: {
            id: true,
            nomeCompleto: true,
            cargo: true,
            dataAdmissao: true
          }
        },
        _count: {
          select: {
            empresasClientes: {
              where: { ativo: true }
            },
            funcionarios: {
              where: { ativo: true }
            }
          }
        }
      }
    })

    if (!empresaPrincipal) {
      return NextResponse.json(
        { error: "Empresa principal não encontrada" },
        { status: 404 }
      )
    }

    // Calcular estatísticas
    const estatisticas = {
      total_funcionarios: empresaPrincipal._count.funcionarios,
      total_projetos: empresaPrincipal._count.empresasClientes,
      projetos_ativos: empresaPrincipal.empresasClientes.length,
      funcionarios_alocados: await prisma.funcionarioAlocacao.count({
        where: { status: 'ativo' }
      })
    }

    return NextResponse.json({
      ...empresaPrincipal,
      estatisticas
    })

  } catch (error) {
    console.error('Erro ao buscar empresa principal:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// PUT /api/empresas/principal - Atualizar empresa principal
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Apenas admin total pode alterar empresa principal" },
        { status: 403 }
      )
    }

    // Buscar empresa principal
    const empresaPrincipal = await prisma.empresa.findFirst({
      where: { tipoEmpresa: 'principal' }
    })

    if (!empresaPrincipal) {
      return NextResponse.json(
        { error: "Empresa principal não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = empresaPrincipalSchema.parse(body)

    // Se alterando CNPJ, verificar se não existe outro
    if (data.cnpj && data.cnpj !== empresaPrincipal.cnpj) {
      const existeCnpj = await prisma.empresa.findUnique({
        where: { cnpj: data.cnpj }
      })

      if (existeCnpj) {
        return NextResponse.json(
          { error: "CNPJ já cadastrado" },
          { status: 400 }
        )
      }
    }

    // Atualizar empresa principal
    const empresaAtualizada = await prisma.empresa.update({
      where: { id: empresaPrincipal.id },
      data: {
        nomeFantasia: data.nomeFantasia,
        razaoSocial: data.razaoSocial,
        cnpj: data.cnpj,
        inscricaoEstadual: data.inscricaoEstadual,
        endereco: data.endereco,
        contatos: data.contatos,
        configuracoes: data.configuracoes
      },
      include: {
        empresasClientes: {
          where: { ativo: true },
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true
          }
        },
        _count: {
          select: {
            empresasClientes: {
              where: { ativo: true }
            },
            funcionarios: {
              where: { ativo: true }
            }
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "UPDATE_EMPRESA_PRINCIPAL",
        tabelaAfetada: "empresas",
        registroId: empresaPrincipal.id,
        dadosAnteriores: empresaPrincipal,
        dadosNovos: empresaAtualizada,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(empresaAtualizada)

  } catch (error) {
    console.error('Erro ao atualizar empresa principal:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// POST /api/empresas/principal - Criar empresa principal (apenas se não existir)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Apenas admin total pode criar empresa principal" },
        { status: 403 }
      )
    }

    // Verificar se já existe empresa principal
    const existePrincipal = await prisma.empresa.findFirst({
      where: { tipoEmpresa: 'principal' }
    })

    if (existePrincipal) {
      return NextResponse.json(
        { error: "Já existe uma empresa principal cadastrada" },
        { status: 400 }
      )
    }

    const body = await request.json()
    const data = empresaPrincipalSchema.parse(body)

    // Verificar se CNPJ já existe
    const existeCnpj = await prisma.empresa.findUnique({
      where: { cnpj: data.cnpj }
    })

    if (existeCnpj) {
      return NextResponse.json(
        { error: "CNPJ já cadastrado" },
        { status: 400 }
      )
    }

    // Criar empresa principal
    const empresaPrincipal = await prisma.empresa.create({
      data: {
        tipoEmpresa: 'principal',
        nomeFantasia: data.nomeFantasia,
        razaoSocial: data.razaoSocial,
        cnpj: data.cnpj,
        inscricaoEstadual: data.inscricaoEstadual,
        endereco: data.endereco,
        contatos: data.contatos,
        configuracoes: data.configuracoes,
        createdBy: session.user.id
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "CREATE_EMPRESA_PRINCIPAL",
        tabelaAfetada: "empresas",
        registroId: empresaPrincipal.id,
        dadosNovos: empresaPrincipal,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(empresaPrincipal, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar empresa principal:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
