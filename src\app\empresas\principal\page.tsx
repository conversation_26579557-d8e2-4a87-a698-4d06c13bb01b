"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { ArrowLeft, Building2, FileText, MapPin, Phone, Save } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { z } from "zod"

// Schema de validação
const empresaPrincipalSchema = z.object({
  nomeFantasia: z.string().min(2, "Nome fantasia deve ter pelo menos 2 caracteres"),
  razaoSocial: z.string().min(2, "Razão social deve ter pelo menos 2 caracteres"),
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, "CNPJ deve estar no formato XX.XXX.XXX/XXXX-XX"),
  inscricaoEstadual: z.string().optional(),
  endereco: z.object({
    cep: z.string().regex(/^\d{5}-\d{3}$/, "CEP deve estar no formato XXXXX-XXX"),
    logradouro: z.string().min(5, "Logradouro deve ter pelo menos 5 caracteres"),
    numero: z.string().min(1, "Número é obrigatório"),
    complemento: z.string().optional(),
    bairro: z.string().min(2, "Bairro deve ter pelo menos 2 caracteres"),
    cidade: z.string().min(2, "Cidade deve ter pelo menos 2 caracteres"),
    estado: z.string().length(2, "Estado deve ter 2 caracteres"),
  }),
  contatos: z.object({
    telefone: z.string().optional(),
    celular: z.string().optional(),
    email: z.string().email("Email inválido").optional(),
    site: z.string().url("Site deve ser uma URL válida").optional(),
  }),
  configuracoes: z.object({
    jornadaPadrao: z.string().optional(),
    toleranciaEntrada: z.number().min(0).max(60).default(10),
    toleranciaSaida: z.number().min(0).max(60).default(10),
    horasSemanais: z.number().min(20).max(60).default(44),
  })
})

type EmpresaPrincipalForm = z.infer<typeof empresaPrincipalSchema>

export default function EmpresaPrincipalPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [empresaExistente, setEmpresaExistente] = useState<any>(null)
  const [formData, setFormData] = useState<EmpresaPrincipalForm>({
    nomeFantasia: "",
    razaoSocial: "",
    cnpj: "",
    inscricaoEstadual: "",
    endereco: {
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      estado: "",
    },
    contatos: {
      telefone: "",
      celular: "",
      email: "",
      site: "",
    },
    configuracoes: {
      jornadaPadrao: "",
      toleranciaEntrada: 10,
      toleranciaSaida: 10,
      horasSemanais: 44,
    }
  })

  // Carregar dados da empresa principal se existir
  useEffect(() => {
    async function carregarEmpresaPrincipal() {
      try {
        const response = await fetch('/api/empresas/principal')
        if (response.ok) {
          const empresa = await response.json()
          setEmpresaExistente(empresa)
          
          // Preencher formulário com dados existentes
          setFormData({
            nomeFantasia: empresa.nomeFantasia || "",
            razaoSocial: empresa.razaoSocial || "",
            cnpj: empresa.cnpj || "",
            inscricaoEstadual: empresa.inscricaoEstadual || "",
            endereco: {
              cep: empresa.endereco?.cep || "",
              logradouro: empresa.endereco?.logradouro || "",
              numero: empresa.endereco?.numero || "",
              complemento: empresa.endereco?.complemento || "",
              bairro: empresa.endereco?.bairro || "",
              cidade: empresa.endereco?.cidade || "",
              estado: empresa.endereco?.estado || "",
            },
            contatos: {
              telefone: empresa.contatos?.telefone || "",
              celular: empresa.contatos?.celular || "",
              email: empresa.contatos?.email || "",
              site: empresa.contatos?.site || "",
            },
            configuracoes: {
              jornadaPadrao: empresa.configuracoes?.jornadaPadrao || "",
              toleranciaEntrada: empresa.configuracoes?.toleranciaEntrada || 10,
              toleranciaSaida: empresa.configuracoes?.toleranciaSaida || 10,
              horasSemanais: empresa.configuracoes?.horasSemanais || 44,
            }
          })
        }
      } catch (error) {
        console.error('Erro ao carregar empresa principal:', error)
      }
    }

    carregarEmpresaPrincipal()
  }, [])

  // Função para buscar CEP
  const buscarCEP = async (cep: string) => {
    if (cep.length === 9) {
      try {
        const response = await fetch(`https://viacep.com.br/ws/${cep.replace('-', '')}/json/`)
        const data = await response.json()
        
        if (!data.erro) {
          setFormData(prev => ({
            ...prev,
            endereco: {
              ...prev.endereco,
              logradouro: data.logradouro || "",
              bairro: data.bairro || "",
              cidade: data.localidade || "",
              estado: data.uf || "",
            }
          }))
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error)
      }
    }
  }

  // Função para salvar empresa principal
  const salvarEmpresa = async () => {
    try {
      setLoading(true)

      // Validar dados
      const validatedData = empresaPrincipalSchema.parse(formData)

      const method = empresaExistente ? 'PUT' : 'POST'
      const url = empresaExistente ? `/api/empresas/principal` : '/api/empresas/principal'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedData),
      })

      if (response.ok) {
        toast({
          title: "Sucesso!",
          description: `Empresa principal ${empresaExistente ? 'atualizada' : 'cadastrada'} com sucesso.`,
        })
        router.push('/empresas')
      } else {
        const error = await response.json()
        toast({
          title: "Erro",
          description: error.message || "Erro ao salvar empresa principal.",
          variant: "destructive",
        })
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0]
        toast({
          title: "Dados inválidos",
          description: firstError.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Erro",
          description: "Erro interno do servidor.",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                  <Building2 className="h-8 w-8 text-blue-600" />
                  {empresaExistente ? 'Configurar' : 'Cadastrar'} Empresa Principal
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  {empresaExistente ? 'Atualize os dados' : 'Configure os dados'} da sua construtora
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button onClick={salvarEmpresa} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Formulário Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Dados Básicos */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Dados Básicos
                </CardTitle>
                <CardDescription>
                  Informações principais da empresa
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="nomeFantasia">Nome Fantasia *</Label>
                    <Input
                      id="nomeFantasia"
                      value={formData.nomeFantasia}
                      onChange={(e) => setFormData(prev => ({ ...prev, nomeFantasia: e.target.value }))}
                      placeholder="Construtora ABC Ltda"
                    />
                  </div>
                  <div>
                    <Label htmlFor="razaoSocial">Razão Social *</Label>
                    <Input
                      id="razaoSocial"
                      value={formData.razaoSocial}
                      onChange={(e) => setFormData(prev => ({ ...prev, razaoSocial: e.target.value }))}
                      placeholder="Construtora ABC Limitada"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="cnpj">CNPJ *</Label>
                    <Input
                      id="cnpj"
                      value={formData.cnpj}
                      onChange={(e) => {
                        // Aplicar máscara de CNPJ
                        let value = e.target.value.replace(/\D/g, '')
                        value = value.replace(/^(\d{2})(\d)/, '$1.$2')
                        value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
                        value = value.replace(/\.(\d{3})(\d)/, '.$1/$2')
                        value = value.replace(/(\d{4})(\d)/, '$1-$2')
                        setFormData(prev => ({ ...prev, cnpj: value }))
                      }}
                      placeholder="XX.XXX.XXX/XXXX-XX"
                      maxLength={18}
                    />
                  </div>
                  <div>
                    <Label htmlFor="inscricaoEstadual">Inscrição Estadual</Label>
                    <Input
                      id="inscricaoEstadual"
                      value={formData.inscricaoEstadual}
                      onChange={(e) => setFormData(prev => ({ ...prev, inscricaoEstadual: e.target.value }))}
                      placeholder="123.456.789.012"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Endereço */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Endereço
                </CardTitle>
                <CardDescription>
                  Localização da empresa
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="cep">CEP *</Label>
                    <Input
                      id="cep"
                      value={formData.endereco.cep}
                      onChange={(e) => {
                        // Aplicar máscara de CEP
                        let value = e.target.value.replace(/\D/g, '')
                        value = value.replace(/^(\d{5})(\d)/, '$1-$2')
                        setFormData(prev => ({
                          ...prev,
                          endereco: { ...prev.endereco, cep: value }
                        }))

                        // Buscar CEP automaticamente
                        if (value.length === 9) {
                          buscarCEP(value)
                        }
                      }}
                      placeholder="XXXXX-XXX"
                      maxLength={9}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="logradouro">Logradouro *</Label>
                    <Input
                      id="logradouro"
                      value={formData.endereco.logradouro}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        endereco: { ...prev.endereco, logradouro: e.target.value }
                      }))}
                      placeholder="Rua, Avenida, etc."
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="numero">Número *</Label>
                    <Input
                      id="numero"
                      value={formData.endereco.numero}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        endereco: { ...prev.endereco, numero: e.target.value }
                      }))}
                      placeholder="123"
                    />
                  </div>
                  <div>
                    <Label htmlFor="complemento">Complemento</Label>
                    <Input
                      id="complemento"
                      value={formData.endereco.complemento}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        endereco: { ...prev.endereco, complemento: e.target.value }
                      }))}
                      placeholder="Sala, Andar"
                    />
                  </div>
                  <div>
                    <Label htmlFor="bairro">Bairro *</Label>
                    <Input
                      id="bairro"
                      value={formData.endereco.bairro}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        endereco: { ...prev.endereco, bairro: e.target.value }
                      }))}
                      placeholder="Centro"
                    />
                  </div>
                  <div>
                    <Label htmlFor="estado">Estado *</Label>
                    <Select
                      value={formData.endereco.estado}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        endereco: { ...prev.endereco, estado: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="UF" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="AC">AC</SelectItem>
                        <SelectItem value="AL">AL</SelectItem>
                        <SelectItem value="AP">AP</SelectItem>
                        <SelectItem value="AM">AM</SelectItem>
                        <SelectItem value="BA">BA</SelectItem>
                        <SelectItem value="CE">CE</SelectItem>
                        <SelectItem value="DF">DF</SelectItem>
                        <SelectItem value="ES">ES</SelectItem>
                        <SelectItem value="GO">GO</SelectItem>
                        <SelectItem value="MA">MA</SelectItem>
                        <SelectItem value="MT">MT</SelectItem>
                        <SelectItem value="MS">MS</SelectItem>
                        <SelectItem value="MG">MG</SelectItem>
                        <SelectItem value="PA">PA</SelectItem>
                        <SelectItem value="PB">PB</SelectItem>
                        <SelectItem value="PR">PR</SelectItem>
                        <SelectItem value="PE">PE</SelectItem>
                        <SelectItem value="PI">PI</SelectItem>
                        <SelectItem value="RJ">RJ</SelectItem>
                        <SelectItem value="RN">RN</SelectItem>
                        <SelectItem value="RS">RS</SelectItem>
                        <SelectItem value="RO">RO</SelectItem>
                        <SelectItem value="RR">RR</SelectItem>
                        <SelectItem value="SC">SC</SelectItem>
                        <SelectItem value="SP">SP</SelectItem>
                        <SelectItem value="SE">SE</SelectItem>
                        <SelectItem value="TO">TO</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="cidade">Cidade *</Label>
                  <Input
                    id="cidade"
                    value={formData.endereco.cidade}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      endereco: { ...prev.endereco, cidade: e.target.value }
                    }))}
                    placeholder="São Paulo"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Contatos */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  Contatos
                </CardTitle>
                <CardDescription>
                  Informações de contato da empresa
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="telefone">Telefone</Label>
                    <Input
                      id="telefone"
                      value={formData.contatos.telefone}
                      onChange={(e) => {
                        // Aplicar máscara de telefone
                        let value = e.target.value.replace(/\D/g, '')
                        value = value.replace(/^(\d{2})(\d)/, '($1) $2')
                        value = value.replace(/(\d{4})(\d)/, '$1-$2')
                        setFormData(prev => ({
                          ...prev,
                          contatos: { ...prev.contatos, telefone: value }
                        }))
                      }}
                      placeholder="(11) 1234-5678"
                      maxLength={14}
                    />
                  </div>
                  <div>
                    <Label htmlFor="celular">Celular</Label>
                    <Input
                      id="celular"
                      value={formData.contatos.celular}
                      onChange={(e) => {
                        // Aplicar máscara de celular
                        let value = e.target.value.replace(/\D/g, '')
                        value = value.replace(/^(\d{2})(\d)/, '($1) $2')
                        value = value.replace(/(\d{5})(\d)/, '$1-$2')
                        setFormData(prev => ({
                          ...prev,
                          contatos: { ...prev.contatos, celular: value }
                        }))
                      }}
                      placeholder="(11) 91234-5678"
                      maxLength={15}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.contatos.email}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        contatos: { ...prev.contatos, email: e.target.value }
                      }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="site">Site</Label>
                    <Input
                      id="site"
                      value={formData.contatos.site}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        contatos: { ...prev.contatos, site: e.target.value }
                      }))}
                      placeholder="https://www.empresa.com.br"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant={empresaExistente ? "default" : "secondary"} className="w-full justify-center">
                  {empresaExistente ? "Configurada" : "Não Configurada"}
                </Badge>
                {empresaExistente && (
                  <div className="mt-4 space-y-2 text-sm text-gray-600">
                    <p><strong>Criada em:</strong> {new Date(empresaExistente.createdAt).toLocaleDateString()}</p>
                    <p><strong>Última atualização:</strong> {new Date(empresaExistente.updatedAt).toLocaleDateString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Configurações de Jornada */}
            <Card>
              <CardHeader>
                <CardTitle>Configurações Padrão</CardTitle>
                <CardDescription>
                  Configurações que serão herdadas pelos projetos
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="toleranciaEntrada">Tolerância Entrada (min)</Label>
                  <Input
                    id="toleranciaEntrada"
                    type="number"
                    min="0"
                    max="60"
                    value={formData.configuracoes.toleranciaEntrada}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      configuracoes: { ...prev.configuracoes, toleranciaEntrada: parseInt(e.target.value) || 0 }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="toleranciaSaida">Tolerância Saída (min)</Label>
                  <Input
                    id="toleranciaSaida"
                    type="number"
                    min="0"
                    max="60"
                    value={formData.configuracoes.toleranciaSaida}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      configuracoes: { ...prev.configuracoes, toleranciaSaida: parseInt(e.target.value) || 0 }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="horasSemanais">Horas Semanais</Label>
                  <Input
                    id="horasSemanais"
                    type="number"
                    min="20"
                    max="60"
                    value={formData.configuracoes.horasSemanais}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      configuracoes: { ...prev.configuracoes, horasSemanais: parseInt(e.target.value) || 44 }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
