# 👥 MÓDULO DE FUNCIONÁRIOS E EPIs

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar cadastro completo de funcionários (47 campos em 6 etapas)
- Controlar EPIs (Equipamentos de Proteção Individual)
- Gerenciar documentos e fotos dos funcionários
- Integrar com APIs governamentais para validação
- Controlar entrega e devolução de EPIs

### **📊 RESPONSABILIDADES:**

- CRUD completo de funcionários
- Upload e gestão de documentos/fotos
- Catálogo e controle de EPIs
- Validação de dados com APIs externas
- Relatórios de funcionários e EPIs
- Integração com biometria

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. GESTÃO DE FUNCIONÁRIOS:**

- Cadastro em 6 etapas com 47 campos
- Upload de foto e documentos
- Validação automática de dados
- Integração com APIs (CEP, CPF, CNPJ)
- Histórico completo de alterações

#### **2. GESTÃO DE EPIs:**

- Catálogo com 9 categorias
- Controle de entrega/devolução
- Validade e certificações (CA)
- Relatórios de conformidade
- Alertas de vencimento

#### **3. DOCUMENTOS:**

- Upload múltiplo de arquivos
- Validação de tipos permitidos
- Compressão automática de imagens
- Backup seguro na nuvem
- Controle de versões

#### **4. VALIDAÇÕES:**

- CPF, CNPJ, PIS válidos
- CEP com autocompletar
- Email único no sistema
- Telefone com máscara
- Datas consistentes

---

## 📱 TELAS E INTERFACES

### **1. LISTAGEM DE FUNCIONÁRIOS** (`/funcionarios`)

```typescript
interface ListagemFuncionariosScreen {
  filtros_avancados: {
    busca_geral: {
      placeholder: 'Nome, CPF, matrícula ou email'
      busca_em: ['nome', 'cpf', 'matricula', 'email']
    }

    filtros_especificos: {
      empresa: {
        tipo: 'select'
        opcoes: Empresa[]
        multiplo: true
      }
      cargo: {
        tipo: 'select'
        opcoes: string[]
        multiplo: true
      }
      setor: {
        tipo: 'select'
        opcoes: string[]
      }
      status: {
        tipo: 'select'
        opcoes: ['ativo', 'inativo', 'afastado', 'demitido']
        padrao: 'ativo'
      }
      data_admissao: {
        tipo: 'daterange'
        label: 'Período de admissão'
      }
    }

    filtros_rapidos: [
      'Admitidos hoje',
      'Aniversariantes do mês',
      'Documentos vencendo',
      'EPIs pendentes',
      'Sem foto',
    ]
  }

  lista_funcionarios: {
    colunas: [
      {
        key: 'foto'
        label: ''
        width: '60px'
        render: 'Avatar com fallback'
      },
      {
        key: 'nome_completo'
        label: 'Nome'
        sortable: true
        searchable: true
      },
      {
        key: 'matricula'
        label: 'Matrícula'
        sortable: true
      },
      {
        key: 'cargo'
        label: 'Cargo'
        sortable: true
        filterable: true
      },
      {
        key: 'empresa_atual'
        label: 'Empresa/Projeto'
        render: 'Link para empresa'
      },
      {
        key: 'data_admissao'
        label: 'Admissão'
        sortable: true
        format: 'DD/MM/YYYY'
      },
      {
        key: 'status'
        label: 'Status'
        render: 'Badge colorido'
      },
      {
        key: 'acoes'
        label: 'Ações'
        width: '120px'
        render: 'Botões de ação'
      },
    ]

    acoes_linha: {
      visualizar: '/funcionarios/[id]'
      editar: '/funcionarios/[id]/editar'
      epis: '/funcionarios/[id]/epis'
      biometria: '/funcionarios/[id]/biometria'
      inativar: 'Modal de confirmação'
    }

    acoes_lote: {
      exportar_selecionados: 'Excel/PDF'
      alocar_empresa: 'Modal de alocação'
      enviar_email: 'Modal de email'
      gerar_crachas: 'PDF com QR codes'
    }
  }

  estatisticas_rapidas: {
    total_funcionarios: number
    ativos: number
    admitidos_mes: number
    aniversariantes_mes: number
    documentos_vencendo: number
    epis_pendentes: number
  }
}
```

### **2. CADASTRO DE FUNCIONÁRIO - ETAPA 1** (`/funcionarios/novo/dados-pessoais`)

```typescript
interface CadastroEtapa1Screen {
  dados_pessoais: {
    // Identificação
    nome_completo: {
      tipo: 'text'
      obrigatorio: true
      validacao: 'Mínimo 3 palavras'
      placeholder: 'Nome completo do funcionário'
    }
    nome_social: {
      tipo: 'text'
      opcional: true
      placeholder: 'Nome social (se aplicável)'
    }
    cpf: {
      tipo: 'text'
      mascara: 'XXX.XXX.XXX-XX'
      obrigatorio: true
      validacao: 'CPF válido + único no sistema'
      api_validacao: 'Receita Federal'
    }
    rg: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Número do RG'
    }
    orgao_expedidor: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'SSP/SP'
    }
    data_expedicao_rg: {
      tipo: 'date'
      validacao: 'Data no passado'
    }

    // Dados Pessoais
    data_nascimento: {
      tipo: 'date'
      obrigatorio: true
      validacao: 'Idade entre 16 e 70 anos'
    }
    sexo: {
      tipo: 'select'
      opcoes: ['Masculino', 'Feminino', 'Outro']
      obrigatorio: true
    }
    estado_civil: {
      tipo: 'select'
      opcoes: [
        'Solteiro(a)',
        'Casado(a)',
        'Divorciado(a)',
        'Viúvo(a)',
        'União Estável',
      ]
      obrigatorio: true
    }
    nacionalidade: {
      tipo: 'text'
      padrao: 'Brasileira'
      obrigatorio: true
    }
    naturalidade: {
      tipo: 'text'
      placeholder: 'Cidade/UF de nascimento'
      obrigatorio: true
    }

    // Filiação
    nome_pai: {
      tipo: 'text'
      placeholder: 'Nome completo do pai'
    }
    nome_mae: {
      tipo: 'text'
      obrigatorio: true
      placeholder: 'Nome completo da mãe'
    }

    // Características Físicas
    cor_raca: {
      tipo: 'select'
      opcoes: [
        'Branca',
        'Preta',
        'Parda',
        'Amarela',
        'Indígena',
        'Não informado',
      ]
    }
    tipo_sanguineo: {
      tipo: 'select'
      opcoes: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
    }

    // Deficiências
    possui_deficiencia: {
      tipo: 'checkbox'
      label: 'Possui alguma deficiência'
    }
    tipo_deficiencia: {
      tipo: 'select'
      multiplo: true
      condicional: 'possui_deficiencia'
      opcoes: ['Física', 'Visual', 'Auditiva', 'Intelectual', 'Múltipla']
    }
    descricao_deficiencia: {
      tipo: 'textarea'
      condicional: 'possui_deficiencia'
      placeholder: 'Descreva a deficiência'
    }
  }

  upload_foto: {
    foto_funcionario: {
      tipo: 'file'
      aceita: ['image/jpeg', 'image/png']
      tamanho_max: '5MB'
      dimensoes: 'Mínimo 300x300px'
      preview: true
      crop: 'Quadrado 1:1'
    }
  }

  navegacao: {
    proximo: '/funcionarios/novo/endereco'
    salvar_rascunho: 'Salvar e continuar depois'
    cancelar: '/funcionarios'
  }
}
```

### **3. GESTÃO DE EPIs** (`/funcionarios/[id]/epis`)

```typescript
interface GestaoEPIsScreen {
  funcionario_info: {
    nome: string
    matricula: string
    cargo: string
    empresa_atual: string
    foto: string
  }

  epis_entregues: {
    lista_atual: {
      epi: {
        nome: string
        categoria: string
        numero_ca: string
        foto: string
      }
      data_entrega: Date
      quantidade: number
      validade_ca: Date
      status: 'em_uso' | 'devolvido' | 'vencido' | 'danificado'
      observacoes: string
      entregue_por: string
      acoes: ['devolver', 'renovar', 'relatar_dano']
    }[]

    resumo: {
      total_epis: number
      em_uso: number
      vencendo_30_dias: number
      vencidos: number
    }
  }

  nova_entrega: {
    buscar_epi: {
      filtros: {
        categoria: string
        nome: string
        disponivel: boolean
      }
      resultados: {
        epi: EPI
        estoque_disponivel: number
        validade_ca: Date
        preco_unitario: number
      }[]
    }

    dados_entrega: {
      epi_id: UUID
      quantidade: {
        tipo: 'number'
        min: 1
        max: 'estoque_disponivel'
      }
      observacoes: {
        tipo: 'textarea'
        placeholder: 'Observações sobre a entrega'
      }
      assinatura_funcionario: {
        tipo: 'signature'
        obrigatorio: true
        label: 'Assinatura do funcionário'
      }
    }
  }

  historico_epis: {
    filtros: {
      periodo: DateRange
      categoria: string
      status: string
    }

    timeline: {
      data: Date
      acao: 'entrega' | 'devolucao' | 'vencimento' | 'dano'
      epi: string
      quantidade: number
      responsavel: string
      observacoes: string
    }[]
  }

  relatorios_epis: {
    relatorio_individual: 'PDF com histórico completo'
    termo_entrega: 'PDF para assinatura'
    relatorio_vencimentos: 'EPIs próximos ao vencimento'
    custo_epis_funcionario: 'Relatório financeiro'
  }
}
```

### **4. CATÁLOGO DE EPIs** (`/epis`)

```typescript
interface CatalogoEPIsScreen {
  categorias_epis: {
    'Proteção da Cabeça': {
      icone: 'hard-hat'
      cor: '#ff6b35'
      epis: ['Capacete', 'Capuz', 'Balaclava']
    }
    'Proteção dos Olhos': {
      icone: 'eye'
      cor: '#4ecdc4'
      epis: ['Óculos de Segurança', 'Protetor Facial']
    }
    'Proteção Auditiva': {
      icone: 'ear'
      cor: '#45b7d1'
      epis: ['Protetor Auricular', 'Abafador de Ruído']
    }
    'Proteção Respiratória': {
      icone: 'lungs'
      cor: '#96ceb4'
      epis: ['Máscara PFF2', 'Respirador', 'Máscara Química']
    }
    'Proteção das Mãos': {
      icone: 'hand'
      cor: '#feca57'
      epis: ['Luva de Segurança', 'Luva Química', 'Luva Térmica']
    }
    'Proteção dos Pés': {
      icone: 'shoe'
      cor: '#ff9ff3'
      epis: ['Botina de Segurança', 'Bota Impermeável']
    }
    'Proteção do Corpo': {
      icone: 'shirt'
      cor: '#54a0ff'
      epis: ['Colete Refletivo', 'Avental', 'Macacão']
    }
    'Proteção Contra Quedas': {
      icone: 'anchor'
      cor: '#5f27cd'
      epis: ['Cinto de Segurança', 'Trava Quedas']
    }
    Sinalização: {
      icone: 'triangle-alert'
      cor: '#ff6348'
      epis: ['Cone', 'Fita Zebrada', 'Placa de Sinalização']
    }
  }

  lista_epis: {
    filtros: {
      categoria: string
      nome: string
      numero_ca: string
      validade_ca: DateRange
      ativo: boolean
    }

    colunas: [
      'foto',
      'nome',
      'categoria',
      'numero_ca',
      'validade_ca',
      'estoque',
      'preco_unitario',
      'status',
      'acoes',
    ]

    acoes_linha: {
      editar: '/epis/[id]/editar'
      historico: '/epis/[id]/historico'
      estoque: '/epis/[id]/estoque'
      inativar: 'Modal de confirmação'
    }
  }

  cadastro_epi: {
    dados_basicos: {
      nome: {
        tipo: 'text'
        obrigatorio: true
        placeholder: 'Nome do EPI'
      }
      categoria: {
        tipo: 'select'
        opcoes: '9 categorias'
        obrigatorio: true
      }
      descricao: {
        tipo: 'textarea'
        placeholder: 'Descrição detalhada'
      }
      numero_ca: {
        tipo: 'text'
        obrigatorio: true
        validacao: 'Formato CA válido'
        placeholder: '12345'
      }
      validade_ca: {
        tipo: 'date'
        obrigatorio: true
        validacao: 'Data futura'
      }
    }

    dados_comerciais: {
      fornecedor: {
        tipo: 'text'
        placeholder: 'Nome do fornecedor'
      }
      preco_unitario: {
        tipo: 'currency'
        placeholder: 'R$ 0,00'
      }
      estoque_minimo: {
        tipo: 'number'
        padrao: 10
      }
      estoque_atual: {
        tipo: 'number'
        padrao: 0
      }
    }

    upload_foto: {
      foto_epi: {
        tipo: 'file'
        aceita: ['image/jpeg', 'image/png']
        tamanho_max: '2MB'
        preview: true
      }
    }
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface FuncionariosEPIsAPI {
  // ✅ FUNCIONÁRIOS
  'GET /api/funcionarios': {
    query: {
      search?: string
      empresa_id?: UUID
      cargo?: string
      status?: 'ativo' | 'inativo'
      page?: number
      limit?: number
    }
    response: {
      funcionarios: Funcionario[]
      total: number
      page: number
      totalPages: number
    }
  }

  'POST /api/funcionarios': {
    body: FuncionarioData
    response: Funcionario
  }

  'GET /api/funcionarios/[id]': {
    response: FuncionarioCompleto
  }

  'PUT /api/funcionarios/[id]': {
    body: Partial<FuncionarioData>
    response: Funcionario
  }

  'POST /api/funcionarios/[id]/foto': {
    body: FormData // multipart/form-data
    response: { foto_url: string }
  }

  'POST /api/funcionarios/[id]/documentos': {
    body: FormData
    response: { documento_id: UUID; url: string }
  }

  // ✅ EPIs
  'GET /api/epis': {
    query: {
      categoria?: string
      ativo?: boolean
      search?: string
    }
    response: EPI[]
  }

  'POST /api/epis': {
    body: EPIData
    response: EPI
  }

  'GET /api/funcionarios/[id]/epis': {
    response: {
      epis_entregues: EPIEntrega[]
      historico: EPIHistorico[]
      resumo: EPIResumo
    }
  }

  'POST /api/funcionarios/[id]/epis/entregar': {
    body: {
      epi_id: UUID
      quantidade: number
      observacoes?: string
      assinatura_funcionario: string // base64
    }
    response: EPIEntrega
  }

  'POST /api/funcionarios/[id]/epis/[entrega_id]/devolver': {
    body: {
      quantidade: number
      motivo: string
      estado_conservacao: 'bom' | 'regular' | 'ruim'
    }
    response: { message: 'EPI devolvido' }
  }

  // ✅ VALIDAÇÕES EXTERNAS
  'POST /api/validacoes/cpf': {
    body: { cpf: string }
    response: { valido: boolean; nome?: string }
  }

  'POST /api/validacoes/cep': {
    body: { cep: string }
    response: {
      logradouro: string
      bairro: string
      cidade: string
      uf: string
    }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS PRINCIPAIS:**

```sql
-- ✅ FUNCIONÁRIOS (47 campos)
CREATE TABLE funcionarios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  empresa_id UUID NOT NULL REFERENCES empresas(id),

  -- Dados Pessoais (Etapa 1)
  nome_completo VARCHAR(255) NOT NULL,
  nome_social VARCHAR(255),
  cpf VARCHAR(14) UNIQUE NOT NULL,
  rg VARCHAR(20) NOT NULL,
  orgao_expedidor VARCHAR(10),
  data_expedicao_rg DATE,
  data_nascimento DATE NOT NULL,
  sexo VARCHAR(20) NOT NULL,
  estado_civil VARCHAR(30),
  nacionalidade VARCHAR(50) DEFAULT 'Brasileira',
  naturalidade VARCHAR(100),
  nome_pai VARCHAR(255),
  nome_mae VARCHAR(255) NOT NULL,
  cor_raca VARCHAR(20),
  tipo_sanguineo VARCHAR(5),
  possui_deficiencia BOOLEAN DEFAULT FALSE,
  tipo_deficiencia JSONB,
  descricao_deficiencia TEXT,

  -- Endereço (Etapa 2)
  endereco JSONB NOT NULL,

  -- Contatos (Etapa 3)
  telefone_principal VARCHAR(20),
  telefone_secundario VARCHAR(20),
  email VARCHAR(255),
  contato_emergencia JSONB,

  -- Documentos (Etapa 4)
  documentos JSONB,

  -- Dados Profissionais (Etapa 5)
  matricula VARCHAR(50) UNIQUE,
  cargo VARCHAR(100) NOT NULL,
  setor VARCHAR(100),
  data_admissao DATE NOT NULL,
  data_demissao DATE,
  salario DECIMAL(10,2),
  jornada_trabalho JSONB,

  -- Dados Bancários (Etapa 6)
  dados_bancarios JSONB,

  -- Arquivos
  foto_url VARCHAR(500),
  documentos_urls JSONB,

  -- Biometria
  template_biometrico TEXT,
  qualidade_template INTEGER,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ EPIs
CREATE TABLE epis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Dados Básicos
  nome VARCHAR(255) NOT NULL,
  categoria VARCHAR(100) NOT NULL,
  descricao TEXT,
  numero_ca VARCHAR(20) NOT NULL,
  validade_ca DATE NOT NULL,

  -- Dados Comerciais
  fornecedor VARCHAR(255),
  preco_unitario DECIMAL(10,2),
  estoque_minimo INTEGER DEFAULT 10,
  estoque_atual INTEGER DEFAULT 0,

  -- Arquivos
  foto_url VARCHAR(500),

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_categoria_epi CHECK (
    categoria IN (
      'Proteção da Cabeça', 'Proteção dos Olhos', 'Proteção Auditiva',
      'Proteção Respiratória', 'Proteção das Mãos', 'Proteção dos Pés',
      'Proteção do Corpo', 'Proteção Contra Quedas', 'Sinalização'
    )
  )
);

-- ✅ ENTREGA DE EPIs
CREATE TABLE funcionario_epis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  epi_id UUID NOT NULL REFERENCES epis(id),

  -- Entrega
  data_entrega DATE NOT NULL,
  quantidade INTEGER NOT NULL DEFAULT 1,
  observacoes TEXT,
  assinatura_funcionario TEXT, -- base64
  entregue_por UUID REFERENCES users(id),

  -- Devolução
  data_devolucao DATE,
  quantidade_devolvida INTEGER,
  motivo_devolucao TEXT,
  estado_conservacao VARCHAR(20),
  recebido_por UUID REFERENCES users(id),

  -- Status
  status VARCHAR(20) DEFAULT 'entregue',

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_status_epi CHECK (
    status IN ('entregue', 'devolvido', 'vencido', 'danificado', 'perdido')
  ),
  CONSTRAINT chk_quantidade_devolucao CHECK (
    quantidade_devolvida IS NULL OR quantidade_devolvida <= quantidade
  )
);

-- ✅ ÍNDICES
CREATE INDEX idx_funcionarios_cpf ON funcionarios(cpf);
CREATE INDEX idx_funcionarios_empresa ON funcionarios(empresa_id);
CREATE INDEX idx_funcionarios_ativo ON funcionarios(ativo);
CREATE INDEX idx_funcionarios_nome ON funcionarios(nome_completo);
CREATE INDEX idx_epis_categoria ON epis(categoria);
CREATE INDEX idx_epis_numero_ca ON epis(numero_ca);
CREATE INDEX idx_funcionario_epis_funcionario ON funcionario_epis(funcionario_id);
CREATE INDEX idx_funcionario_epis_status ON funcionario_epis(status);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **👥 FUNCIONÁRIOS:**

1. **Validações Obrigatórias:**
   - CPF único e válido
   - Email único (se informado)
   - Idade entre 16 e 70 anos
   - Matrícula única por empresa

2. **Upload de Arquivos:**
   - Foto: máximo 5MB, formatos JPG/PNG
   - Documentos: máximo 10MB, formatos PDF/JPG/PNG
   - Compressão automática de imagens
   - Backup em nuvem obrigatório

3. **Integração com APIs:**
   - CEP: autocompletar endereço
   - CPF: validação na Receita Federal
   - PIS: validação no CNIS

### **🦺 EPIs:**

1. **Controle de Estoque:**
   - Estoque não pode ficar negativo
   - Alerta quando atingir estoque mínimo
   - Bloqueio de entrega sem estoque

2. **Validade CA:**
   - Não permitir entrega de EPI vencido
   - Alerta 30 dias antes do vencimento
   - Relatório mensal de vencimentos

3. **Entrega e Devolução:**
   - Assinatura obrigatória na entrega
   - Controle de quantidade entregue/devolvida
   - Histórico completo mantido

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE FUNCIONÁRIOS:**

```typescript
describe('Gestão de Funcionários', () => {
  test('Cadastrar funcionário completo', async () => {
    const funcionario = await cadastrarFuncionario({
      nome_completo: 'João Silva',
      cpf: '123.456.789-00',
      data_nascimento: '1990-01-01',
    })
    expect(funcionario.id).toBeDefined()
    expect(funcionario.cpf).toBe('12345678900')
  })

  test('Não permitir CPF duplicado', async () => {
    await cadastrarFuncionario({ cpf: '123.456.789-00' })

    await expect(
      cadastrarFuncionario({
        cpf: '123.456.789-00',
      })
    ).rejects.toThrow('CPF já cadastrado')
  })
})
```

### **✅ TESTES DE EPIs:**

```typescript
describe('Gestão de EPIs', () => {
  test('Entregar EPI para funcionário', async () => {
    const funcionario = await criarFuncionario()
    const epi = await criarEPI({ estoque_atual: 10 })

    const entrega = await entregarEPI({
      funcionario_id: funcionario.id,
      epi_id: epi.id,
      quantidade: 2,
    })

    expect(entrega.status).toBe('entregue')

    const epiAtualizado = await buscarEPI(epi.id)
    expect(epiAtualizado.estoque_atual).toBe(8)
  })

  test('Não permitir entrega sem estoque', async () => {
    const epi = await criarEPI({ estoque_atual: 0 })

    await expect(
      entregarEPI({
        epi_id: epi.id,
        quantidade: 1,
      })
    ).rejects.toThrow('Estoque insuficiente')
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**

- Modelo de dados funcionários
- APIs básicas CRUD
- Validações de CPF/email

**Semana 3-4: Cadastro Multi-etapas**

- 6 telas de cadastro
- Upload de arquivos
- Integração APIs externas

**Semana 5-6: Gestão de EPIs**

- Catálogo de EPIs
- Sistema de entrega/devolução
- Controle de estoque

**Semana 7-8: Funcionalidades Avançadas**

- Relatórios
- Histórico completo
- Testes e otimizações

### **🔧 DEPENDÊNCIAS:**

- Módulo de Empresas (alocação)
- ViaCEP API (endereços)
- Receita Federal API (CPF)
- AWS S3 (upload de arquivos)
- Sharp (compressão de imagens)

---

## 📊 MÉTRICAS E KPIs

### **📈 INDICADORES DE FUNCIONÁRIOS:**

- Taxa de rotatividade mensal
- Tempo médio de cadastro completo
- Percentual de documentos em dia
- Funcionários sem foto cadastrada

### **🦺 INDICADORES DE EPIs:**

- Conformidade de entrega (%)
- EPIs próximos ao vencimento
- Custo médio de EPI por funcionário
- Taxa de devolução por estado

### **🎯 METAS:**

- 100% funcionários com foto
- 95% conformidade EPIs
- Cadastro completo em < 15 minutos
- 0 EPIs vencidos em uso
