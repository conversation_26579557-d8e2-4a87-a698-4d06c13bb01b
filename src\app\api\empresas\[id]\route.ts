import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para atualização de empresa
const updateEmpresaSchema = z.object({
  nomeFantasia: z.string().min(1).optional(),
  razaoSocial: z.string().min(1).optional(),
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/).optional(),
  inscricaoEstadual: z.string().optional(),
  nomeProjeto: z.string().optional(),
  codigoProjeto: z.string().optional(),
  descricao: z.string().optional(),
  endereco: z.object({
    cep: z.string().optional(),
    logradouro: z.string().optional(),
    numero: z.string().optional(),
    complemento: z.string().optional(),
    bairro: z.string().optional(),
    cidade: z.string().optional(),
    estado: z.string().optional()
  }).optional(),
  contatos: z.object({
    telefone: z.string().optional(),
    email: z.string().email().optional(),
    responsavel: z.string().optional()
  }).optional(),
  dataInicio: z.string().datetime().optional(),
  dataFimPrevista: z.string().datetime().optional(),
  dataFimReal: z.string().datetime().optional(),
  configuracoes: z.record(z.any()).optional(),
  tolerancias: z.record(z.any()).optional(),
  configuracoesFin: z.record(z.any()).optional(),
  ativo: z.boolean().optional()
})

// GET /api/empresas/[id] - Obter empresa específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id },
      include: {
        empresaPrincipal: {
          select: {
            id: true,
            nomeFantasia: true,
            configuracoes: true,
            tolerancias: true
          }
        },
        empresasClientes: {
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true,
            ativo: true,
            dataInicio: true,
            dataFimPrevista: true
          },
          where: { ativo: true }
        },
        funcionarios: {
          where: { ativo: true },
          select: {
            id: true,
            nomeCompleto: true,
            cargo: true,
            dataAdmissao: true
          }
        },
        funcionarioAlocacoes: {
          where: { status: 'ativo' },
          include: {
            funcionario: {
              select: {
                id: true,
                nomeCompleto: true,
                cargo: true
              }
            }
          }
        },
        _count: {
          select: {
            funcionarios: {
              where: { ativo: true }
            },
            funcionarioAlocacoes: {
              where: { status: 'ativo' }
            }
          }
        }
      }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    return NextResponse.json(empresa)

  } catch (error) {
    console.error('Erro ao buscar empresa:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// PUT /api/empresas/[id] - Atualizar empresa
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Verificar se empresa existe
    const empresaExistente = await prisma.empresa.findUnique({
      where: { id: params.id }
    })

    if (!empresaExistente) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = updateEmpresaSchema.parse(body)

    // Se alterando CNPJ, verificar se não existe outro
    if (data.cnpj && data.cnpj !== empresaExistente.cnpj) {
      const existeCnpj = await prisma.empresa.findUnique({
        where: { cnpj: data.cnpj }
      })

      if (existeCnpj) {
        return NextResponse.json(
          { error: "CNPJ já cadastrado" },
          { status: 400 }
        )
      }
    }

    // Preparar dados para atualização
    const updateData: any = { ...data }

    // Converter datas
    if (data.dataInicio) {
      updateData.dataInicio = new Date(data.dataInicio)
    }
    if (data.dataFimPrevista) {
      updateData.dataFimPrevista = new Date(data.dataFimPrevista)
    }
    if (data.dataFimReal) {
      updateData.dataFimReal = new Date(data.dataFimReal)
    }

    // Atualizar empresa
    const empresaAtualizada = await prisma.empresa.update({
      where: { id: params.id },
      data: updateData,
      include: {
        empresaPrincipal: {
          select: {
            id: true,
            nomeFantasia: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "UPDATE_EMPRESA",
        tabelaAfetada: "empresas",
        registroId: params.id,
        dadosAnteriores: empresaExistente,
        dadosNovos: empresaAtualizada,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(empresaAtualizada)

  } catch (error) {
    console.error('Erro ao atualizar empresa:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// DELETE /api/empresas/[id] - Inativar empresa
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Apenas admin total pode inativar empresas" },
        { status: 403 }
      )
    }

    // Verificar se empresa existe
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id },
      include: {
        funcionarios: {
          where: { ativo: true }
        },
        funcionarioAlocacoes: {
          where: { status: 'ativo' }
        }
      }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    // Não permitir inativar empresa principal
    if (empresa.tipoEmpresa === 'principal') {
      return NextResponse.json(
        { error: "Não é possível inativar a empresa principal" },
        { status: 400 }
      )
    }

    // Verificar se tem funcionários ativos ou alocações ativas
    if (empresa.funcionarios.length > 0 || empresa.funcionarioAlocacoes.length > 0) {
      return NextResponse.json(
        { 
          error: "Não é possível inativar empresa com funcionários ou alocações ativas",
          details: {
            funcionarios_ativos: empresa.funcionarios.length,
            alocacoes_ativas: empresa.funcionarioAlocacoes.length
          }
        },
        { status: 400 }
      )
    }

    // Inativar empresa
    const empresaInativada = await prisma.empresa.update({
      where: { id: params.id },
      data: { 
        ativo: false,
        dataFimReal: new Date()
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "DELETE_EMPRESA",
        tabelaAfetada: "empresas",
        registroId: params.id,
        dadosAnteriores: empresa,
        dadosNovos: empresaInativada,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({ 
      message: "Empresa inativada com sucesso",
      empresa: empresaInativada
    })

  } catch (error) {
    console.error('Erro ao inativar empresa:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
