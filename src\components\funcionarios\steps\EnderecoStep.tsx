"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { MapPin, Search, Loader2 } from "lucide-react"

interface EnderecoStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function EnderecoStep({ data, onUpdate }: EnderecoStepProps) {
  const [formData, setFormData] = useState({
    cep: data.cep || '',
    logradouro: data.logradouro || '',
    numero: data.numero || '',
    complemento: data.complemento || '',
    bairro: data.bairro || '',
    cidade: data.cidade || '',
    estado: data.estado || '',
    pais: data.pais || 'Brasil'
  })

  const [loadingCep, setLoadingCep] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Estados brasileiros
  const estados = [
    { value: 'AC', label: 'Acre' },
    { value: 'AL', label: 'Alagoas' },
    { value: 'AP', label: 'Amapá' },
    { value: 'AM', label: 'Amazonas' },
    { value: 'BA', label: 'Bahia' },
    { value: 'CE', label: 'Ceará' },
    { value: 'DF', label: 'Distrito Federal' },
    { value: 'ES', label: 'Espírito Santo' },
    { value: 'GO', label: 'Goiás' },
    { value: 'MA', label: 'Maranhão' },
    { value: 'MT', label: 'Mato Grosso' },
    { value: 'MS', label: 'Mato Grosso do Sul' },
    { value: 'MG', label: 'Minas Gerais' },
    { value: 'PA', label: 'Pará' },
    { value: 'PB', label: 'Paraíba' },
    { value: 'PR', label: 'Paraná' },
    { value: 'PE', label: 'Pernambuco' },
    { value: 'PI', label: 'Piauí' },
    { value: 'RJ', label: 'Rio de Janeiro' },
    { value: 'RN', label: 'Rio Grande do Norte' },
    { value: 'RS', label: 'Rio Grande do Sul' },
    { value: 'RO', label: 'Rondônia' },
    { value: 'RR', label: 'Roraima' },
    { value: 'SC', label: 'Santa Catarina' },
    { value: 'SP', label: 'São Paulo' },
    { value: 'SE', label: 'Sergipe' },
    { value: 'TO', label: 'Tocantins' }
  ]

  // Formatar CEP
  const formatarCEP = (value: string) => {
    const cep = value.replace(/\D/g, '')
    return cep.replace(/(\d{5})(\d{3})/, '$1-$2')
  }

  // Buscar CEP na API ViaCEP
  const buscarCEP = async (cep: string) => {
    const cepLimpo = cep.replace(/\D/g, '')
    
    if (cepLimpo.length !== 8) {
      setErrors(prev => ({ ...prev, cep: 'CEP deve ter 8 dígitos' }))
      return
    }

    setLoadingCep(true)
    setErrors(prev => ({ ...prev, cep: '' }))

    try {
      const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`)
      const data = await response.json()

      if (data.erro) {
        setErrors(prev => ({ ...prev, cep: 'CEP não encontrado' }))
        return
      }

      // Atualizar campos automaticamente
      const newFormData = {
        ...formData,
        cep: cepLimpo,
        logradouro: data.logradouro || '',
        bairro: data.bairro || '',
        cidade: data.localidade || '',
        estado: data.uf || ''
      }

      setFormData(newFormData)
      onUpdate(newFormData)

    } catch (error) {
      console.error('Erro ao buscar CEP:', error)
      setErrors(prev => ({ ...prev, cep: 'Erro ao buscar CEP' }))
    } finally {
      setLoadingCep(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    const newFormData = { ...formData, [field]: value }
    setFormData(newFormData)
    onUpdate(newFormData)

    // Validações
    if (field === 'cep') {
      const cepLimpo = value.replace(/\D/g, '')
      if (cepLimpo.length === 8) {
        buscarCEP(cepLimpo)
      }
    }

    // Limpar erros quando campo é preenchido
    if (errors[field] && value.trim()) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleCepBlur = () => {
    const cepLimpo = formData.cep.replace(/\D/g, '')
    if (cepLimpo.length === 8) {
      buscarCEP(cepLimpo)
    }
  }

  const enderecoCompleto = `${formData.logradouro}${formData.numero ? ', ' + formData.numero : ''}${formData.complemento ? ', ' + formData.complemento : ''} - ${formData.bairro}, ${formData.cidade}/${formData.estado}`

  return (
    <div className="space-y-6">
      {/* Localização */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Endereço Residencial
          </CardTitle>
          <CardDescription>
            Endereço completo do funcionário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* CEP */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="cep">CEP</Label>
              <div className="flex gap-2">
                <Input
                  id="cep"
                  value={formatarCEP(formData.cep)}
                  onChange={(e) => handleChange('cep', e.target.value)}
                  onBlur={handleCepBlur}
                  placeholder="00000-000"
                  maxLength={9}
                  className={errors.cep ? 'border-red-500' : ''}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => buscarCEP(formData.cep)}
                  disabled={loadingCep || formData.cep.replace(/\D/g, '').length !== 8}
                >
                  {loadingCep ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.cep && (
                <p className="text-sm text-red-500 mt-1">{errors.cep}</p>
              )}
            </div>

            <div>
              <Label htmlFor="estado">Estado</Label>
              <Select value={formData.estado} onValueChange={(value) => handleChange('estado', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o estado" />
                </SelectTrigger>
                <SelectContent>
                  {estados.map((estado) => (
                    <SelectItem key={estado.value} value={estado.value}>
                      {estado.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="cidade">Cidade</Label>
              <Input
                id="cidade"
                value={formData.cidade}
                onChange={(e) => handleChange('cidade', e.target.value)}
                placeholder="Nome da cidade"
              />
            </div>
          </div>

          {/* Endereço */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="logradouro">Logradouro</Label>
              <Input
                id="logradouro"
                value={formData.logradouro}
                onChange={(e) => handleChange('logradouro', e.target.value)}
                placeholder="Rua, Avenida, Travessa..."
              />
            </div>

            <div>
              <Label htmlFor="numero">Número</Label>
              <Input
                id="numero"
                value={formData.numero}
                onChange={(e) => handleChange('numero', e.target.value)}
                placeholder="Número da residência"
              />
            </div>

            <div>
              <Label htmlFor="complemento">Complemento</Label>
              <Input
                id="complemento"
                value={formData.complemento}
                onChange={(e) => handleChange('complemento', e.target.value)}
                placeholder="Apto, Bloco, Casa..."
              />
            </div>

            <div>
              <Label htmlFor="bairro">Bairro</Label>
              <Input
                id="bairro"
                value={formData.bairro}
                onChange={(e) => handleChange('bairro', e.target.value)}
                placeholder="Nome do bairro"
              />
            </div>

            <div>
              <Label htmlFor="pais">País</Label>
              <Input
                id="pais"
                value={formData.pais}
                onChange={(e) => handleChange('pais', e.target.value)}
                placeholder="Brasil"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resumo do Endereço */}
      {(formData.logradouro || formData.cidade) && (
        <Card>
          <CardHeader>
            <CardTitle>Endereço Completo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-gray-900">
                {enderecoCompleto}
              </p>
              {formData.cep && (
                <p className="text-sm text-gray-600 mt-1">
                  CEP: {formatarCEP(formData.cep)}
                </p>
              )}
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              {formData.cep && (
                <Badge variant="outline">CEP: {formatarCEP(formData.cep)}</Badge>
              )}
              {formData.cidade && formData.estado && (
                <Badge variant="outline">{formData.cidade}/{formData.estado}</Badge>
              )}
              {formData.bairro && (
                <Badge variant="outline">Bairro: {formData.bairro}</Badge>
              )}
              {formData.pais && (
                <Badge variant="outline">{formData.pais}</Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dicas */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Dicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Digite o CEP para preenchimento automático do endereço</li>
            <li>• Verifique se todos os dados estão corretos antes de prosseguir</li>
            <li>• O endereço será usado para correspondências e documentos</li>
            <li>• Em caso de mudança, atualize os dados no sistema</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
