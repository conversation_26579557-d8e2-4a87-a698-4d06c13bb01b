# ⚙️ MÓDULO DE CONFIGURAÇÕES

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar configurações hierárquicas do sistema
- Controlar parâmetros operacionais
- Administrar usuários e permissões
- Configurar integrações externas
- Manter backup e auditoria de alterações

### **📊 RESPONSABILIDADES:**

- Configurações de sistema, empresa e usuário
- Gestão de usuários e níveis de acesso
- Parâmetros de tolerâncias e limites
- Configurações de email e notificações
- Integrações com APIs externas
- Backup automático e manual

---

## 🚀 FUNCIONALIDADES

### **✅ HIERARQUIA DE CONFIGURAÇÕES:**

#### **1. NÍVEL SISTEMA (admin_total):**

- Configurações globais do RLPONTO
- Parâmetros de segurança
- Configurações de backup
- Integrações principais

#### **2. NÍVEL EMPRESA PRINCIPAL (admin_limitado):**

- Configurações da construtora
- Parâmetros padrão para clientes
- Jornadas e tolerâncias base
- Configurações de relatórios

#### **3. NÍVEL EMPRESA CLIENTE (gerente_rh):**

- Configurações específicas do projeto
- Customizações de jornada
- Parâmetros locais
- Configurações de dispositivos

#### **4. NÍVEL USUÁRIO (todos):**

- Preferências pessoais
- Configurações de interface
- Notificações individuais
- Temas e idioma

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE CONFIGURAÇÕES** (`/configuracoes`)

```typescript
interface DashboardConfiguracoesScreen {
  categorias_configuracao: {
    sistema: {
      icone: 'server'
      cor: '#e74c3c'
      nivel_acesso: 'admin_total'
      configuracoes: [
        'Configurações Globais',
        'Segurança e Auditoria',
        'Backup e Manutenção',
        'Integrações Principais',
        'Licenciamento',
      ]
    }

    empresa_principal: {
      icone: 'building'
      cor: '#3498db'
      nivel_acesso: 'admin_limitado'
      configuracoes: [
        'Dados da Empresa',
        'Jornadas Padrão',
        'Tolerâncias Base',
        'Configurações de Email',
        'Relatórios Padrão',
      ]
    }

    usuarios_permissoes: {
      icone: 'users'
      cor: '#9b59b6'
      nivel_acesso: 'admin_limitado'
      configuracoes: [
        'Gestão de Usuários',
        'Níveis de Acesso',
        'Perfis de Permissão',
        'Auditoria de Usuários',
      ]
    }

    parametros_sistema: {
      icone: 'sliders'
      cor: '#f39c12'
      nivel_acesso: 'gerente_rh'
      configuracoes: [
        'Tolerâncias de Ponto',
        'Limites de Horas Extras',
        'Configurações de Turno',
        'Parâmetros de Cálculo',
      ]
    }

    integracoes: {
      icone: 'link'
      cor: '#1abc9c'
      nivel_acesso: 'admin_limitado'
      configuracoes: [
        'APIs Externas',
        'Webhooks',
        'Sincronizações',
        'Conectores',
      ]
    }

    preferencias_usuario: {
      icone: 'user-cog'
      cor: '#95a5a6'
      nivel_acesso: 'todos'
      configuracoes: [
        'Perfil Pessoal',
        'Preferências de Interface',
        'Notificações',
        'Tema e Idioma',
      ]
    }
  }

  status_sistema: {
    versao_atual: string
    ultima_atualizacao: Date
    backup_automatico: {
      ativo: boolean
      ultimo_backup: Date
      proximo_backup: Date
    }
    integracao_status: {
      email: 'conectado' | 'erro' | 'desabilitado'
      apis_externas: 'conectado' | 'erro' | 'desabilitado'
      backup_nuvem: 'conectado' | 'erro' | 'desabilitado'
    }
  }

  alertas_configuracao: {
    configuracoes_pendentes: number
    usuarios_sem_permissao: number
    integracao_com_erro: number
    backup_atrasado: boolean
  }
}
```

### **2. GESTÃO DE USUÁRIOS** (`/configuracoes/usuarios`)

```typescript
interface GestaoUsuariosScreen {
  lista_usuarios: {
    filtros: {
      nivel_acesso: NivelAcesso[]
      empresa: UUID[]
      status: 'ativo' | 'inativo' | 'bloqueado'
      ultimo_login: DateRange
    }

    colunas: [
      'foto',
      'nome',
      'email',
      'nivel_acesso',
      'empresa',
      'ultimo_login',
      'status',
      'acoes',
    ]

    acoes_linha: {
      editar: '/configuracoes/usuarios/[id]/editar'
      alterar_permissoes: 'Modal de permissões'
      resetar_senha: 'Enviar email de reset'
      bloquear_desbloquear: 'Toggle status'
      historico_acesso: '/configuracoes/usuarios/[id]/historico'
    }

    acoes_lote: {
      alterar_nivel_acesso: 'Modal de alteração em massa'
      enviar_notificacao: 'Modal de notificação'
      exportar_lista: 'Excel com dados dos usuários'
      bloquear_selecionados: 'Bloqueio em massa'
    }
  }

  cadastro_usuario: {
    dados_pessoais: {
      nome: {
        tipo: 'text'
        obrigatorio: true
        placeholder: 'Nome completo'
      }
      email: {
        tipo: 'email'
        obrigatorio: true
        validacao: 'Email único no sistema'
      }
      telefone: {
        tipo: 'tel'
        mascara: '(XX) XXXXX-XXXX'
      }
    }

    configuracao_acesso: {
      nivel_acesso: {
        tipo: 'select'
        opcoes: NivelAcesso[]
        obrigatorio: true
        descricao_niveis: {
          [key in NivelAcesso]: string
        }
      }
      empresa_vinculada: {
        tipo: 'select'
        opcoes: 'Empresas disponíveis'
        condicional: "nivel_acesso !== 'admin_total'"
      }
      senha_temporaria: {
        tipo: 'password'
        gerada_automaticamente: boolean
        enviar_por_email: boolean
      }
    }

    permissoes_especificas: {
      modulos_acesso: {
        funcionarios: boolean
        empresas: boolean
        relatorios: boolean
        biometria: boolean
        configuracoes: boolean
      }

      acoes_permitidas: {
        criar: boolean
        editar: boolean
        excluir: boolean
        exportar: boolean
        aprovar: boolean
      }

      restricoes_especiais: {
        horario_acesso: {
          inicio: Time
          fim: Time
        }
        ips_permitidos: string[]
        dispositivos_limitados: boolean
      }
    }
  }

  perfis_permissao: {
    perfis_predefinidos: {
      [key in NivelAcesso]: {
        nome: string
        descricao: string
        permissoes: PermissaoDetalhada[]
        restricoes: RestricaoAcesso[]
      }
    }

    criar_perfil_customizado: {
      nome_perfil: string
      baseado_em: NivelAcesso
      permissoes_customizadas: PermissaoDetalhada[]
      usuarios_aplicar: UUID[]
    }
  }
}
```

### **3. PARÂMETROS DO SISTEMA** (`/configuracoes/parametros`)

```typescript
interface ParametrosSistemaScreen {
  categorias_parametros: {
    tolerancias_ponto: {
      tolerancia_entrada: {
        valor: number
        unidade: 'minutos'
        min: 0
        max: 60
        descricao: 'Tolerância para entrada'
      }
      tolerancia_saida: {
        valor: number
        unidade: 'minutos'
        min: 0
        max: 60
      }
      tolerancia_intervalo: {
        valor: number
        unidade: 'minutos'
        min: 0
        max: 30
      }
      intervalo_minimo: {
        valor: number
        unidade: 'minutos'
        min: 30
        max: 180
      }
      intervalo_maximo: {
        valor: number
        unidade: 'minutos'
        min: 60
        max: 240
      }
    }

    limites_horas_extras: {
      limite_diario: {
        valor: number
        unidade: 'horas'
        min: 0
        max: 4
      }
      limite_mensal: {
        valor: number
        unidade: 'horas'
        min: 0
        max: 100
      }
      aprovacao_obrigatoria: {
        valor: boolean
        descricao: 'Exigir aprovação para horas extras'
      }
      percentual_adicional: {
        valor: number
        unidade: '%'
        min: 50
        max: 100
      }
    }

    configuracoes_noturno: {
      horario_inicio: {
        valor: Time
        padrao: '22:00'
      }
      horario_fim: {
        valor: Time
        padrao: '05:00'
      }
      percentual_adicional: {
        valor: number
        unidade: '%'
        padrao: 20
        min: 20
        max: 50
      }
    }

    parametros_calculo: {
      arredondamento_minutos: {
        valor: number
        opcoes: [1, 5, 10, 15]
        descricao: 'Arredondamento para cálculo de horas'
      }
      desconto_atraso: {
        valor: boolean
        descricao: 'Aplicar desconto automático por atraso'
      }
      banco_horas: {
        valor: boolean
        descricao: 'Permitir banco de horas'
      }
      limite_banco_horas: {
        valor: number
        unidade: 'horas'
        condicional: 'banco_horas'
        min: 0
        max: 220
      }
    }
  }

  configuracoes_avancadas: {
    backup_automatico: {
      ativo: boolean
      frequencia: 'diario' | 'semanal' | 'mensal'
      horario: Time
      retencao_dias: number
      incluir_arquivos: boolean
    }

    auditoria_sistema: {
      log_detalhado: boolean
      retencao_logs_dias: number
      alertas_automaticos: boolean
      relatorio_mensal: boolean
    }

    performance: {
      cache_ativo: boolean
      tempo_cache_minutos: number
      compressao_dados: boolean
      otimizacao_consultas: boolean
    }

    seguranca: {
      sessao_timeout_minutos: number
      tentativas_login_max: number
      bloqueio_tempo_minutos: number
      senha_complexa_obrigatoria: boolean
      autenticacao_dois_fatores: boolean
    }
  }

  heranca_configuracoes: {
    empresa_principal_para_clientes: {
      jornadas: boolean
      tolerancias: boolean
      parametros_calculo: boolean
      configuracoes_email: boolean
    }

    sobrescrita_permitida: {
      nivel_empresa_cliente: string[]
      nivel_usuario: string[]
    }

    propagacao_alteracoes: {
      automatica: boolean
      notificar_alteracoes: boolean
      manter_historico: boolean
    }
  }
}
```

### **4. INTEGRAÇÕES E APIs** (`/configuracoes/integracoes`)

```typescript
interface IntegracoesAPIsScreen {
  configuracoes_email: {
    servidor_smtp: {
      host: {
        tipo: 'text'
        placeholder: 'smtp.gmail.com'
        obrigatorio: true
      }
      porta: {
        tipo: 'number'
        padrao: 587
        opcoes: [25, 465, 587, 2525]
      }
      seguranca: {
        tipo: 'select'
        opcoes: ['TLS', 'SSL', 'STARTTLS']
        padrao: 'TLS'
      }
      usuario: {
        tipo: 'email'
        obrigatorio: true
      }
      senha: {
        tipo: 'password'
        obrigatorio: true
        criptografada: true
      }
    }

    configuracoes_envio: {
      remetente_padrao: {
        nome: string
        email: string
      }
      limite_envios_hora: number
      retry_tentativas: number
      timeout_segundos: number
    }

    templates_email: {
      recuperacao_senha: {
        assunto: string
        corpo_html: string
        variaveis_disponiveis: string[]
      }
      novo_usuario: {
        assunto: string
        corpo_html: string
      }
      relatorio_agendado: {
        assunto: string
        corpo_html: string
      }
    }
  }

  apis_externas: {
    viacep: {
      ativo: boolean
      url_base: 'https://viacep.com.br/ws/'
      timeout_segundos: 10
      cache_resultados: boolean
    }

    receita_federal: {
      ativo: boolean
      url_base: string
      chave_api: string
      limite_consultas_dia: number
    }

    esocial: {
      ativo: boolean
      ambiente: 'producao' | 'homologacao'
      certificado_digital: {
        arquivo: string
        senha: string
        validade: Date
      }
      configuracoes_envio: {
        lote_maximo: number
        retry_automatico: boolean
      }
    }
  }

  webhooks: {
    configuracoes_gerais: {
      timeout_segundos: number
      retry_tentativas: number
      assinatura_secreta: string
    }

    eventos_disponiveis: {
      funcionario_criado: {
        ativo: boolean
        url_destino: string
        headers_customizados: { [key: string]: string }
      }
      ponto_registrado: {
        ativo: boolean
        url_destino: string
        filtros: {
          apenas_irregularidades: boolean
          empresas_especificas: UUID[]
        }
      }
      relatorio_gerado: {
        ativo: boolean
        url_destino: string
      }
    }

    historico_webhooks: {
      evento: string
      url_destino: string
      status_resposta: number
      tempo_resposta: number
      tentativas: number
      timestamp: Date
      erro_detalhes?: string
    }[]
  }

  sincronizacoes: {
    backup_nuvem: {
      provedor: 'AWS_S3' | 'Google_Cloud' | 'Azure'
      configuracoes: {
        bucket_nome: string
        regiao: string
        chave_acesso: string
        chave_secreta: string
      }
      frequencia: 'diario' | 'semanal'
      criptografia: boolean
    }

    integracao_erp: {
      ativo: boolean
      tipo_erp: 'SAP' | 'Oracle' | 'Protheus' | 'Personalizado'
      configuracoes_conexao: {
        url_api: string
        autenticacao: 'API_Key' | 'OAuth' | 'Basic'
        credenciais: object
      }
      mapeamento_campos: {
        funcionario: { [campo_rlponto: string]: string }
        empresa: { [campo_rlponto: string]: string }
      }
    }
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface ConfiguracoesAPI {
  // ✅ CONFIGURAÇÕES GERAIS
  'GET /api/configuracoes': {
    query: {
      categoria?: 'sistema' | 'empresa' | 'usuario'
      nivel?: 'global' | 'empresa' | 'pessoal'
    }
    response: {
      configuracoes: ConfiguracaoItem[]
      heranca: HerancaConfiguracao[]
    }
  }

  'PUT /api/configuracoes': {
    body: {
      configuracoes: {
        chave: string
        valor: any
        nivel: 'sistema' | 'empresa' | 'usuario'
        empresa_id?: UUID
      }[]
    }
    response: { message: 'Configurações atualizadas' }
  }

  // ✅ USUÁRIOS
  'GET /api/configuracoes/usuarios': {
    query: {
      nivel_acesso?: NivelAcesso
      empresa_id?: UUID
      ativo?: boolean
    }
    response: Usuario[]
  }

  'POST /api/configuracoes/usuarios': {
    body: UsuarioData
    response: Usuario
  }

  'PUT /api/configuracoes/usuarios/[id]/permissoes': {
    body: {
      nivel_acesso: NivelAcesso
      permissoes_especificas?: PermissaoEspecifica[]
      restricoes?: RestricaoAcesso[]
    }
    response: { message: 'Permissões atualizadas' }
  }

  // ✅ PARÂMETROS
  'GET /api/configuracoes/parametros': {
    query: {
      categoria?: string
      empresa_id?: UUID
    }
    response: ParametroSistema[]
  }

  'PUT /api/configuracoes/parametros': {
    body: {
      parametros: {
        chave: string
        valor: any
        empresa_id?: UUID
      }[]
    }
    response: { message: 'Parâmetros atualizados' }
  }

  // ✅ INTEGRAÇÕES
  'POST /api/configuracoes/integracoes/testar': {
    body: {
      tipo: 'email' | 'api_externa' | 'webhook'
      configuracoes: object
    }
    response: {
      sucesso: boolean
      tempo_resposta?: number
      erro_detalhes?: string
    }
  }

  'GET /api/configuracoes/integracoes/status': {
    response: {
      email: StatusIntegracao
      apis_externas: StatusIntegracao[]
      webhooks: StatusIntegracao[]
      backup_nuvem: StatusIntegracao
    }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ CONFIGURAÇÕES
CREATE TABLE configuracoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Hierarquia
  nivel VARCHAR(20) NOT NULL, -- 'sistema', 'empresa', 'usuario'
  empresa_id UUID REFERENCES empresas(id),
  usuario_id UUID REFERENCES users(id),

  -- Configuração
  categoria VARCHAR(100) NOT NULL,
  chave VARCHAR(255) NOT NULL,
  valor JSONB NOT NULL,
  tipo_valor VARCHAR(50) NOT NULL, -- 'string', 'number', 'boolean', 'object'

  -- Metadados
  descricao TEXT,
  valor_padrao JSONB,
  obrigatoria BOOLEAN DEFAULT FALSE,
  editavel BOOLEAN DEFAULT TRUE,

  -- Herança
  herda_de_nivel_superior BOOLEAN DEFAULT TRUE,
  permite_sobrescrita BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  updated_by UUID REFERENCES users(id),

  CONSTRAINT chk_nivel CHECK (nivel IN ('sistema', 'empresa', 'usuario')),
  CONSTRAINT chk_hierarquia CHECK (
    (nivel = 'sistema' AND empresa_id IS NULL AND usuario_id IS NULL) OR
    (nivel = 'empresa' AND empresa_id IS NOT NULL AND usuario_id IS NULL) OR
    (nivel = 'usuario' AND usuario_id IS NOT NULL)
  ),

  -- Única configuração por nível/contexto
  UNIQUE(nivel, empresa_id, usuario_id, categoria, chave)
);

-- ✅ PARÂMETROS DO SISTEMA
CREATE TABLE parametros_sistema (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  empresa_id UUID REFERENCES empresas(id), -- NULL = global

  -- Parâmetro
  categoria VARCHAR(100) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  valor JSONB NOT NULL,
  tipo VARCHAR(50) NOT NULL,

  -- Validação
  valor_minimo JSONB,
  valor_maximo JSONB,
  valores_permitidos JSONB, -- array de valores válidos

  -- Metadados
  descricao TEXT,
  unidade VARCHAR(20),
  impacto_alteracao VARCHAR(20) DEFAULT 'baixo', -- 'baixo', 'medio', 'alto'

  -- Controle
  editavel BOOLEAN DEFAULT TRUE,
  requer_aprovacao BOOLEAN DEFAULT FALSE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  updated_by UUID REFERENCES users(id),

  CONSTRAINT chk_impacto CHECK (
    impacto_alteracao IN ('baixo', 'medio', 'alto')
  ),

  UNIQUE(empresa_id, categoria, nome)
);

-- ✅ HISTÓRICO DE ALTERAÇÕES
CREATE TABLE configuracoes_historico (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Referência
  configuracao_id UUID REFERENCES configuracoes(id),
  parametro_id UUID REFERENCES parametros_sistema(id),

  -- Alteração
  valor_anterior JSONB,
  valor_novo JSONB,
  motivo_alteracao TEXT,

  -- Contexto
  ip_origem INET,
  user_agent TEXT,

  -- Auditoria
  alterado_em TIMESTAMP DEFAULT NOW(),
  alterado_por UUID REFERENCES users(id),

  CONSTRAINT chk_referencia CHECK (
    (configuracao_id IS NOT NULL AND parametro_id IS NULL) OR
    (configuracao_id IS NULL AND parametro_id IS NOT NULL)
  )
);

-- ✅ INTEGRAÇÕES
CREATE TABLE integracoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Identificação
  nome VARCHAR(255) NOT NULL,
  tipo VARCHAR(100) NOT NULL, -- 'email', 'api_externa', 'webhook', 'erp'
  descricao TEXT,

  -- Configuração
  configuracoes JSONB NOT NULL,
  credenciais JSONB, -- Criptografadas

  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  ultimo_teste TIMESTAMP,
  status_ultimo_teste VARCHAR(20),
  erro_ultimo_teste TEXT,

  -- Monitoramento
  total_execucoes INTEGER DEFAULT 0,
  execucoes_sucesso INTEGER DEFAULT 0,
  execucoes_erro INTEGER DEFAULT 0,
  tempo_medio_resposta INTEGER, -- ms

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_tipo_integracao CHECK (
    tipo IN ('email', 'api_externa', 'webhook', 'erp', 'backup_nuvem')
  )
);

-- ✅ ÍNDICES
CREATE INDEX idx_configuracoes_nivel ON configuracoes(nivel);
CREATE INDEX idx_configuracoes_empresa ON configuracoes(empresa_id);
CREATE INDEX idx_configuracoes_usuario ON configuracoes(usuario_id);
CREATE INDEX idx_configuracoes_categoria ON configuracoes(categoria);
CREATE INDEX idx_parametros_empresa ON parametros_sistema(empresa_id);
CREATE INDEX idx_parametros_categoria ON parametros_sistema(categoria);
CREATE INDEX idx_historico_configuracao ON configuracoes_historico(configuracao_id);
CREATE INDEX idx_historico_parametro ON configuracoes_historico(parametro_id);
CREATE INDEX idx_integracoes_tipo ON integracoes(tipo);
CREATE INDEX idx_integracoes_ativo ON integracoes(ativo);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🏗️ HIERARQUIA DE CONFIGURAÇÕES:**

1. **Sistema** → **Empresa Principal** → **Empresa Cliente** → **Usuário**
2. Herança automática de níveis superiores
3. Sobrescrita permitida em níveis inferiores
4. Auditoria completa de alterações

### **👥 GESTÃO DE USUÁRIOS:**

1. Email único no sistema
2. Nível de acesso não pode ser superior ao criador
3. Usuários inativos mantêm histórico
4. Alterações de permissão auditadas

### **⚙️ PARÂMETROS:**

1. Validação de limites mínimos/máximos
2. Alterações de alto impacto requerem aprovação
3. Backup automático antes de alterações críticas
4. Rollback disponível por 30 dias

### **🔗 INTEGRAÇÕES:**

1. Credenciais sempre criptografadas
2. Teste obrigatório antes de ativar
3. Monitoramento automático de status
4. Alertas em caso de falha

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE CONFIGURAÇÕES:**

```typescript
describe('Configurações', () => {
  test('Herança de configurações', async () => {
    await criarConfiguracaoSistema({
      chave: 'tolerancia_entrada',
      valor: 10,
    })

    const empresaConfig = await buscarConfiguracao({
      nivel: 'empresa',
      empresa_id: 'uuid',
      chave: 'tolerancia_entrada',
    })

    expect(empresaConfig.valor).toBe(10) // Herdado
  })

  test('Sobrescrita de configuração', async () => {
    await criarConfiguracaoEmpresa({
      empresa_id: 'uuid',
      chave: 'tolerancia_entrada',
      valor: 15,
    })

    const config = await buscarConfiguracao({
      nivel: 'empresa',
      empresa_id: 'uuid',
      chave: 'tolerancia_entrada',
    })

    expect(config.valor).toBe(15) // Sobrescrito
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**

- Modelo hierárquico
- APIs de configuração
- Sistema de herança

**Semana 3-4: Gestão de Usuários**

- CRUD de usuários
- Sistema de permissões
- Auditoria de acesso

**Semana 5-6: Parâmetros**

- Interface de parâmetros
- Validações e limites
- Histórico de alterações

**Semana 7-8: Integrações**

- Configuração de APIs
- Testes de conectividade
- Monitoramento

### **🔧 DEPENDÊNCIAS:**

- Módulo de Autenticação
- Sistema de criptografia
- APIs externas (ViaCEP, etc.)
- Sistema de backup
- Notificações por email
