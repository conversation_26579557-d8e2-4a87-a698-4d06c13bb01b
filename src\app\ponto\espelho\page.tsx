import { auth } from "@/lib/auth-basic"
import { redirect } from "next/navigation"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

interface SearchParams {
  funcionario?: string
  mes?: string
  ano?: string
}

export default async function EspelhoPontoPage({
  searchParams
}: {
  searchParams: SearchParams
}) {
  const session = await auth()

  if (!session?.user) {
    redirect("/auth/login")
  }

  // Parâmetros de busca
  const funcionarioId = searchParams.funcionario
  const mes = searchParams.mes ? parseInt(searchParams.mes) : new Date().getMonth() + 1
  const ano = searchParams.ano ? parseInt(searchParams.ano) : new Date().getFullYear()

  // Buscar funcionários para o filtro
  const funcionarios = await prisma.funcionario.findMany({
    where: { ativo: true },
    select: {
      id: true,
      nomeCompleto: true,
      matricula: true,
      cargo: true,
      empresa: {
        select: { nomeFantasia: true }
      }
    },
    orderBy: { nomeCompleto: 'asc' }
  })

  // Buscar registros do período selecionado
  const inicioMes = new Date(ano, mes - 1, 1)
  const fimMes = new Date(ano, mes, 0, 23, 59, 59)

  const registros = await prisma.registroPonto.findMany({
    where: {
      ...(funcionarioId && { funcionarioId }),
      dataHora: {
        gte: inicioMes,
        lte: fimMes
      }
    },
    include: {
      funcionario: {
        select: {
          id: true,
          nomeCompleto: true,
          matricula: true,
          cargo: true,
          empresa: {
            select: { nomeFantasia: true }
          }
        }
      }
    },
    orderBy: [
      { funcionarioId: 'asc' },
      { dataHora: 'desc' }
    ]
  })

  // Agrupar registros por funcionário e dia
  const registrosPorFuncionario = registros.reduce((acc, registro) => {
    const funcionarioId = registro.funcionarioId
    const dia = new Date(registro.dataHora).toDateString()
    
    if (!acc[funcionarioId]) {
      acc[funcionarioId] = {
        funcionario: registro.funcionario,
        dias: {}
      }
    }
    
    if (!acc[funcionarioId].dias[dia]) {
      acc[funcionarioId].dias[dia] = []
    }
    
    acc[funcionarioId].dias[dia].push(registro)
    return acc
  }, {} as any)

  // Calcular estatísticas
  const totalRegistros = registros.length
  const funcionariosComRegistro = Object.keys(registrosPorFuncionario).length
  const diasUteis = getDiasUteis(ano, mes)

  function getDiasUteis(ano: number, mes: number) {
    const diasNoMes = new Date(ano, mes, 0).getDate()
    let diasUteis = 0
    
    for (let dia = 1; dia <= diasNoMes; dia++) {
      const data = new Date(ano, mes - 1, dia)
      const diaSemana = data.getDay()
      // 0 = domingo, 6 = sábado
      if (diaSemana !== 0 && diaSemana !== 6) {
        diasUteis++
      }
    }
    
    return diasUteis
  }

  function calcularHorasTrabalhadas(registrosDia: any[]) {
    if (registrosDia.length < 2) return '0:00'
    
    const entradas = registrosDia.filter(r => r.tipoRegistro === 'entrada' || r.tipoRegistro === 'intervalo_fim')
    const saidas = registrosDia.filter(r => r.tipoRegistro === 'saida' || r.tipoRegistro === 'intervalo_inicio')
    
    let totalMinutos = 0
    
    for (let i = 0; i < Math.min(entradas.length, saidas.length); i++) {
      const entrada = new Date(entradas[i].dataHora)
      const saida = new Date(saidas[i].dataHora)
      const diff = saida.getTime() - entrada.getTime()
      totalMinutos += diff / (1000 * 60)
    }
    
    const horas = Math.floor(totalMinutos / 60)
    const minutos = Math.round(totalMinutos % 60)
    
    return `${horas}:${minutos.toString().padStart(2, '0')}`
  }

  const meses = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Espelho de Ponto</h1>
              <span className="ml-4 px-2 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">
                {meses[mes - 1]} {ano}
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Exportar PDF
              </button>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Exportar Excel
              </button>
              <form action="/api/auth/signout" method="post">
                <button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Sair
                </button>
              </form>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Filtros */}
          <div className="mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Filtros</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Funcionário
                  </label>
                  <select 
                    defaultValue={funcionarioId || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Todos os funcionários</option>
                    {funcionarios.map(func => (
                      <option key={func.id} value={func.id}>
                        {func.nomeCompleto} ({func.matricula})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mês
                  </label>
                  <select 
                    defaultValue={mes}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {meses.map((nome, index) => (
                      <option key={index + 1} value={index + 1}>
                        {nome}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ano
                  </label>
                  <select 
                    defaultValue={ano}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {[2024, 2025, 2026].map(year => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-end">
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Filtrar
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Estatísticas */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total de Registros
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalRegistros}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Funcionários
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {funcionariosComRegistro}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Dias Úteis
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {diasUteis}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Média/Dia
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {diasUteis > 0 ? Math.round(totalRegistros / diasUteis) : 0}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Espelho de Ponto */}
          {Object.keys(registrosPorFuncionario).length > 0 ? (
            <div className="space-y-8">
              {Object.values(registrosPorFuncionario).map((dadosFuncionario: any) => (
                <div key={dadosFuncionario.funcionario.id} className="bg-white shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {dadosFuncionario.funcionario.nomeCompleto}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Matrícula: {dadosFuncionario.funcionario.matricula} • 
                          {dadosFuncionario.funcionario.cargo} • 
                          {dadosFuncionario.funcionario.empresa.nomeFantasia}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">
                          {Object.keys(dadosFuncionario.dias).length} dias com registro
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Data
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Entrada
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Saída Intervalo
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Volta Intervalo
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Saída
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Horas Trabalhadas
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {Object.entries(dadosFuncionario.dias)
                          .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                          .map(([dia, registrosDia]: [string, any]) => {
                            const registrosOrdenados = registrosDia.sort((a: any, b: any) => 
                              new Date(a.dataHora).getTime() - new Date(b.dataHora).getTime()
                            )
                            
                            const entrada = registrosOrdenados.find((r: any) => r.tipoRegistro === 'entrada')
                            const saidaIntervalo = registrosOrdenados.find((r: any) => r.tipoRegistro === 'intervalo_inicio')
                            const voltaIntervalo = registrosOrdenados.find((r: any) => r.tipoRegistro === 'intervalo_fim')
                            const saida = registrosOrdenados.find((r: any) => r.tipoRegistro === 'saida')
                            
                            return (
                              <tr key={dia}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {new Date(dia).toLocaleDateString('pt-BR', { 
                                    weekday: 'short', 
                                    day: '2-digit', 
                                    month: '2-digit' 
                                  })}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {entrada ? new Date(entrada.dataHora).toLocaleTimeString('pt-BR', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  }) : '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {saidaIntervalo ? new Date(saidaIntervalo.dataHora).toLocaleTimeString('pt-BR', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  }) : '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {voltaIntervalo ? new Date(voltaIntervalo.dataHora).toLocaleTimeString('pt-BR', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  }) : '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {saida ? new Date(saida.dataHora).toLocaleTimeString('pt-BR', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                  }) : '-'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {calcularHorasTrabalhadas(registrosDia)}
                                </td>
                              </tr>
                            )
                          })}
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum registro encontrado</h3>
              <p className="mt-1 text-sm text-gray-500">
                Não há registros de ponto para o período selecionado.
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
