"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Briefcase, Building, Calendar, DollarSign } from "lucide-react"
import { useEffect, useState } from "react"

interface DadosProfissionaisStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function DadosProfissionaisStep({ data, onUpdate }: DadosProfissionaisStepProps) {
  const [formData, setFormData] = useState({
    empresaId: data.empresaId || '',
    matricula: data.matricula || '',
    cargo: data.cargo || '',
    setor: data.setor || '',
    dataAdmissao: data.dataAdmissao || '',
    salario: data.salario || '',
    tipoContrato: data.tipoContrato || '',
    jornadaTrabalho: data.jornadaTrabalho || '',
    horarioEntrada: data.horarioEntrada || '',
    horarioSaida: data.horarioSaida || '',
    intervalos: data.intervalos || '',
    dadosBancarios: {
      banco: data.dadosBancarios?.banco || '',
      agencia: data.dadosBancarios?.agencia || '',
      conta: data.dadosBancarios?.conta || '',
      tipoConta: data.dadosBancarios?.tipoConta || '',
      pix: data.dadosBancarios?.pix || ''
    }
  })

  const [empresas, setEmpresas] = useState([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Buscar empresas
  useEffect(() => {
    const fetchEmpresas = async () => {
      try {
        const response = await fetch('/api/empresas?status=ativo')
        if (response.ok) {
          const data = await response.json()
          setEmpresas(data.empresas || [])
        }
      } catch (error) {
        console.error('Erro ao buscar empresas:', error)
      }
    }

    fetchEmpresas()
  }, [])

  // Formatar salário
  const formatarSalario = (value: string) => {
    const numero = value.replace(/\D/g, '')
    const valorFormatado = (parseInt(numero) / 100).toFixed(2)
    return valorFormatado.replace('.', ',').replace(/\B(?=(\d{3})+(?!\d))/g, '.')
  }

  // Gerar matrícula automática
  const gerarMatricula = () => {
    const ano = new Date().getFullYear()
    const timestamp = Date.now().toString().slice(-4)
    return `${ano}${timestamp}`
  }

  const handleChange = (field: string, value: any) => {
    let newFormData = { ...formData }

    if (field.startsWith('dadosBancarios.')) {
      const subField = field.split('.')[1]
      newFormData.dadosBancarios = {
        ...formData.dadosBancarios,
        [subField]: value
      }
    } else {
      newFormData[field] = value
    }

    // Gerar matrícula automaticamente se não existir
    if (field === 'empresaId' && !formData.matricula) {
      newFormData.matricula = gerarMatricula()
    }

    setFormData(newFormData)
    onUpdate(newFormData)

    // Validações
    if (field === 'matricula' && !value.trim()) {
      setErrors(prev => ({ ...prev, matricula: 'Matrícula é obrigatória' }))
    } else if (field === 'matricula') {
      setErrors(prev => ({ ...prev, matricula: '' }))
    }

    if (field === 'cargo' && !value.trim()) {
      setErrors(prev => ({ ...prev, cargo: 'Cargo é obrigatório' }))
    } else if (field === 'cargo') {
      setErrors(prev => ({ ...prev, cargo: '' }))
    }

    if (field === 'dataAdmissao' && !value) {
      setErrors(prev => ({ ...prev, dataAdmissao: 'Data de admissão é obrigatória' }))
    } else if (field === 'dataAdmissao') {
      setErrors(prev => ({ ...prev, dataAdmissao: '' }))
    }
  }

  const cargosComuns = [
    'Analista',
    'Assistente',
    'Auxiliar',
    'Coordenador',
    'Desenvolvedor',
    'Gerente',
    'Supervisor',
    'Técnico',
    'Vendedor',
    'Operador'
  ]

  const setoresComuns = [
    'Administrativo',
    'Comercial',
    'Financeiro',
    'Recursos Humanos',
    'Tecnologia da Informação',
    'Produção',
    'Qualidade',
    'Logística',
    'Marketing',
    'Jurídico'
  ]

  return (
    <div className="space-y-6">
      {/* Dados da Empresa */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Empresa e Matrícula
          </CardTitle>
          <CardDescription>
            Empresa de lotação e identificação do funcionário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="empresaId">Empresa *</Label>
              <Select value={formData.empresaId} onValueChange={(value) => handleChange('empresaId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a empresa" />
                </SelectTrigger>
                <SelectContent>
                  {empresas.map((empresa: any) => (
                    <SelectItem key={empresa.id} value={empresa.id}>
                      {empresa.nomeFantasia}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="matricula">Matrícula *</Label>
              <div className="flex gap-2">
                <Input
                  id="matricula"
                  value={formData.matricula}
                  onChange={(e) => handleChange('matricula', e.target.value)}
                  placeholder="Número da matrícula"
                  className={errors.matricula ? 'border-red-500' : ''}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleChange('matricula', gerarMatricula())}
                >
                  Gerar
                </Button>
              </div>
              {errors.matricula && (
                <p className="text-sm text-red-500 mt-1">{errors.matricula}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cargo e Função */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            Cargo e Função
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="cargo">Cargo *</Label>
              <Input
                id="cargo"
                value={formData.cargo}
                onChange={(e) => handleChange('cargo', e.target.value)}
                placeholder="Digite ou selecione um cargo"
                list="cargos-list"
                className={errors.cargo ? 'border-red-500' : ''}
              />
              <datalist id="cargos-list">
                {cargosComuns.map((cargo) => (
                  <option key={cargo} value={cargo} />
                ))}
              </datalist>
              {errors.cargo && (
                <p className="text-sm text-red-500 mt-1">{errors.cargo}</p>
              )}
            </div>

            <div>
              <Label htmlFor="setor">Setor</Label>
              <Input
                id="setor"
                value={formData.setor}
                onChange={(e) => handleChange('setor', e.target.value)}
                placeholder="Digite ou selecione um setor"
                list="setores-list"
              />
              <datalist id="setores-list">
                {setoresComuns.map((setor) => (
                  <option key={setor} value={setor} />
                ))}
              </datalist>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dados Contratuais */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Dados Contratuais
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="dataAdmissao">Data de Admissão *</Label>
              <Input
                id="dataAdmissao"
                type="date"
                value={formData.dataAdmissao}
                onChange={(e) => handleChange('dataAdmissao', e.target.value)}
                className={errors.dataAdmissao ? 'border-red-500' : ''}
              />
              {errors.dataAdmissao && (
                <p className="text-sm text-red-500 mt-1">{errors.dataAdmissao}</p>
              )}
            </div>

            <div>
              <Label htmlFor="tipoContrato">Tipo de Contrato</Label>
              <Select value={formData.tipoContrato} onValueChange={(value) => handleChange('tipoContrato', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="clt">CLT</SelectItem>
                  <SelectItem value="pj">Pessoa Jurídica</SelectItem>
                  <SelectItem value="estagio">Estágio</SelectItem>
                  <SelectItem value="temporario">Temporário</SelectItem>
                  <SelectItem value="terceirizado">Terceirizado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="jornadaTrabalho">Jornada de Trabalho</Label>
              <Select value={formData.jornadaTrabalho} onValueChange={(value) => handleChange('jornadaTrabalho', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="44h">44h semanais</SelectItem>
                  <SelectItem value="40h">40h semanais</SelectItem>
                  <SelectItem value="36h">36h semanais</SelectItem>
                  <SelectItem value="30h">30h semanais</SelectItem>
                  <SelectItem value="20h">20h semanais</SelectItem>
                  <SelectItem value="personalizada">Personalizada</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="horarioEntrada">Horário de Entrada</Label>
              <Input
                id="horarioEntrada"
                type="time"
                value={formData.horarioEntrada}
                onChange={(e) => handleChange('horarioEntrada', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="horarioSaida">Horário de Saída</Label>
              <Input
                id="horarioSaida"
                type="time"
                value={formData.horarioSaida}
                onChange={(e) => handleChange('horarioSaida', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="intervalos">Intervalos (minutos)</Label>
              <Input
                id="intervalos"
                type="number"
                value={formData.intervalos}
                onChange={(e) => handleChange('intervalos', e.target.value)}
                placeholder="60"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Remuneração */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Remuneração
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="salario">Salário Base</Label>
            <Input
              id="salario"
              value={formData.salario}
              onChange={(e) => handleChange('salario', e.target.value)}
              placeholder="R$ 0,00"
            />
            <p className="text-sm text-gray-600 mt-1">
              Valor do salário base mensal
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Dados Bancários */}
      <Card>
        <CardHeader>
          <CardTitle>Dados Bancários</CardTitle>
          <CardDescription>
            Informações para pagamento de salário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="banco">Banco</Label>
              <Input
                id="banco"
                value={formData.dadosBancarios.banco}
                onChange={(e) => handleChange('dadosBancarios.banco', e.target.value)}
                placeholder="Nome ou código do banco"
              />
            </div>

            <div>
              <Label htmlFor="agencia">Agência</Label>
              <Input
                id="agencia"
                value={formData.dadosBancarios.agencia}
                onChange={(e) => handleChange('dadosBancarios.agencia', e.target.value)}
                placeholder="0000"
              />
            </div>

            <div>
              <Label htmlFor="conta">Conta</Label>
              <Input
                id="conta"
                value={formData.dadosBancarios.conta}
                onChange={(e) => handleChange('dadosBancarios.conta', e.target.value)}
                placeholder="00000-0"
              />
            </div>

            <div>
              <Label htmlFor="tipoConta">Tipo de Conta</Label>
              <Select 
                value={formData.dadosBancarios.tipoConta} 
                onValueChange={(value) => handleChange('dadosBancarios.tipoConta', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="corrente">Conta Corrente</SelectItem>
                  <SelectItem value="poupanca">Conta Poupança</SelectItem>
                  <SelectItem value="salario">Conta Salário</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="pix">Chave PIX (opcional)</Label>
              <Input
                id="pix"
                value={formData.dadosBancarios.pix}
                onChange={(e) => handleChange('dadosBancarios.pix', e.target.value)}
                placeholder="CPF, email, telefone ou chave aleatória"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resumo */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo Profissional</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {formData.matricula && (
              <Badge variant="outline">Matrícula: {formData.matricula}</Badge>
            )}
            {formData.cargo && (
              <Badge variant="outline">Cargo: {formData.cargo}</Badge>
            )}
            {formData.setor && (
              <Badge variant="outline">Setor: {formData.setor}</Badge>
            )}
            {formData.dataAdmissao && (
              <Badge variant="outline">
                Admissão: {new Date(formData.dataAdmissao).toLocaleDateString('pt-BR')}
              </Badge>
            )}
            {formData.tipoContrato && (
              <Badge variant="secondary">{formData.tipoContrato.toUpperCase()}</Badge>
            )}
            {formData.jornadaTrabalho && (
              <Badge variant="outline">{formData.jornadaTrabalho}</Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dicas */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Dicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• A matrícula deve ser única dentro da empresa</li>
            <li>• Verifique se o cargo está correto para relatórios</li>
            <li>• Dados bancários são necessários para pagamento</li>
            <li>• Horários devem estar de acordo com a jornada</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
