# Script de Deploy Simples para RLP-V2
Write-Host "Iniciando deploy do RLP-V2..." -ForegroundColor Green

# 1. Build
Write-Host "Fazendo build..." -ForegroundColor Yellow
npm run build

# 2. <PERSON><PERSON><PERSON> pacote (incluindo todos os arquivos necessários)
Write-Host "Criando pacote..." -ForegroundColor Yellow
tar -czf deploy.tar.gz .next public package.json package-lock.json next.config.ts src prisma scripts .env.example tailwind.config.ts tsconfig.json

# 3. Enviar
Write-Host "Enviando para servidor..." -ForegroundColor Yellow
scp deploy.tar.gz rlp-server:/opt/rlponto/

# 4. Extrair e instalar
Write-Host "Configurando no servidor..." -ForegroundColor Yellow
ssh rlp-server "cd /opt/rlponto && tar -xzf deploy.tar.gz && npm ci --only=production && npx prisma generate"

# 4.5. Inicializar sistema
Write-Host "Inicializando sistema..." -ForegroundColor Yellow
ssh rlp-server "cd /opt/rlponto && node scripts/init-system.js"

# 5. PM2 config
$config = 'module.exports = { apps: [{ name: "rlponto", script: "npm", args: "start", cwd: "/opt/rlponto", instances: 1, autorestart: true, env: { NODE_ENV: "production", PORT: 3000 } }] }'
echo $config | ssh rlp-server "cd /opt/rlponto && cat > ecosystem.config.js"

# 6. Restart
ssh rlp-server "cd /opt/rlponto && pm2 stop rlponto || true && pm2 delete rlponto || true && pm2 start ecosystem.config.js && pm2 save"

# 7. Limpar
Remove-Item "deploy.tar.gz" -ErrorAction SilentlyContinue
ssh rlp-server "rm -f /opt/rlponto/deploy.tar.gz"

Write-Host "Deploy concluido! Acesse: http://***********" -ForegroundColor Green
