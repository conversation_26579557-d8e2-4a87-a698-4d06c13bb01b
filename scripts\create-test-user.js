const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    console.log('🔄 Criando usuário de teste...')

    // Verificar se usuário já existe
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('✅ Usuário <EMAIL> já existe')
      console.log('📧 Email:', existingUser.email)
      console.log('👤 Nome:', existingUser.nome)
      console.log('🔑 Nível:', existingUser.nivelAcesso)
      console.log('✅ Ativo:', existingUser.ativo)
      return
    }

    // Hash da senha
    const passwordHash = await bcrypt.hash('admin123', 12)

    // Criar usuário
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        nome: 'Administrador',
        passwordHash,
        nivelAcesso: 'admin_total',
        ativo: true,
        emailVerified: new Date()
      }
    })

    console.log('✅ Usuário criado com sucesso!')
    console.log('📧 Email:', user.email)
    console.log('🔑 Senha: admin123')
    console.log('👤 Nome:', user.nome)
    console.log('🔑 Nível:', user.nivelAcesso)

    // Criar empresa principal se não existir
    const empresaPrincipal = await prisma.empresa.findFirst({
      where: { tipoEmpresa: 'principal' }
    })

    if (!empresaPrincipal) {
      console.log('🔄 Criando empresa principal...')
      
      const empresa = await prisma.empresa.create({
        data: {
          tipoEmpresa: 'principal',
          nomeFantasia: 'RLPONTO Sistemas',
          razaoSocial: 'RLPONTO Sistemas de Controle de Ponto LTDA',
          cnpj: '12.345.678/0001-90',
          endereco: {
            logradouro: 'Rua Principal, 123',
            cidade: 'São Paulo',
            estado: 'SP',
            cep: '01234-567'
          },
          contatos: {
            telefone: '(11) 99999-9999',
            email: '<EMAIL>'
          },
          configuracoes: {
            jornada_padrao: {
              horas_semanais: 40,
              dias_semana: [1, 2, 3, 4, 5],
              horario_entrada: '08:00',
              horario_saida: '17:00',
              intervalo_almoco: 60
            },
            tolerancias: {
              entrada_minutos: 10,
              saida_minutos: 10,
              intervalo_minutos: 5
            },
            parametros_ponto: {
              permitir_registro_manual: true,
              exigir_justificativa_atraso: true,
              bloquear_registro_fora_jornada: false
            }
          },
          createdBy: user.id
        }
      })

      console.log('✅ Empresa principal criada!')
      console.log('🏢 Nome:', empresa.nomeFantasia)
      console.log('📄 CNPJ:', empresa.cnpj)
    }

  } catch (error) {
    console.error('❌ Erro ao criar usuário de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
