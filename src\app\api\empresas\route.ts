// Removido import do auth para evitar problemas de Server Components
import { PrismaClient } from "@prisma/client"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para validação de query parameters
const querySchema = z.object({
  status: z.enum(['ativo', 'inativo', 'todos']).optional().default('ativo'),
  search: z.string().optional(),
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  tipo: z.enum(['principal', 'cliente', 'todos']).optional().default('todos')
})

// Schema para criação de empresa
const createEmpresaSchema = z.object({
  tipoEmpresa: z.enum(['principal', 'cliente']),
  empresaPrincipalId: z.string().uuid().optional(),
  nomeFantasia: z.string().min(1, "Nome fantasia é obrigatório"),
  razaoSocial: z.string().min(1, "Razão social é obrigatória"),
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, "CNPJ inválido"),
  inscricaoEstadual: z.string().optional(),
  nomeProjeto: z.string().optional(),
  codigoProjeto: z.string().optional(),
  descricao: z.string().optional(),
  endereco: z.object({
    cep: z.string().optional(),
    logradouro: z.string().optional(),
    numero: z.string().optional(),
    complemento: z.string().optional(),
    bairro: z.string().optional(),
    cidade: z.string().optional(),
    estado: z.string().optional()
  }).optional().default({}),
  contatos: z.object({
    telefone: z.string().optional(),
    celular: z.string().optional(),
    email: z.string().email().optional(),
    responsavel: z.string().optional()
  }).optional().default({}),
  descricaoProjeto: z.string().optional(),
  dataInicio: z.string().datetime().optional(),
  dataFimPrevista: z.string().datetime().optional(),
  configuracoes: z.record(z.any()).optional().default({})
})

// GET /api/empresas - Listar empresas
export async function GET(request: NextRequest) {
  try {
    const user = requireApiAuth(request)

    if (!user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const query = querySchema.parse({
      status: searchParams.get('status'),
      search: searchParams.get('search'),
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      tipo: searchParams.get('tipo')
    })

    // Construir filtros
    const where: any = {}

    // Filtro por status
    if (query.status !== 'todos') {
      where.ativo = query.status === 'ativo'
    }

    // Filtro por tipo
    if (query.tipo !== 'todos') {
      where.tipoEmpresa = query.tipo
    }

    // Filtro por busca
    if (query.search) {
      where.OR = [
        { nomeFantasia: { contains: query.search, mode: 'insensitive' } },
        { razaoSocial: { contains: query.search, mode: 'insensitive' } },
        { cnpj: { contains: query.search } },
        { nomeProjeto: { contains: query.search, mode: 'insensitive' } },
        { codigoProjeto: { contains: query.search, mode: 'insensitive' } }
      ]
    }

    // Calcular offset
    const offset = (query.page - 1) * query.limit

    // Buscar empresas
    const [empresas, total] = await Promise.all([
      prisma.empresa.findMany({
        where,
        include: {
          empresaPrincipal: {
            select: {
              id: true,
              nomeFantasia: true
            }
          },
          _count: {
            select: {
              funcionarios: {
                where: { ativo: true }
              },
              funcionarioAlocacoes: {
                where: { status: 'ativo' }
              }
            }
          }
        },
        orderBy: [
          { tipoEmpresa: 'asc' }, // Principal primeiro
          { nomeFantasia: 'asc' }
        ],
        skip: offset,
        take: query.limit
      }),
      prisma.empresa.count({ where })
    ])

    const totalPages = Math.ceil(total / query.limit)

    return NextResponse.json({
      empresas,
      total,
      page: query.page,
      totalPages,
      hasNext: query.page < totalPages,
      hasPrev: query.page > 1
    })

  } catch (error) {
    console.error('Erro ao buscar empresas:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Parâmetros inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// POST /api/empresas - Criar empresa
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const data = createEmpresaSchema.parse(body)

    // Validações específicas
    if (data.tipoEmpresa === 'principal') {
      // Verificar se já existe empresa principal
      const existePrincipal = await prisma.empresa.findFirst({
        where: { tipoEmpresa: 'principal' }
      })

      if (existePrincipal) {
        return NextResponse.json(
          { error: "Já existe uma empresa principal cadastrada" },
          { status: 400 }
        )
      }
    } else if (data.tipoEmpresa === 'cliente') {
      // Empresa cliente deve ter empresa principal
      if (!data.empresaPrincipalId) {
        return NextResponse.json(
          { error: "Empresa cliente deve ter empresa principal associada" },
          { status: 400 }
        )
      }

      // Verificar se empresa principal existe
      const empresaPrincipal = await prisma.empresa.findUnique({
        where: { id: data.empresaPrincipalId }
      })

      if (!empresaPrincipal || empresaPrincipal.tipoEmpresa !== 'principal') {
        return NextResponse.json(
          { error: "Empresa principal não encontrada" },
          { status: 400 }
        )
      }
    }

    // Verificar se CNPJ já existe
    const existeCnpj = await prisma.empresa.findUnique({
      where: { cnpj: data.cnpj }
    })

    if (existeCnpj) {
      return NextResponse.json(
        { error: "CNPJ já cadastrado" },
        { status: 400 }
      )
    }

    // Criar empresa
    const empresa = await prisma.empresa.create({
      data: {
        ...data,
        dataInicio: data.dataInicio ? new Date(data.dataInicio) : null,
        dataFimPrevista: data.dataFimPrevista ? new Date(data.dataFimPrevista) : null,
        createdBy: session.user.id
      },
      include: {
        empresaPrincipal: {
          select: {
            id: true,
            nomeFantasia: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "CREATE_EMPRESA",
        tabelaAfetada: "empresas",
        registroId: empresa.id,
        dadosNovos: empresa,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(empresa, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar empresa:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
