# 💳 MÓDULO DE LICENCIAMENTO

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Validar e consumir licenças externas
- Monitorar limites de uso em tempo real
- Controlar funcionalidades por tipo de licença
- Gerar alertas de expiração e limites
- Integrar com sistema externo de geração

### **📊 RESPONSABILIDADES:**

- Validação de licenças .rllic
- Monitoramento de uso de recursos
- Controle de acesso por plano
- Alertas automáticos
- Dashboard de status
- Comunicação com API externa

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. VALIDAÇÃO DE LICENÇAS:**

- Importação de arquivos .rllic
- Validação de assinatura digital
- Verificação de expiração
- Controle de revogação via API

#### **2. MONITORAMENTO DE LIMITES:**

- **Funcionários cadastrados**: Contagem em tempo real por plano
- **Dispositivos biométricos**: Limite de conexões simultâneas
- **Usuários do sistema**: Controle de sessões ativas
- **Empresas/projetos**: Limite de criação por licença
- **Requests de API**: Rate limiting por dia/hora
- **Armazenamento**: Monitoramento de uso em GB
- **Relatórios**: Controle de geração mensal
- **Backup retention**: Dias de retenção por plano

#### **3. CONTROLE DE FUNCIONALIDADES:**

- **Módulos por plano**: Habilitação/desabilitação dinâmica
- **Limites específicos**: Enforcement em tempo real
- **Bloqueio inteligente**: Respeita princípio "NUNCA IMPEDIR"
- **Degradação gradual**: Avisos → Limitações → Bloqueio parcial
- **Override de emergência**: Supervisor pode autorizar temporariamente

#### **4. ALERTAS E NOTIFICAÇÕES:**

- Expiração próxima (30, 15, 7 dias)
- Limites atingidos (80%, 90%, 100%)
- Licença revogada
- Falha na validação

---

## 🔒 IMPLEMENTAÇÃO DO PRINCÍPIO "NUNCA IMPEDIR"

### **⚖️ BALANCEAMENTO: LICENCIAMENTO vs NUNCA IMPEDIR**

```typescript
interface LicenseEnforcement {
  // REGRAS DE APLICAÇÃO DE LIMITES
  enforcement_rules: {
    funcionarios: {
      limite_soft: '80% do limite' // Avisos começam aqui
      limite_hard: '100% do limite' // Bloqueio de NOVOS cadastros
      excecao_registro_ponto: true // NUNCA bloqueia registro de ponto
    }

    dispositivos_biometricos: {
      limite_soft: '90% do limite'
      limite_hard: '100% do limite'
      fallback_manual: true // Sempre permite registro manual
    }

    usuarios_sistema: {
      limite_soft: '85% do limite'
      limite_hard: '100% do limite'
      admin_sempre_permitido: true // Admin sempre pode acessar
    }
  }

  // AÇÕES POR NÍVEL DE LIMITE
  actions_by_level: {
    '0-79%': 'funcionamento_normal'
    '80-89%': 'avisos_visuais'
    '90-99%': 'avisos_criticos + notificacao_admin'
    '100%': 'bloqueio_novas_operacoes + manutencao_funcionamento_atual'
    licenca_expirada: 'modo_degradado_30_dias + bloqueio_gradual'
  }
}
```

### **🚨 ESTRATÉGIA DE DEGRADAÇÃO GRADUAL**

```typescript
interface GradualDegradation {
  // LICENÇA VÁLIDA - LIMITES ATINGIDOS
  limits_reached: {
    funcionarios_100: {
      bloqueia: ['novo_cadastro_funcionario']
      permite: ['registro_ponto', 'edicao_funcionario_existente', 'relatorios']
      aviso: 'Limite de funcionários atingido. Contate suporte para upgrade.'
    }

    dispositivos_100: {
      bloqueia: ['novo_dispositivo_biometrico']
      permite: ['registro_manual', 'dispositivos_existentes']
      fallback: 'Registro manual sempre disponível'
    }
  }

  // LICENÇA EXPIRADA - PERÍODO DE GRAÇA
  license_expired: {
    '0-7_dias': {
      funcionalidade: '100%'
      aviso: 'Licença expirada - renove em 7 dias'
    }

    '8-15_dias': {
      funcionalidade: '90%'
      bloqueia: ['novos_relatorios', 'novas_configuracoes']
      permite: ['registro_ponto', 'consultas_basicas']
    }

    '16-30_dias': {
      funcionalidade: '70%'
      bloqueia: ['relatorios', 'configuracoes', 'novos_usuarios']
      permite: ['registro_ponto', 'consulta_funcionarios']
    }

    '30+_dias': {
      funcionalidade: '50%'
      bloqueia: ['maioria_funcionalidades']
      permite: ['registro_ponto_basico', 'consulta_limitada']
      modo: 'somente_leitura_parcial'
    }
  }
}
```

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE LICENCIAMENTO** (`/licenciamento`)

```typescript
interface DashboardLicenciamentoScreen {
  status_licenca: {
    informacoes_gerais: {
      tipo_licenca: 'mensal' | 'anual' | 'vitalicia'
      plano: 'basico' | 'profissional' | 'empresarial' | 'vitalicio'
      status: 'ativa' | 'expirada' | 'revogada' | 'suspensa'
      data_inicio: Date
      data_expiracao: Date | null // null para vitalícia
      dias_restantes: number | null
      empresa_licenciada: string
      numero_licenca: string
      valor_mensal: number // R$ 299, 599, 1299 ou 0 (vitalício)
    }

    indicadores_visuais: {
      cor_status: 'green' | 'yellow' | 'red'
      icone_tipo: string
      barra_progresso_tempo: number // % do período usado
      alerta_principal: string | null
    }
  }

  uso_recursos: {
    funcionarios: {
      atual: number
      limite: number
      percentual_uso: number
      status: 'ok' | 'proximo_limite' | 'limite_excedido'
    }

    usuarios_sistema: {
      atual: number
      limite: number
      percentual_uso: number
      status: 'ok' | 'proximo_limite' | 'limite_excedido'
    }

    dispositivos_biometricos: {
      atual: number
      limite: number
      percentual_uso: number
      status: 'ok' | 'proximo_limite' | 'limite_excedido'
    }

    empresas_clientes: {
      atual: number
      limite: number
      percentual_uso: number
      status: 'ok' | 'proximo_limite' | 'limite_excedido'
    }

    armazenamento: {
      atual_gb: number
      limite_gb: number
      percentual_uso: number
      status: 'ok' | 'proximo_limite' | 'limite_excedido'
    }
  }

  funcionalidades_plano: {
    modulos_inclusos: {
      funcionarios: boolean
      empresas: boolean
      registro_ponto: boolean
      relatorios: boolean
      biometria: boolean
      apis: boolean
      configuracoes_avancadas: boolean
      integracao_erp: boolean
      backup_nuvem: boolean
    }

    limites_especificos: {
      relatorios_mes: number
      api_calls_dia: number
      backup_retencao_dias: number
      suporte_tecnico: 'basico' | 'prioritario' | 'dedicado'
    }
  }

  alertas_ativos: {
    criticos: {
      tipo: 'expiracao_iminente' | 'limite_excedido' | 'licenca_revogada'
      mensagem: string
      data_alerta: Date
      acao_requerida: string
    }[]

    avisos: {
      tipo: 'limite_proximo' | 'expiracao_30_dias' | 'uso_alto'
      mensagem: string
      data_alerta: Date
      acao_sugerida: string
    }[]
  }

  historico_licencas: {
    licencas_anteriores: {
      numero_licenca: string
      tipo: string
      plano: string
      periodo: { inicio: Date; fim: Date }
      status_final: 'expirada' | 'revogada' | 'substituida'
    }[]
  }
}
```

### **2. IMPORTAÇÃO DE LICENÇA** (`/licenciamento/importar`)

```typescript
interface ImportacaoLicencaScreen {
  upload_licenca: {
    arquivo_licenca: {
      tipo: 'file'
      aceita: ['.rllic']
      tamanho_max: '1MB'
      validacao: 'Arquivo .rllic válido'
      preview_info: {
        nome_arquivo: string
        tamanho: string
        data_modificacao: Date
      }
    }

    validacao_automatica: {
      etapas: [
        'Verificação de formato',
        'Validação de assinatura digital',
        'Verificação de expiração',
        'Consulta de revogação',
        'Validação de empresa',
      ]

      progresso_atual: number
      etapa_atual: string
      tempo_estimado: string
    }
  }

  preview_licenca: {
    informacoes_licenca: {
      numero_licenca: string
      tipo_licenca: 'mensal' | 'anual' | 'vitalicia'
      plano: 'basico' | 'profissional' | 'empresarial'
      empresa_licenciada: string
      cnpj_empresa: string
      data_emissao: Date
      data_inicio_vigencia: Date
      data_fim_vigencia: Date | null
      gerada_por: string
    }

    limites_recursos: {
      funcionarios_max: number
      usuarios_max: number
      dispositivos_max: number
      empresas_clientes_max: number
      armazenamento_gb: number
      relatorios_mes: number
      api_calls_dia: number
    }

    funcionalidades_incluidas: {
      modulos: string[]
      integracao_erp: boolean
      backup_nuvem: boolean
      suporte_nivel: string
      personalizacoes: boolean
    }

    validacoes_resultado: {
      assinatura_valida: boolean
      nao_expirada: boolean
      nao_revogada: boolean
      empresa_compativel: boolean
      todas_validacoes_ok: boolean
    }
  }

  confirmacao_importacao: {
    impacto_alteracao: {
      licenca_atual: {
        tipo: string
        plano: string
        expira_em: Date
      }

      nova_licenca: {
        tipo: string
        plano: string
        expira_em: Date | null
      }

      mudancas_limites: {
        recurso: string
        limite_atual: number
        novo_limite: number
        impacto: 'aumento' | 'reducao' | 'sem_alteracao'
      }[]

      funcionalidades_afetadas: {
        funcionalidade: string
        status_atual: boolean
        novo_status: boolean
        impacto: 'habilitada' | 'desabilitada' | 'sem_alteracao'
      }[]
    }

    acoes_pos_importacao: {
      backup_licenca_atual: boolean
      notificar_usuarios: boolean
      gerar_relatorio_mudancas: boolean
      agendar_verificacao_uso: boolean
    }

    confirmacao_final: {
      aceito_termos: boolean
      ciente_impactos: boolean
      autorizado_alteracao: boolean
    }
  }
}
```

### **3. MONITORAMENTO DE USO** (`/licenciamento/monitoramento`)

```typescript
interface MonitoramentoUsoScreen {
  metricas_tempo_real: {
    uso_atual: {
      funcionarios_ativos: number
      usuarios_logados: number
      dispositivos_online: number
      relatorios_gerados_hoje: number
      api_calls_hoje: number
      armazenamento_usado_gb: number
    }

    tendencias: {
      crescimento_funcionarios_mes: number // %
      media_usuarios_simultaneos: number
      pico_uso_dispositivos: number
      projecao_uso_armazenamento: number // GB próximo mês
    }
  }

  graficos_historico: {
    uso_funcionarios: {
      tipo: 'linha'
      periodo: 'ultimos_12_meses'
      dados: {
        mes: string
        funcionarios_cadastrados: number
        funcionarios_ativos: number
        limite_plano: number
      }[]
    }

    uso_dispositivos: {
      tipo: 'area'
      periodo: 'ultimos_30_dias'
      dados: {
        data: Date
        dispositivos_online: number
        reconhecimentos_dia: number
      }[]
    }

    consumo_apis: {
      tipo: 'barra'
      periodo: 'ultima_semana'
      dados: {
        dia: string
        calls_realizadas: number
        limite_diario: number
      }[]
    }

    crescimento_armazenamento: {
      tipo: 'linha'
      periodo: 'ultimos_6_meses'
      dados: {
        mes: string
        armazenamento_gb: number
        limite_gb: number
      }[]
    }
  }

  alertas_configuracao: {
    limites_alerta: {
      funcionarios: {
        alerta_80_porcento: boolean
        alerta_90_porcento: boolean
        alerta_95_porcento: boolean
        notificar_emails: string[]
      }

      armazenamento: {
        alerta_75_porcento: boolean
        alerta_85_porcento: boolean
        alerta_95_porcento: boolean
        limpeza_automatica: boolean
      }

      api_calls: {
        alerta_diario_80_porcento: boolean
        throttling_automatico: boolean
        notificar_desenvolvedores: boolean
      }
    }

    acoes_automaticas: {
      limite_funcionarios_atingido: {
        bloquear_novos_cadastros: boolean
        notificar_administradores: boolean
        sugerir_upgrade: boolean
      }

      limite_armazenamento_atingido: {
        bloquear_uploads: boolean
        comprimir_arquivos_antigos: boolean
        arquivar_dados_antigos: boolean
      }
    }
  }

  projecoes_uso: {
    baseado_tendencia_atual: {
      funcionarios_proximo_mes: number
      armazenamento_proximo_mes: number
      data_limite_funcionarios: Date | null
      data_limite_armazenamento: Date | null
    }

    cenarios: {
      crescimento_conservador: {
        funcionarios_6_meses: number
        necessidade_upgrade: Date | null
      }

      crescimento_moderado: {
        funcionarios_6_meses: number
        necessidade_upgrade: Date | null
      }

      crescimento_acelerado: {
        funcionarios_6_meses: number
        necessidade_upgrade: Date | null
      }
    }

    recomendacoes: {
      acao_sugerida: 'manter_plano' | 'upgrade_recomendado' | 'upgrade_urgente'
      justificativa: string
      economia_potencial: number | null
      risco_interrupcao: 'baixo' | 'medio' | 'alto'
    }
  }
}
```

### **4. CONFIGURAÇÕES DE LICENÇA** (`/licenciamento/configuracoes`)

```typescript
interface ConfiguracoesLicencaScreen {
  validacao_automatica: {
    frequencia_verificacao: {
      valor: 'diaria' | 'semanal' | 'mensal'
      horario_verificacao: Time
      descricao: 'Frequência de validação com servidor externo'
    }

    acao_licenca_expirada: {
      modo_gracioso: {
        ativo: boolean
        dias_graca: number
        funcionalidades_limitadas: string[]
      }

      bloqueio_imediato: {
        ativo: boolean
        manter_dados: boolean
        notificar_usuarios: boolean
      }
    }

    acao_limite_excedido: {
      bloqueio_preventivo: boolean
      degradacao_gradual: boolean
      notificacao_antecipada: boolean
    }
  }

  comunicacao_servidor_externo: {
    configuracao_api: {
      url_servidor: string
      timeout_segundos: number
      retry_tentativas: number
      chave_autenticacao: string
    }

    dados_enviados: {
      informacoes_uso: boolean
      metricas_performance: boolean
      logs_erro: boolean
      dados_anonimizados: boolean
    }

    cache_validacao: {
      ativo: boolean
      tempo_cache_horas: number
      validacao_offline: boolean
    }
  }

  notificacoes: {
    email_configuracao: {
      destinatarios_admin: string[]
      destinatarios_financeiro: string[]
      template_expiracao: string
      template_limite: string
    }

    alertas_sistema: {
      dashboard_popup: boolean
      notificacao_browser: boolean
      log_sistema: boolean
    }

    cronograma_lembretes: {
      expiracao_30_dias: boolean
      expiracao_15_dias: boolean
      expiracao_7_dias: boolean
      expiracao_1_dia: boolean
      limite_80_porcento: boolean
      limite_90_porcento: boolean
    }
  }

  backup_licenca: {
    backup_automatico: {
      ativo: boolean
      frequencia: 'diario' | 'semanal'
      local_backup: 'local' | 'nuvem' | 'ambos'
    }

    historico_licencas: {
      manter_historico: boolean
      retencao_anos: number
      incluir_metricas_uso: boolean
    }

    recuperacao_emergencia: {
      licenca_temporaria_dias: number
      funcionalidades_essenciais: string[]
      contato_emergencia: string
    }
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface LicenciamentoAPI {
  // ✅ VALIDAÇÃO DE LICENÇA
  'POST /api/licenciamento/validar': {
    body: {
      arquivo_licenca: string // base64
      forcar_validacao: boolean
    }
    response: {
      valida: boolean
      detalhes_licenca: DetalhesLicenca
      validacoes: ValidacaoResultado[]
      erro?: string
    }
  }

  'POST /api/licenciamento/importar': {
    body: {
      arquivo_licenca: string
      confirmar_alteracoes: boolean
      opcoes_importacao: OpcoesImportacao
    }
    response: {
      sucesso: boolean
      licenca_anterior: LicencaInfo
      nova_licenca: LicencaInfo
      mudancas_aplicadas: MudancaLicenca[]
    }
  }

  // ✅ STATUS E MONITORAMENTO
  'GET /api/licenciamento/status': {
    response: {
      licenca_ativa: LicencaInfo
      uso_recursos: UsoRecursos
      alertas_ativos: AlertaLicenca[]
      dias_restantes: number | null
    }
  }

  'GET /api/licenciamento/uso': {
    query: {
      periodo?: 'hoje' | 'semana' | 'mes' | 'ano'
      recurso?: 'funcionarios' | 'usuarios' | 'dispositivos' | 'armazenamento'
    }
    response: {
      uso_atual: UsoAtual
      historico: HistoricoUso[]
      projecoes: ProjecaoUso
    }
  }

  // ✅ COMUNICAÇÃO COM SERVIDOR EXTERNO
  'POST /api/licenciamento/verificar-revogacao': {
    response: {
      licenca_revogada: boolean
      motivo_revogacao?: string
      data_revogacao?: Date
    }
  }

  'POST /api/licenciamento/relatar-uso': {
    body: {
      metricas_uso: MetricasUso
      periodo: DateRange
    }
    response: {
      relatorio_enviado: boolean
      proxima_verificacao: Date
    }
  }

  // ✅ CONFIGURAÇÕES
  'GET /api/licenciamento/configuracoes': {
    response: ConfiguracoesLicenciamento
  }

  'PUT /api/licenciamento/configuracoes': {
    body: Partial<ConfiguracoesLicenciamento>
    response: { message: 'Configurações atualizadas' }
  }

  // ✅ ALERTAS
  'GET /api/licenciamento/alertas': {
    query: {
      tipo?: 'critico' | 'aviso' | 'info'
      ativo?: boolean
    }
    response: AlertaLicenca[]
  }

  'POST /api/licenciamento/alertas/[id]/marcar-lido': {
    response: { message: 'Alerta marcado como lido' }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ LICENÇAS
CREATE TABLE licencas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Identificação
  numero_licenca VARCHAR(100) UNIQUE NOT NULL,
  tipo_licenca VARCHAR(20) NOT NULL, -- 'mensal', 'anual', 'vitalicia'
  plano VARCHAR(50) NOT NULL, -- 'basico', 'profissional', 'empresarial'

  -- Vigência
  data_inicio DATE NOT NULL,
  data_fim DATE, -- NULL para vitalícia

  -- Empresa
  empresa_licenciada VARCHAR(255) NOT NULL,
  cnpj_empresa VARCHAR(18) NOT NULL,

  -- Limites
  limites_recursos JSONB NOT NULL,
  funcionalidades_incluidas JSONB NOT NULL,

  -- Arquivo
  arquivo_licenca TEXT NOT NULL, -- Conteúdo .rllic criptografado
  hash_arquivo VARCHAR(64) NOT NULL,
  assinatura_digital TEXT NOT NULL,

  -- Status
  status VARCHAR(20) DEFAULT 'ativa',
  revogada BOOLEAN DEFAULT FALSE,
  data_revogacao TIMESTAMP,
  motivo_revogacao TEXT,

  -- Validação
  ultima_validacao TIMESTAMP,
  proxima_validacao TIMESTAMP,
  validacao_offline_ate TIMESTAMP,

  -- Auditoria
  importada_em TIMESTAMP DEFAULT NOW(),
  importada_por UUID REFERENCES users(id),

  CONSTRAINT chk_tipo_licenca CHECK (
    tipo_licenca IN ('mensal', 'anual', 'vitalicia')
  ),
  CONSTRAINT chk_status_licenca CHECK (
    status IN ('ativa', 'expirada', 'revogada', 'suspensa')
  ),
  CONSTRAINT chk_vigencia CHECK (
    (tipo_licenca = 'vitalicia' AND data_fim IS NULL) OR
    (tipo_licenca != 'vitalicia' AND data_fim IS NOT NULL)
  )
);

-- ✅ USO DE RECURSOS
CREATE TABLE uso_recursos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  licenca_id UUID NOT NULL REFERENCES licencas(id),

  -- Data
  data_medicao DATE NOT NULL,

  -- Recursos
  funcionarios_cadastrados INTEGER DEFAULT 0,
  funcionarios_ativos INTEGER DEFAULT 0,
  usuarios_sistema INTEGER DEFAULT 0,
  dispositivos_biometricos INTEGER DEFAULT 0,
  empresas_clientes INTEGER DEFAULT 0,
  armazenamento_usado_gb DECIMAL(10,2) DEFAULT 0,

  -- Atividade
  relatorios_gerados INTEGER DEFAULT 0,
  api_calls_realizadas INTEGER DEFAULT 0,
  reconhecimentos_biometricos INTEGER DEFAULT 0,

  -- Auditoria
  coletado_em TIMESTAMP DEFAULT NOW(),

  UNIQUE(licenca_id, data_medicao)
);

-- ✅ ALERTAS DE LICENCIAMENTO
CREATE TABLE alertas_licenciamento (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  licenca_id UUID NOT NULL REFERENCES licencas(id),

  -- Alerta
  tipo_alerta VARCHAR(50) NOT NULL,
  gravidade VARCHAR(20) NOT NULL, -- 'info', 'aviso', 'critico'
  titulo VARCHAR(255) NOT NULL,
  mensagem TEXT NOT NULL,

  -- Contexto
  recurso_afetado VARCHAR(100),
  valor_atual DECIMAL(10,2),
  valor_limite DECIMAL(10,2),
  percentual_uso DECIMAL(5,2),

  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  lido BOOLEAN DEFAULT FALSE,
  data_leitura TIMESTAMP,
  lido_por UUID REFERENCES users(id),

  -- Ações
  acao_automatica_executada BOOLEAN DEFAULT FALSE,
  detalhes_acao JSONB,

  -- Auditoria
  criado_em TIMESTAMP DEFAULT NOW(),

  CONSTRAINT chk_tipo_alerta CHECK (
    tipo_alerta IN (
      'expiracao_iminente', 'limite_funcionarios', 'limite_usuarios',
      'limite_dispositivos', 'limite_armazenamento', 'limite_api_calls',
      'licenca_revogada', 'validacao_falhou', 'upgrade_recomendado'
    )
  ),
  CONSTRAINT chk_gravidade CHECK (
    gravidade IN ('info', 'aviso', 'critico')
  )
);

-- ✅ HISTÓRICO DE LICENÇAS
CREATE TABLE historico_licencas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Licença
  numero_licenca VARCHAR(100) NOT NULL,
  tipo_licenca VARCHAR(20) NOT NULL,
  plano VARCHAR(50) NOT NULL,

  -- Período
  data_inicio DATE NOT NULL,
  data_fim DATE,
  data_substituicao TIMESTAMP DEFAULT NOW(),

  -- Motivo da Substituição
  motivo VARCHAR(100) NOT NULL, -- 'expiracao', 'upgrade', 'revogacao', 'renovacao'
  substituida_por VARCHAR(100), -- número da nova licença

  -- Uso Final
  uso_final JSONB,

  -- Auditoria
  arquivada_por UUID REFERENCES users(id)
);

-- ✅ CONFIGURAÇÕES DE LICENCIAMENTO
CREATE TABLE configuracoes_licenciamento (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Configuração
  chave VARCHAR(255) NOT NULL UNIQUE,
  valor JSONB NOT NULL,
  tipo VARCHAR(50) NOT NULL,

  -- Metadados
  descricao TEXT,
  categoria VARCHAR(100),

  -- Auditoria
  updated_at TIMESTAMP DEFAULT NOW(),
  updated_by UUID REFERENCES users(id)
);

-- ✅ ÍNDICES
CREATE INDEX idx_licencas_status ON licencas(status);
CREATE INDEX idx_licencas_vigencia ON licencas(data_inicio, data_fim);
CREATE INDEX idx_licencas_empresa ON licencas(cnpj_empresa);
CREATE INDEX idx_uso_recursos_data ON uso_recursos(data_medicao);
CREATE INDEX idx_uso_recursos_licenca ON uso_recursos(licenca_id);
CREATE INDEX idx_alertas_ativo ON alertas_licenciamento(ativo);
CREATE INDEX idx_alertas_gravidade ON alertas_licenciamento(gravidade);
CREATE INDEX idx_historico_numero ON historico_licencas(numero_licenca);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **💳 VALIDAÇÃO DE LICENÇAS:**

1. **Arquivo .rllic obrigatório** para importação
2. **Assinatura digital** deve ser válida
3. **Verificação de revogação** via API externa
4. **Backup automático** antes de substituir

### **📊 MONITORAMENTO DE USO:**

1. **Coleta diária** de métricas de uso
2. **Alertas automáticos** em 80%, 90%, 95% dos limites
3. **Bloqueio preventivo** ao atingir 100%
4. **Relatório mensal** enviado ao servidor externo

### **⚠️ ALERTAS E AÇÕES:**

1. **Expiração**: alertas 30, 15, 7, 1 dias antes
2. **Limites**: alertas progressivos por recurso
3. **Revogação**: verificação diária via API
4. **Ações automáticas**: configuráveis por tipo

### **🔄 COMUNICAÇÃO EXTERNA:**

1. **API de validação** consultada diariamente
2. **Métricas de uso** enviadas mensalmente
3. **Cache offline** para continuidade
4. **Fallback** em caso de falha de comunicação

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE VALIDAÇÃO:**

```typescript
describe('Validação de Licenças', () => {
  test('Importar licença válida', async () => {
    const arquivo = criarArquivoRllic({
      tipo: 'anual',
      plano: 'profissional',
      valida: true,
    })

    const resultado = await importarLicenca(arquivo)
    expect(resultado.sucesso).toBe(true)
    expect(resultado.nova_licenca.status).toBe('ativa')
  })

  test('Rejeitar licença expirada', async () => {
    const arquivo = criarArquivoRllic({
      data_fim: '2023-01-01', // expirada
      valida: true,
    })

    await expect(importarLicenca(arquivo)).rejects.toThrow('Licença expirada')
  })
})
```

### **✅ TESTES DE MONITORAMENTO:**

```typescript
describe('Monitoramento de Uso', () => {
  test('Gerar alerta ao atingir 90% do limite', async () => {
    await configurarLicenca({
      limite_funcionarios: 100,
    })

    await cadastrarFuncionarios(90)

    const alertas = await buscarAlertas({ ativo: true })
    expect(alertas).toHaveLength(1)
    expect(alertas[0].tipo_alerta).toBe('limite_funcionarios')
  })

  test('Bloquear cadastro ao atingir limite', async () => {
    await configurarLicenca({
      limite_funcionarios: 10,
    })

    await cadastrarFuncionarios(10)

    await expect(cadastrarFuncionario()).rejects.toThrow(
      'Limite de funcionários atingido'
    )
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Validação**

- Importação de .rllic
- Validação de assinatura
- Comunicação com API externa

**Semana 3-4: Monitoramento**

- Coleta de métricas
- Sistema de alertas
- Dashboard de uso

**Semana 5-6: Controle de Limites**

- Bloqueios automáticos
- Degradação de serviços
- Ações preventivas

**Semana 7-8: Configurações**

- Interface de configuração
- Notificações
- Testes completos

### **🔧 DEPENDÊNCIAS:**

- Sistema de criptografia (validação)
- API externa (servidor de licenças)
- Sistema de notificações
- Módulo de configurações
- Sistema de backup
- Monitoramento de recursos
