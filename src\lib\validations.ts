import { z } from 'zod'

export const emailSchema = z.string().email('Email inválido')

export const passwordSchema = z
  .string()
  .min(8, 'Senha deve ter pelo menos 8 caracteres')
  .regex(/[A-Z]/, '<PERSON>ha deve conter pelo menos uma letra mai<PERSON>')
  .regex(/[0-9]/, 'Senha deve conter pelo menos um número')

export const userSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: emailSchema,
  password: passwordSchema,
})

export type User = z.infer<typeof userSchema>
