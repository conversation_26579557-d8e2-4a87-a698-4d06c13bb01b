"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Building2, Save, ArrowLeft, MapPin, Calendar, FileText } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { z } from "zod"

// Schema de validação
const empresaClienteSchema = z.object({
  nomeFantasia: z.string().min(2, "Nome fantasia deve ter pelo menos 2 caracteres"),
  razaoSocial: z.string().min(2, "Razão social deve ter pelo menos 2 caracteres"),
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, "CNPJ deve estar no formato XX.XXX.XXX/XXXX-XX"),
  nomeProjeto: z.string().min(2, "Nome do projeto deve ter pelo menos 2 caracteres"),
  descricaoProjeto: z.string().optional(),
  dataInicio: z.string().min(1, "Data de início é obrigatória"),
  dataFimPrevista: z.string().optional(),
  endereco: z.object({
    cep: z.string().regex(/^\d{5}-\d{3}$/, "CEP deve estar no formato XXXXX-XXX"),
    logradouro: z.string().min(5, "Logradouro deve ter pelo menos 5 caracteres"),
    numero: z.string().min(1, "Número é obrigatório"),
    complemento: z.string().optional(),
    bairro: z.string().min(2, "Bairro deve ter pelo menos 2 caracteres"),
    cidade: z.string().min(2, "Cidade deve ter pelo menos 2 caracteres"),
    estado: z.string().length(2, "Estado deve ter 2 caracteres"),
  }),
  contatos: z.object({
    telefone: z.string().optional(),
    celular: z.string().optional(),
    email: z.string().email("Email inválido").optional(),
    responsavel: z.string().optional(),
  })
})

type EmpresaClienteForm = z.infer<typeof empresaClienteSchema>

export default function NovaEmpresaPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<EmpresaClienteForm>({
    nomeFantasia: "",
    razaoSocial: "",
    cnpj: "",
    nomeProjeto: "",
    descricaoProjeto: "",
    dataInicio: "",
    dataFimPrevista: "",
    endereco: {
      cep: "",
      logradouro: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      estado: "",
    },
    contatos: {
      telefone: "",
      celular: "",
      email: "",
      responsavel: "",
    }
  })

  // Função para buscar CEP
  const buscarCEP = async (cep: string) => {
    if (cep.length === 9) {
      try {
        const response = await fetch(`https://viacep.com.br/ws/${cep.replace('-', '')}/json/`)
        const data = await response.json()
        
        if (!data.erro) {
          setFormData(prev => ({
            ...prev,
            endereco: {
              ...prev.endereco,
              logradouro: data.logradouro || "",
              bairro: data.bairro || "",
              cidade: data.localidade || "",
              estado: data.uf || "",
            }
          }))
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error)
      }
    }
  }

  // Função para salvar empresa cliente
  const salvarEmpresa = async () => {
    try {
      setLoading(true)

      // Validar dados
      const validatedData = empresaClienteSchema.parse(formData)

      const response = await fetch('/api/empresas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...validatedData,
          tipoEmpresa: 'cliente'
        }),
      })

      if (response.ok) {
        toast({
          title: "Sucesso!",
          description: "Empresa cliente cadastrada com sucesso.",
        })
        router.push('/empresas')
      } else {
        const error = await response.json()
        toast({
          title: "Erro",
          description: error.message || "Erro ao cadastrar empresa cliente.",
          variant: "destructive",
        })
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0]
        toast({
          title: "Dados inválidos",
          description: firstError.message,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Erro",
          description: "Erro interno do servidor.",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                  <Building2 className="h-8 w-8 text-blue-600" />
                  Nova Empresa Cliente
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  Cadastre um novo projeto/obra
                </p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button onClick={salvarEmpresa} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Formulário Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Dados Básicos */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Dados da Empresa
                </CardTitle>
                <CardDescription>
                  Informações da empresa cliente
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="nomeFantasia">Nome Fantasia *</Label>
                    <Input
                      id="nomeFantasia"
                      value={formData.nomeFantasia}
                      onChange={(e) => setFormData(prev => ({ ...prev, nomeFantasia: e.target.value }))}
                      placeholder="Cliente ABC Ltda"
                    />
                  </div>
                  <div>
                    <Label htmlFor="razaoSocial">Razão Social *</Label>
                    <Input
                      id="razaoSocial"
                      value={formData.razaoSocial}
                      onChange={(e) => setFormData(prev => ({ ...prev, razaoSocial: e.target.value }))}
                      placeholder="Cliente ABC Limitada"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="cnpj">CNPJ *</Label>
                    <Input
                      id="cnpj"
                      value={formData.cnpj}
                      onChange={(e) => {
                        // Aplicar máscara de CNPJ
                        let value = e.target.value.replace(/\D/g, '')
                        value = value.replace(/^(\d{2})(\d)/, '$1.$2')
                        value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
                        value = value.replace(/\.(\d{3})(\d)/, '.$1/$2')
                        value = value.replace(/(\d{4})(\d)/, '$1-$2')
                        setFormData(prev => ({ ...prev, cnpj: value }))
                      }}
                      placeholder="XX.XXX.XXX/XXXX-XX"
                      maxLength={18}
                    />
                  </div>
                  <div>
                    <Label htmlFor="responsavel">Responsável</Label>
                    <Input
                      id="responsavel"
                      value={formData.contatos.responsavel}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        contatos: { ...prev.contatos, responsavel: e.target.value }
                      }))}
                      placeholder="João Silva"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dados do Projeto */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Dados do Projeto
                </CardTitle>
                <CardDescription>
                  Informações específicas do projeto/obra
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="nomeProjeto">Nome do Projeto *</Label>
                  <Input
                    id="nomeProjeto"
                    value={formData.nomeProjeto}
                    onChange={(e) => setFormData(prev => ({ ...prev, nomeProjeto: e.target.value }))}
                    placeholder="Edifício Residencial ABC"
                  />
                </div>

                <div>
                  <Label htmlFor="descricaoProjeto">Descrição do Projeto</Label>
                  <Textarea
                    id="descricaoProjeto"
                    value={formData.descricaoProjeto}
                    onChange={(e) => setFormData(prev => ({ ...prev, descricaoProjeto: e.target.value }))}
                    placeholder="Descrição detalhada do projeto..."
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="dataInicio">Data de Início *</Label>
                    <Input
                      id="dataInicio"
                      type="date"
                      value={formData.dataInicio}
                      onChange={(e) => setFormData(prev => ({ ...prev, dataInicio: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dataFimPrevista">Data de Fim Prevista</Label>
                    <Input
                      id="dataFimPrevista"
                      type="date"
                      value={formData.dataFimPrevista}
                      onChange={(e) => setFormData(prev => ({ ...prev, dataFimPrevista: e.target.value }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant="secondary" className="w-full justify-center">
                  Novo Cadastro
                </Badge>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
