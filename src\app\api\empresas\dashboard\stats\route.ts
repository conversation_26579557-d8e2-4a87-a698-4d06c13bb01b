import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"

const prisma = new PrismaClient()

// GET /api/empresas/dashboard/stats - Obter estatísticas para o dashboard
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Data atual e data de 30 dias atrás
    const hoje = new Date()
    const trintaDiasAtras = new Date()
    trintaDiasAtras.setDate(hoje.getDate() - 30)
    
    const sessentaDiasAtras = new Date()
    sessentaDiasAtras.setDate(hoje.getDate() - 60)

    // Buscar estatísticas de empresas
    const [
      totalEmpresas,
      empresasAtivas,
      empresasClientes,
      empresasNovasMes,
      empresasNovasMesAnterior,
      totalFuncionarios,
      funcionariosAtivos,
      alocacoesAtivas,
      projetosAtivos,
      projetosFinalizadosMes,
      novasAlocacoes,
      funcionariosAdmitidos,
      projetosIniciados
    ] = await Promise.all([
      // Total de empresas
      prisma.empresa.count(),
      
      // Empresas ativas
      prisma.empresa.count({
        where: { ativo: true }
      }),
      
      // Empresas clientes
      prisma.empresa.count({
        where: { 
          tipoEmpresa: 'cliente',
          ativo: true
        }
      }),
      
      // Empresas novas no último mês
      prisma.empresa.count({
        where: {
          createdAt: {
            gte: trintaDiasAtras
          }
        }
      }),
      
      // Empresas novas no mês anterior
      prisma.empresa.count({
        where: {
          createdAt: {
            gte: sessentaDiasAtras,
            lt: trintaDiasAtras
          }
        }
      }),
      
      // Total de funcionários
      prisma.funcionario.count(),
      
      // Funcionários ativos
      prisma.funcionario.count({
        where: { ativo: true }
      }),
      
      // Alocações ativas
      prisma.funcionarioAlocacao.count({
        where: { status: 'ativo' }
      }),
      
      // Projetos ativos (empresas clientes ativas)
      prisma.empresa.count({
        where: {
          tipoEmpresa: 'cliente',
          ativo: true
        }
      }),
      
      // Projetos finalizados no mês
      prisma.empresa.count({
        where: {
          tipoEmpresa: 'cliente',
          dataFimReal: {
            gte: trintaDiasAtras
          }
        }
      }),
      
      // Novas alocações nos últimos 30 dias
      prisma.funcionarioAlocacao.count({
        where: {
          createdAt: {
            gte: trintaDiasAtras
          }
        }
      }),
      
      // Funcionários admitidos nos últimos 30 dias
      prisma.funcionario.count({
        where: {
          dataAdmissao: {
            gte: trintaDiasAtras
          }
        }
      }),
      
      // Projetos iniciados nos últimos 30 dias
      prisma.empresa.count({
        where: {
          tipoEmpresa: 'cliente',
          dataInicio: {
            gte: trintaDiasAtras
          }
        }
      })
    ])

    // Calcular valor total por hora
    const alocacoes = await prisma.funcionarioAlocacao.findMany({
      where: { status: 'ativo' },
      select: {
        valorHoraProjeto: true
      }
    })

    const valorTotalHora = alocacoes.reduce((total, alocacao) => {
      return total + (Number(alocacao.valorHoraProjeto) || 0)
    }, 0)

    // Calcular valor total por hora do mês anterior
    const alocacoesMesAnterior = await prisma.funcionarioAlocacao.findMany({
      where: {
        createdAt: {
          gte: sessentaDiasAtras,
          lt: trintaDiasAtras
        }
      },
      select: {
        valorHoraProjeto: true
      }
    })

    const valorTotalHoraMesAnterior = alocacoesMesAnterior.reduce((total, alocacao) => {
      return total + (Number(alocacao.valorHoraProjeto) || 0)
    }, 0)

    // Calcular crescimento
    const crescimentoEmpresas = empresasNovasMesAnterior > 0 
      ? ((empresasNovasMes - empresasNovasMesAnterior) / empresasNovasMesAnterior) * 100 
      : 0

    const crescimentoValor = valorTotalHoraMesAnterior > 0 
      ? ((valorTotalHora - valorTotalHoraMesAnterior) / valorTotalHoraMesAnterior) * 100 
      : 0

    // Montar resposta
    const stats = {
      empresas: {
        total: totalEmpresas,
        ativas: empresasAtivas,
        clientes: empresasClientes,
        crescimento_mensal: Math.round(crescimentoEmpresas)
      },
      funcionarios: {
        total: totalFuncionarios,
        alocados: alocacoesAtivas,
        taxa_alocacao: funcionariosAtivos > 0 
          ? Math.round((alocacoesAtivas / funcionariosAtivos) * 100) 
          : 0,
        crescimento_mensal: 0 // Não temos dados para calcular
      },
      projetos: {
        ativos: projetosAtivos,
        finalizados_mes: projetosFinalizadosMes,
        valor_total_hora: valorTotalHora,
        crescimento_valor: Math.round(crescimentoValor)
      },
      atividade_recente: {
        novas_alocacoes: novasAlocacoes,
        funcionarios_admitidos: funcionariosAdmitidos,
        projetos_iniciados: projetosIniciados
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
