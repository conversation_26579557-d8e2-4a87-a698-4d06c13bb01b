import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"

const prisma = new PrismaClient()

// GET /api/empresas/dashboard/projetos-recentes - Obter projetos recentes para o dashboard
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Buscar empresas clientes (projetos) mais recentes
    const empresasClientes = await prisma.empresa.findMany({
      where: {
        tipoEmpresa: 'cliente'
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      take: 10,
      include: {
        funcionarioAlocacoes: {
          where: { status: 'ativo' },
          select: {
            valorHoraProjeto: true
          }
        },
        _count: {
          select: {
            funcionarioAlocacoes: {
              where: { status: 'ativo' }
            }
          }
        }
      }
    })

    // Formatar dados para resposta
    const projetos = empresasClientes.map(empresa => {
      // Calcular valor total por hora
      const valorTotalHora = empresa.funcionarioAlocacoes.reduce((total, alocacao) => {
        return total + (Number(alocacao.valorHoraProjeto) || 0)
      }, 0)

      return {
        id: empresa.id,
        nomeFantasia: empresa.nomeFantasia,
        nomeProjeto: empresa.nomeProjeto,
        dataInicio: empresa.dataInicio,
        dataFimPrevista: empresa.dataFimPrevista,
        funcionarios_alocados: empresa._count.funcionarioAlocacoes,
        valor_total_hora: valorTotalHora,
        status: empresa.ativo ? 'ativo' : 'inativo'
      }
    })

    return NextResponse.json({ projetos })

  } catch (error) {
    console.error('Erro ao buscar projetos recentes:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
