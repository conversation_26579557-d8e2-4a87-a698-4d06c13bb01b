const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDatabase() {
  try {
    console.log('🔄 Testando conexão com o banco de dados...')
    
    // Testar conexão
    await prisma.$connect()
    console.log('✅ Conexão com banco de dados estabelecida!')
    
    // Contar usuários
    const userCount = await prisma.user.count()
    console.log(`👥 Total de usuários: ${userCount}`)
    
    // Contar empresas
    const empresaCount = await prisma.empresa.count()
    console.log(`🏢 Total de empresas: ${empresaCount}`)
    
    // Listar usuários
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        nome: true,
        nivelAcesso: true,
        ativo: true
      }
    })
    
    console.log('\n📋 Usuários cadastrados:')
    users.forEach(user => {
      console.log(`  - ${user.email} (${user.nome}) - ${user.nivelAcesso} - ${user.ativo ? 'Ativo' : 'Inativo'}`)
    })
    
    console.log('\n✅ Teste concluído com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro ao testar banco de dados:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
