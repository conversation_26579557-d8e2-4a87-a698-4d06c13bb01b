import { PrismaClient } from "@prisma/client"
import bcrypt from "bcryptjs"
import <PERSON>Auth from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema de validação para login
const loginSchema = z.object({
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres")
})

export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>"
        },
        password: {
          label: "<PERSON>ha",
          type: "password"
        }
      },
      async authorize(credentials) {
        try {
          // Validar entrada
          const validatedFields = loginSchema.safeParse(credentials)
          
          if (!validatedFields.success) {
            console.log("❌ Campos inválidos:", validatedFields.error.flatten().fieldErrors)
            return null
          }

          const { email, password } = validatedFields.data

          // Buscar usuário no banco
          const user = await prisma.user.findUnique({
            where: { 
              email: email.toLowerCase(),
              ativo: true 
            }
          })

          if (!user || !user.passwordHash) {
            console.log("❌ Usuário não encontrado ou inativo:", email)
            return null
          }

          // Verificar senha
          const isValidPassword = await bcrypt.compare(password, user.passwordHash)
          
          if (!isValidPassword) {
            console.log("❌ Senha inválida para:", email)
            return null
          }

          console.log("✅ Login bem-sucedido:", email)

          // Retornar dados do usuário para a sessão
          return {
            id: user.id,
            email: user.email,
            name: user.nome,
            nivelAcesso: user.nivelAcesso,
            ativo: user.ativo
          }
        } catch (error) {
          console.error("❌ Erro na autenticação:", error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 horas
  },
  jwt: {
    maxAge: 8 * 60 * 60, // 8 horas
  },
  callbacks: {
    async jwt({ token, user }) {
      // Incluir dados do usuário no token JWT
      if (user) {
        token.id = user.id
        token.nivelAcesso = user.nivelAcesso
        token.ativo = user.ativo
      }
      return token
    },
    async session({ session, token }) {
      // Incluir dados do token na sessão
      if (token) {
        session.user.id = token.id as string
        session.user.nivelAcesso = token.nivelAcesso as string
        session.user.ativo = token.ativo as boolean
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Redirecionar para dashboard após login
      if (url.startsWith("/")) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  events: {
    async signOut(message) {
      // Log de auditoria para logout (simplificado)
      try {
        if ('token' in message && message.token?.id) {
          console.log("🔄 User logout:", message.token.id)
        }
      } catch (error) {
        console.error("❌ Error in signOut event:", error)
      }
    }
  },
  debug: process.env.NODE_ENV === "development",
})

// Tipos para TypeScript
declare module "next-auth" {
  interface User {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name: string
      nivelAcesso: string
      ativo: boolean
    }
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
}

// Função helper para verificar permissões
export function hasPermission(userLevel: string, requiredLevel: string): boolean {
  const levels = [
    'readonly',
    'operador_biometria', 
    'cliente_vinculado',
    'supervisor_obra',
    'gerente_rh',
    'admin_limitado',
    'admin_total'
  ]
  
  const userIndex = levels.indexOf(userLevel)
  const requiredIndex = levels.indexOf(requiredLevel)
  
  return userIndex >= requiredIndex
}

// Função helper para verificar se usuário está ativo
export function isUserActive(user: { ativo?: boolean } | null | undefined): boolean {
  return user?.ativo === true
}

// Middleware de autenticação para API routes
export async function requireAuth(_req: Request) {
  const session = await auth()
  
  if (!session?.user) {
    throw new Error("Não autenticado")
  }
  
  if (!isUserActive(session.user)) {
    throw new Error("Usuário inativo")
  }
  
  return session.user
}

// Middleware de autorização para API routes
export async function requirePermission(_req: Request, requiredLevel: string) {
  const user = await requireAuth(_req)
  
  if (!hasPermission(user.nivelAcesso, requiredLevel)) {
    throw new Error("Permissão insuficiente")
  }
  
  return user
}
