import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth-basic"
import { PrismaClient } from "@prisma/client"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema de validação
const registroPontoSchema = z.object({
  funcionarioId: z.string().min(1, "ID do funcionário é obrigatório"),
  tipoRegistro: z.enum(['entrada', 'saida', 'intervalo_inicio', 'intervalo_fim'], {
    errorMap: () => ({ message: "Tipo de registro inválido" })
  }),
  origemRegistro: z.enum(['biometrico', 'manual', 'web'], {
    errorMap: () => ({ message: "Origem do registro inválida" })
  }),
  localizacao: z.string().optional(),
  observacoes: z.string().optional(),
  supervisorId: z.string().optional(), // Para registros manuais
  fotoBase64: z.string().optional(), // Para registros manuais
})

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    // Validar dados de entrada
    const validatedData = registroPontoSchema.parse(body)

    // Verificar se o funcionário existe e está ativo
    const funcionario = await prisma.funcionario.findFirst({
      where: {
        id: validatedData.funcionarioId,
        ativo: true
      },
      include: {
        empresa: true
      }
    })

    if (!funcionario) {
      return NextResponse.json(
        { error: "Funcionário não encontrado ou inativo" },
        { status: 404 }
      )
    }

    // Buscar último registro do funcionário para validações
    const ultimoRegistro = await prisma.registroPonto.findFirst({
      where: {
        funcionarioId: validatedData.funcionarioId
      },
      orderBy: {
        dataHora: 'desc'
      }
    })

    // Validações de sequência de batidas
    const agora = new Date()
    const hojeInicio = new Date(agora.getFullYear(), agora.getMonth(), agora.getDate())
    const hojeFim = new Date(hojeInicio.getTime() + 24 * 60 * 60 * 1000)

    // Contar registros do dia
    const registrosHoje = await prisma.registroPonto.count({
      where: {
        funcionarioId: validatedData.funcionarioId,
        dataHora: {
          gte: hojeInicio,
          lt: hojeFim
        }
      }
    })

    // Aplicar princípio "NUNCA IMPEDIR" - sempre permitir registro
    // Mas classificar irregularidades para posterior análise
    let classificacao = 'normal'
    let alertas: string[] = []

    // Verificar sequência lógica
    if (ultimoRegistro) {
      const ultimoTipo = ultimoRegistro.tipoRegistro
      const novoTipo = validatedData.tipoRegistro
      
      // Verificar se a sequência faz sentido
      const sequenciasValidas = {
        'entrada': ['saida', 'intervalo_inicio'],
        'intervalo_inicio': ['intervalo_fim'],
        'intervalo_fim': ['saida', 'intervalo_inicio'],
        'saida': ['entrada']
      }

      if (ultimoTipo && sequenciasValidas[ultimoTipo] && 
          !sequenciasValidas[ultimoTipo].includes(novoTipo)) {
        classificacao = 'sequencia_irregular'
        alertas.push(`Sequência irregular: ${ultimoTipo} → ${novoTipo}`)
      }
    }

    // Verificar múltiplos registros do mesmo tipo no dia
    const registrosMesmoTipo = await prisma.registroPonto.count({
      where: {
        funcionarioId: validatedData.funcionarioId,
        tipoRegistro: validatedData.tipoRegistro,
        dataHora: {
          gte: hojeInicio,
          lt: hojeFim
        }
      }
    })

    if (registrosMesmoTipo > 0) {
      classificacao = 'duplicado'
      alertas.push(`Registro duplicado do tipo ${validatedData.tipoRegistro}`)
    }

    // Verificar horário (tolerâncias configuráveis)
    const hora = agora.getHours()
    if (validatedData.tipoRegistro === 'entrada' && (hora < 5 || hora > 10)) {
      classificacao = 'horario_irregular'
      alertas.push('Entrada fora do horário normal')
    }

    // Para registros manuais, exigir supervisor
    if (validatedData.origemRegistro === 'manual' && !validatedData.supervisorId) {
      return NextResponse.json(
        { error: "Registro manual requer autorização de supervisor" },
        { status: 400 }
      )
    }

    // Criar o registro de ponto
    const novoRegistro = await prisma.registroPonto.create({
      data: {
        funcionarioId: validatedData.funcionarioId,
        dataHora: agora,
        tipoRegistro: validatedData.tipoRegistro,
        origemRegistro: validatedData.origemRegistro,
        localizacao: validatedData.localizacao,
        observacoes: validatedData.observacoes,
        validado: validatedData.origemRegistro === 'biometrico', // Biométrico é auto-validado
        classificacao,
        alertas: alertas.length > 0 ? alertas.join('; ') : null,
        supervisorId: validatedData.supervisorId,
        fotoBase64: validatedData.fotoBase64,
        criadoPor: session.user.id,
        criadoEm: agora,
        atualizadoEm: agora
      },
      include: {
        funcionario: {
          select: {
            nomeCompleto: true,
            matricula: true,
            cargo: true,
            empresa: {
              select: {
                nomeFantasia: true
              }
            }
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        acao: 'REGISTRO_PONTO',
        tabela: 'RegistroPonto',
        registroId: novoRegistro.id,
        dadosAnteriores: null,
        dadosNovos: {
          funcionario: funcionario.nomeCompleto,
          tipo: validatedData.tipoRegistro,
          origem: validatedData.origemRegistro,
          classificacao
        },
        usuarioId: session.user.id,
        ip: request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        criadoEm: agora
      }
    })

    // Resposta de sucesso
    return NextResponse.json({
      success: true,
      registro: {
        id: novoRegistro.id,
        funcionario: novoRegistro.funcionario,
        dataHora: novoRegistro.dataHora,
        tipoRegistro: novoRegistro.tipoRegistro,
        origemRegistro: novoRegistro.origemRegistro,
        classificacao: novoRegistro.classificacao,
        alertas: alertas
      },
      message: alertas.length > 0 
        ? `Ponto registrado com alertas: ${alertas.join(', ')}`
        : 'Ponto registrado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao registrar ponto:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Endpoint para buscar funcionário
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const busca = searchParams.get('busca')

    if (!busca) {
      return NextResponse.json(
        { error: "Parâmetro de busca é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar funcionário por matrícula, nome ou CPF
    const funcionarios = await prisma.funcionario.findMany({
      where: {
        ativo: true,
        OR: [
          { matricula: { contains: busca, mode: 'insensitive' } },
          { nomeCompleto: { contains: busca, mode: 'insensitive' } },
          { cpf: { contains: busca.replace(/\D/g, ''), mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        nomeCompleto: true,
        matricula: true,
        cpf: true,
        cargo: true,
        empresa: {
          select: {
            nomeFantasia: true
          }
        }
      },
      take: 10
    })

    // Para cada funcionário, buscar último registro
    const funcionariosComUltimoRegistro = await Promise.all(
      funcionarios.map(async (funcionario) => {
        const ultimoRegistro = await prisma.registroPonto.findFirst({
          where: {
            funcionarioId: funcionario.id
          },
          orderBy: {
            dataHora: 'desc'
          },
          select: {
            tipoRegistro: true,
            dataHora: true,
            origemRegistro: true
          }
        })

        return {
          ...funcionario,
          ultimoRegistro
        }
      })
    )

    return NextResponse.json({
      funcionarios: funcionariosComUltimoRegistro
    })

  } catch (error) {
    console.error('Erro ao buscar funcionários:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
