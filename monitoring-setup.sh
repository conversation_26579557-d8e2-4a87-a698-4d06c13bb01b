#!/bin/bash

# Script de Configuração de Monitoramento RLPONTO
# Configura logs estruturados, métricas e alertas básicos

echo "🔧 Configurando monitoramento do RLPONTO..."

# 1. <PERSON><PERSON>r diret<PERSON><PERSON> de logs estruturados
mkdir -p /opt/rlponto/logs/{app,nginx,system,audit}
chown -R rlponto:rlponto /opt/rlponto/logs

# 2. Configurar logrotate para logs da aplicação
cat > /etc/logrotate.d/rlponto << 'EOF'
/opt/rlponto/logs/app/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 rlponto rlponto
    postrotate
        pm2 reload rlponto
    endscript
}

/var/log/nginx/rlponto_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
EOF

# 3. Script de monitoramento de saúde
cat > /opt/rlponto/health-check.sh << 'EOF'
#!/bin/bash

# Health Check Script para RLPONTO
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
LOG_FILE="/opt/rlponto/logs/system/health-check.log"

# Função de log
log() {
    echo "[$TIMESTAMP] $1" >> $LOG_FILE
}

# Verificar se PM2 está rodando
if pm2 status rlponto | grep -q "online"; then
    log "✅ PM2: RLPONTO está online"
    PM2_STATUS="OK"
else
    log "❌ PM2: RLPONTO está offline"
    PM2_STATUS="ERROR"
fi

# Verificar se a aplicação responde
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$HTTP_STATUS" = "200" ]; then
    log "✅ HTTP: Aplicação respondendo (200)"
    HTTP_STATUS="OK"
else
    log "❌ HTTP: Aplicação não responde ($HTTP_STATUS)"
    HTTP_STATUS="ERROR"
fi

# Verificar PostgreSQL
if pg_isready -h localhost -p 5432 -U rlponto_user > /dev/null 2>&1; then
    log "✅ PostgreSQL: Conectado"
    DB_STATUS="OK"
else
    log "❌ PostgreSQL: Falha na conexão"
    DB_STATUS="ERROR"
fi

# Verificar Redis
if redis-cli ping > /dev/null 2>&1; then
    log "✅ Redis: Conectado"
    REDIS_STATUS="OK"
else
    log "❌ Redis: Falha na conexão"
    REDIS_STATUS="ERROR"
fi

# Verificar uso de memória
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
    log "✅ Memória: ${MEMORY_USAGE}% (OK)"
    MEMORY_STATUS="OK"
else
    log "⚠️ Memória: ${MEMORY_USAGE}% (ALTO)"
    MEMORY_STATUS="WARNING"
fi

# Verificar uso de disco
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    log "✅ Disco: ${DISK_USAGE}% (OK)"
    DISK_STATUS="OK"
else
    log "⚠️ Disco: ${DISK_USAGE}% (ALTO)"
    DISK_STATUS="WARNING"
fi

# Status geral
if [ "$PM2_STATUS" = "OK" ] && [ "$HTTP_STATUS" = "OK" ] && [ "$DB_STATUS" = "OK" ] && [ "$REDIS_STATUS" = "OK" ]; then
    OVERALL_STATUS="HEALTHY"
    log "🎉 Sistema: SAUDÁVEL"
else
    OVERALL_STATUS="UNHEALTHY"
    log "🚨 Sistema: PROBLEMAS DETECTADOS"
fi

# Criar arquivo de status JSON para APIs
cat > /opt/rlponto/logs/system/status.json << EOL
{
    "timestamp": "$TIMESTAMP",
    "status": "$OVERALL_STATUS",
    "services": {
        "pm2": "$PM2_STATUS",
        "http": "$HTTP_STATUS",
        "database": "$DB_STATUS",
        "redis": "$REDIS_STATUS"
    },
    "resources": {
        "memory_usage": "${MEMORY_USAGE}%",
        "disk_usage": "${DISK_USAGE}%",
        "memory_status": "$MEMORY_STATUS",
        "disk_status": "$DISK_STATUS"
    }
}
EOL

# Se houver problemas, enviar alerta (placeholder)
if [ "$OVERALL_STATUS" = "UNHEALTHY" ]; then
    # Aqui poderia enviar email, SMS, webhook, etc.
    log "🚨 ALERTA: Sistema com problemas - notificação enviada"
fi
EOF

chmod +x /opt/rlponto/health-check.sh

# 4. Configurar cron para health check a cada 5 minutos
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/rlponto/health-check.sh") | crontab -

# 5. Script de métricas de performance
cat > /opt/rlponto/metrics-collector.sh << 'EOF'
#!/bin/bash

# Coletor de Métricas RLPONTO
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
METRICS_FILE="/opt/rlponto/logs/system/metrics.log"

# CPU Usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')

# Memory Usage
MEMORY_INFO=$(free -m | grep Mem)
MEMORY_TOTAL=$(echo $MEMORY_INFO | awk '{print $2}')
MEMORY_USED=$(echo $MEMORY_INFO | awk '{print $3}')
MEMORY_PERCENT=$(echo "scale=2; $MEMORY_USED * 100 / $MEMORY_TOTAL" | bc)

# PM2 Memory Usage
PM2_MEMORY=$(pm2 jlist | jq -r '.[] | select(.name=="rlponto") | .memory' 2>/dev/null || echo "0")
PM2_CPU=$(pm2 jlist | jq -r '.[] | select(.name=="rlponto") | .cpu' 2>/dev/null || echo "0")

# Database Connections
DB_CONNECTIONS=$(psql -h localhost -U rlponto_user -d rlponto_db -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname='rlponto_db';" 2>/dev/null || echo "0")

# Log metrics in JSON format
echo "{\"timestamp\":\"$TIMESTAMP\",\"cpu_usage\":\"$CPU_USAGE\",\"memory_total\":$MEMORY_TOTAL,\"memory_used\":$MEMORY_USED,\"memory_percent\":$MEMORY_PERCENT,\"pm2_memory\":$PM2_MEMORY,\"pm2_cpu\":$PM2_CPU,\"db_connections\":$DB_CONNECTIONS}" >> $METRICS_FILE
EOF

chmod +x /opt/rlponto/metrics-collector.sh

# 6. Configurar cron para métricas a cada minuto
(crontab -l 2>/dev/null; echo "* * * * * /opt/rlponto/metrics-collector.sh") | crontab -

# 7. Criar dashboard simples de status
cat > /opt/rlponto/dashboard.sh << 'EOF'
#!/bin/bash

# Dashboard de Status RLPONTO
clear
echo "🚀 RLPONTO - Dashboard de Status"
echo "=================================="
echo ""

# Status atual
if [ -f "/opt/rlponto/logs/system/status.json" ]; then
    STATUS=$(cat /opt/rlponto/logs/system/status.json | jq -r '.status')
    TIMESTAMP=$(cat /opt/rlponto/logs/system/status.json | jq -r '.timestamp')
    
    echo "📊 Status Geral: $STATUS"
    echo "🕐 Última verificação: $TIMESTAMP"
    echo ""
    
    echo "🔧 Serviços:"
    cat /opt/rlponto/logs/system/status.json | jq -r '.services | to_entries[] | "  \(.key): \(.value)"'
    echo ""
    
    echo "💾 Recursos:"
    cat /opt/rlponto/logs/system/status.json | jq -r '.resources | to_entries[] | "  \(.key): \(.value)"'
    echo ""
fi

# PM2 Status
echo "🔄 PM2 Status:"
pm2 status

echo ""
echo "📈 Acesse: http://10.19.208.8"
echo "📝 Logs: /opt/rlponto/logs/"
EOF

chmod +x /opt/rlponto/dashboard.sh

echo "✅ Monitoramento configurado com sucesso!"
echo ""
echo "📋 Comandos úteis:"
echo "  - Health Check: /opt/rlponto/health-check.sh"
echo "  - Dashboard: /opt/rlponto/dashboard.sh"
echo "  - Logs: tail -f /opt/rlponto/logs/system/health-check.log"
echo "  - Métricas: tail -f /opt/rlponto/logs/system/metrics.log"
