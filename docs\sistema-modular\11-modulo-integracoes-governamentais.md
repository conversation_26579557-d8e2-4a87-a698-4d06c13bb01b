# 🏛️ MÓDULO DE INTEGRAÇÕES GOVERNAMENTAIS

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Integrar com eSocial para envio de eventos trabalhistas
- Conectar com FGTS Digital para informações do fundo
- Implementar conformidade com Portaria 671/2021
- Garantir compliance com legislação trabalhista
- Automatizar envios obrigatórios

### **📊 RESPONSABILIDADES:**

- Envio automático de eventos eSocial
- Consulta e envio FGTS Digital
- Assinatura digital de registros (Portaria 671)
- Backup obrigatório de dados
- Relatórios de conformidade
- Tratamento de retornos e erros

---

## 🚀 FUNCIONALIDADES

### **✅ INTEGRAÇÃO eSocial:**

#### **🔐 CERTIFICAÇÃO DIGITAL OBRIGATÓRIA:**

```typescript
interface CertificadoDigital {
  tipo: 'A1' | 'A3' // A1 = arquivo, A3 = token/smartcard
  validade: Date
  cnpj_vinculado: string
  senha_certificado: string // Criptografada
  caminho_arquivo?: string // Para A1
  serial_token?: string // Para A3

  validacao: {
    verificar_validade: boolean
    verificar_cadeia_certificacao: boolean
    verificar_revogacao: boolean
  }
}
```

#### **1. EVENTOS OBRIGATÓRIOS:**

- **S-1000**: Informações do Empregador
- **S-1005**: Tabela de Estabelecimentos
- **S-1010**: Tabela de Rubricas
- **S-1020**: Tabela de Lotações Tributárias
- **S-1030**: Tabela de Cargos
- **S-2190**: Admissão de Trabalhador - Registro Preliminar
- **S-2200**: Cadastramento Inicial do Vínculo
- **S-2206**: Alteração de Contrato de Trabalho
- **S-2230**: Afastamento Temporário
- **S-2298**: Reintegração/Outros Provimentos
- **S-2299**: Desligamento
- **S-2300**: Trabalhador Sem Vínculo - Início
- **S-1200**: Remuneração de Trabalhador
- **S-1210**: Pagamentos de Rendimentos do Trabalho

#### **2. MAPEAMENTO DE DADOS:**

```typescript
interface ESocialEventMapping {
  // S-2200 - Cadastramento Inicial
  s2200_cadastramento: {
    cpfTrabalhador: string
    nisTrabalhador: string
    nmTrabalhador: string
    sexo: 'M' | 'F'
    racaCor: 1 | 2 | 3 | 4 | 5 | 6
    estCiv: 1 | 2 | 3 | 4 | 5
    grauInstr: string
    nmMae: string
    dtNascto: string // YYYY-MM-DD

    // Dados do contrato
    matricula: string
    tpRegTrab: 1 | 2 // CLT ou Estatutário
    tpRegPrev: 1 | 2 | 3 // RGPS, RPPS, etc
    dtAdm: string
    tpAdmissao: 1 | 2 | 3 | 4 | 5
    indAdmissao: 1 | 2 | 3
    codCargo: string
    codFuncao?: string
    codCateg: string

    // Remuneração
    vrSalFx: number
    undSalFixo: 1 | 2 | 3 | 4 | 5 | 6 | 7
    dscSalVar?: string

    // Jornada
    tpJornada: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
    tmpParc: 0 | 1 | 2 | 3
    horContratual: string // Formato: HHMM
    dscTpJorn?: string
  }

  // S-1200 - Remuneração
  s1200_remuneracao: {
    cpfBenef: string
    dtIniApur: string // YYYY-MM
    dtFimApur: string // YYYY-MM

    // Rubricas
    itensRemun: {
      codRubr: string
      ideTabRubr: string
      qtdRubr?: number
      fatorRubr?: number
      vrUnit?: number
      vrRubr: number
    }[]

    // Totalizadores
    infoAgNocivo?: {
      grauExp: 1 | 2 | 3 | 4
    }
  }
}
```

#### **3. FILA DE ENVIO:**

```typescript
interface ESocialQueue {
  // Adicionar evento à fila
  adicionarEvento(
    tipoEvento: string,
    dados: any,
    prioridade: 'alta' | 'media' | 'baixa'
  ): Promise<void>

  // Processar fila
  processarFila(): Promise<void>

  // Reprocessar eventos com erro
  reprocessarErros(): Promise<void>

  // Status da fila
  obterStatus(): Promise<{
    pendentes: number
    processando: number
    enviados: number
    erros: number
  }>
}
```

#### **4. TRATAMENTO DE RETORNOS E ERROS:**

```typescript
interface ESocialReturnHandler {
  // Processar retorno do eSocial
  processarRetorno(retorno: ESocialReturn): Promise<void>

  // Tipos de retorno
  tipos_retorno: {
    '101': 'Sucesso'
    '201': 'Sucesso com advertência'
    '301': 'Erro - evento rejeitado'
    '401': 'Erro - evento duplicado'
    '501': 'Erro - falha na validação'
  }

  // Ações por tipo de retorno
  acoes_retorno: {
    sucesso: 'marcar_como_enviado'
    advertencia: 'marcar_enviado_com_alerta'
    erro_rejeicao: 'corrigir_e_reenviar'
    erro_duplicado: 'marcar_como_duplicado'
    erro_validacao: 'corrigir_dados_e_reenviar'
  }

  // Retry automático
  retry_config: {
    max_tentativas: 3
    intervalo_base: 60 // segundos
    backoff_exponencial: true
    erros_que_nao_reenviam: ['401', 'duplicado']
  }
}
```

### **✅ INTEGRAÇÃO FGTS DIGITAL:**

#### **1. CONECTIVIDADE SOCIAL:**

```typescript
interface FGTSDigitalAPI {
  // Consultar situação do trabalhador
  consultarSituacao(cpf: string): Promise<{
    situacao: 'ativo' | 'inativo'
    contaVinculada: boolean
    saldoFGTS: number
    ultimoDeposito: Date
  }>

  // Enviar informações de admissão
  enviarAdmissao(dados: {
    cpfTrabalhador: string
    dataAdmissao: Date
    salario: number
    codigoEstabelecimento: string
  }): Promise<{
    protocolo: string
    status: 'sucesso' | 'erro'
    mensagem: string
  }>

  // Enviar informações de desligamento
  enviarDesligamento(dados: {
    cpfTrabalhador: string
    dataDesligamento: Date
    tipoDesligamento: string
    valorRescisao: number
  }): Promise<{
    protocolo: string
    status: 'sucesso' | 'erro'
    mensagem: string
  }>
}
```

### **✅ CONFORMIDADE PORTARIA 671/2021:**

#### **1. ASSINATURA DIGITAL:**

```typescript
interface AssinaturaDigital {
  // Assinar registro de ponto
  assinarRegistro(registro: {
    funcionarioId: string
    dataHora: Date
    tipoRegistro: string
    localizacao?: Coordenadas
  }): Promise<{
    hash: string
    assinatura: string
    certificado: string
    timestamp: Date
  }>

  // Validar assinatura
  validarAssinatura(
    dados: string,
    assinatura: string,
    certificado: string
  ): Promise<boolean>

  // Gerar relatório assinado
  gerarRelatorioAssinado(
    tipoRelatorio: string,
    periodo: { inicio: Date; fim: Date },
    funcionarios: string[]
  ): Promise<{
    arquivo: Buffer
    hash: string
    assinatura: string
  }>
}
```

#### **2. BACKUP OBRIGATÓRIO:**

```typescript
interface BackupObrigatorio {
  // Configuração do backup
  configuracao: {
    frequencia: 'diario' | 'semanal'
    retencao: number // em anos
    criptografia: 'AES-256'
    destinos: ['local', 'nuvem', 'fita']
    verificacaoIntegridade: boolean
  }

  // Executar backup
  executarBackup(): Promise<{
    id: string
    dataHora: Date
    tamanho: number
    hash: string
    destinos: string[]
    status: 'sucesso' | 'erro'
  }>

  // Verificar integridade
  verificarIntegridade(backupId: string): Promise<boolean>

  // Restaurar backup
  restaurarBackup(backupId: string, destino: string): Promise<void>
}
```

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD eSocial** (`/integracoes/esocial`)

```typescript
interface DashboardESocialScreen {
  status_conexao: {
    ambiente: 'producao' | 'homologacao'
    status: 'conectado' | 'desconectado' | 'erro'
    ultima_sincronizacao: Date
    certificado_valido_ate: Date
  }

  fila_eventos: {
    pendentes: number
    processando: number
    enviados_hoje: number
    erros_pendentes: number

    eventos_recentes: {
      tipo: string
      funcionario: string
      status: 'pendente' | 'enviado' | 'erro'
      data_criacao: Date
      data_envio?: Date
      protocolo?: string
      erro?: string
    }[]
  }

  alertas: {
    certificado_vencendo: boolean
    eventos_com_erro: number
    fila_congestionada: boolean
    conexao_instavel: boolean
  }
}
```

### **2. GESTÃO FGTS DIGITAL** (`/integracoes/fgts`)

```typescript
interface GestaoFGTSScreen {
  resumo_geral: {
    trabalhadores_ativos: number
    depositos_pendentes: number
    valor_total_depositos: number
    ultima_transmissao: Date
  }

  lista_trabalhadores: {
    cpf: string
    nome: string
    situacao_fgts: 'ativo' | 'inativo'
    ultimo_deposito: Date
    valor_deposito: number
    status_transmissao: 'ok' | 'pendente' | 'erro'
  }[]

  transmissoes: {
    data: Date
    tipo: 'admissao' | 'desligamento' | 'deposito'
    quantidade: number
    status: 'sucesso' | 'erro' | 'processando'
    protocolo?: string
  }[]
}
```

---

## 🔧 CONFIGURAÇÕES

### **1. CERTIFICADOS DIGITAIS:**

```typescript
interface CertificadoConfig {
  esocial: {
    arquivo_pfx: string
    senha: string
    valido_ate: Date
    emissor: string
    ambiente: 'producao' | 'homologacao'
  }

  fgts: {
    arquivo_pfx: string
    senha: string
    valido_ate: Date
    conectividade_social: {
      usuario: string
      senha: string
    }
  }

  portaria671: {
    arquivo_pfx: string
    senha: string
    algoritmo: 'RSA-2048' | 'RSA-4096'
    timestamping_url: string
  }
}
```

### **2. ENDPOINTS GOVERNAMENTAIS:**

```typescript
interface EndpointsGoverno {
  esocial: {
    producao: 'https://webservices.producaorestrita.esocial.gov.br/servicos/empregador/'
    homologacao: 'https://webservices.homologacao.esocial.gov.br/servicos/empregador/'
    consulta: 'https://webservices.producaorestrita.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc'
  }

  fgts: {
    conectividade: 'https://conectividade.caixa.gov.br/'
    consulta: 'https://webservice.caixa.gov.br/fgts/'
  }

  receita: {
    cpf: 'https://servicos.receita.fazenda.gov.br/Servicos/CPF/'
    cnpj: 'https://servicos.receita.fazenda.gov.br/Servicos/cnpjreva/'
  }
}
```

---

## 🔒 SEGURANÇA E COMPLIANCE

### **1. CRIPTOGRAFIA:**

- Certificados A1/A3 para assinatura
- TLS 1.3 para comunicação
- AES-256 para dados em repouso
- RSA-4096 para assinaturas críticas

### **2. AUDITORIA:**

- Log completo de todas as transmissões
- Rastreabilidade de alterações
- Backup de comprovantes
- Relatórios de conformidade

### **3. CONTINGÊNCIA:**

- Fila offline para falhas de conexão
- Retry automático com backoff
- Alertas para administradores
- Procedimentos de recuperação

---

## 📊 MONITORAMENTO

### **1. MÉTRICAS:**

- Taxa de sucesso por integração
- Tempo médio de resposta
- Volume de eventos por dia
- Erros por tipo

### **2. ALERTAS:**

- Certificados vencendo (30 dias)
- Falhas consecutivas (> 3)
- Fila congestionada (> 100 eventos)
- Conexão instável

### **3. RELATÓRIOS:**

- Conformidade mensal
- Eventos enviados por período
- Análise de erros
- Performance das integrações
