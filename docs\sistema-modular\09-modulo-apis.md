# 🔌 MÓDULO DE APIs

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Fornecer APIs RESTful completas para integração
- Gerenciar chaves de API e autenticação
- Controlar rate limiting e throttling
- Monitorar uso e performance das APIs
- Documentar endpoints automaticamente
- Implementar webhooks para eventos

### **📊 RESPONSABILIDADES:**

- CRUD completo via API para todos os módulos
- Sistema de autenticação JWT para APIs
- Rate limiting inteligente
- Documentação automática (OpenAPI/Swagger)
- Monitoramento e analytics
- Webhooks configuráveis
- Versionamento de APIs

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. GESTÃO DE CHAVES API:**

- Geração de chaves com escopo específico
- Controle de permissões por endpoint
- Expiração automática configurável
- Rotação de chaves

#### **2. AUTENTICAÇÃO E AUTORIZAÇÃO:**

- JWT tokens para APIs
- Scopes granulares de permissão
- IP whitelisting
- Rate limiting por chave

#### **3. DOCUMENTAÇÃO AUTOMÁTICA:**

- OpenAPI 3.0 specification
- Interface Swagger UI
- Exemplos de código
- Playground interativo

#### **4. MONITORAMENTO:**

- Métricas de uso em tempo real
- Logs detalhados de requests
- Analytics de performance
- Alertas de anomalias

#### **5. WEBHOOKS:**

- Eventos configuráveis
- Retry automático
- Assinatura de segurança
- Logs de entrega

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE APIs** (`/apis`)

```typescript
interface DashboardAPIsScreen {
  metricas_gerais: {
    requests_hoje: number
    requests_mes: number
    chaves_ativas: number
    endpoints_disponiveis: number
    tempo_resposta_medio: number // ms
    taxa_sucesso_24h: number // %

    graficos_tempo_real: {
      requests_por_minuto: {
        dados: { timestamp: Date; quantidade: number }[]
        atualizacao: 'tempo real'
      }

      tempo_resposta: {
        dados: { timestamp: Date; tempo_ms: number }[]
        meta_tempo: 500 // ms
      }

      status_codes: {
        dados: { codigo: number; quantidade: number }[]
        cores: { [codigo: number]: string }
      }
    }
  }

  chaves_api_ativas: {
    lista_resumida: {
      nome: string
      chave_parcial: string // últimos 8 caracteres
      requests_hoje: number
      ultimo_uso: Date
      status: 'ativa' | 'suspensa' | 'expirada'
      rate_limit_atual: string
    }[]

    acoes_rapidas: {
      criar_nova_chave: '/apis/chaves/nova'
      gerenciar_todas: '/apis/chaves'
      documentacao: '/apis/docs'
      playground: '/apis/playground'
    }
  }

  endpoints_populares: {
    ranking: {
      endpoint: string
      metodo: 'GET' | 'POST' | 'PUT' | 'DELETE'
      requests_hoje: number
      tempo_medio_ms: number
      taxa_sucesso: number
      documentacao_url: string
    }[]
  }

  alertas_api: {
    criticos: {
      tipo: 'rate_limit_excedido' | 'tempo_resposta_alto' | 'erro_rate_alto'
      endpoint: string
      detalhes: string
      timestamp: Date
    }[]

    avisos: {
      tipo: 'uso_alto' | 'chave_expirando' | 'webhook_falhando'
      detalhes: string
      timestamp: Date
    }[]
  }

  webhooks_status: {
    total_configurados: number
    ativos: number
    com_erro: number
    ultima_entrega: Date
    taxa_sucesso_webhooks: number
  }
}
```

### **2. GESTÃO DE CHAVES API** (`/apis/chaves`)

```typescript
interface GestaoChavesAPIScreen {
  lista_chaves: {
    filtros: {
      status: 'todas' | 'ativas' | 'suspensas' | 'expiradas'
      aplicacao: string
      criada_por: UUID
      data_criacao: DateRange
    }

    colunas: [
      'nome',
      'aplicacao',
      'chave_parcial',
      'permissoes',
      'rate_limit',
      'ultimo_uso',
      'requests_total',
      'status',
      'expira_em',
      'acoes',
    ]

    acoes_linha: {
      visualizar_detalhes: 'Modal com estatísticas'
      editar_permissoes: 'Modal de edição'
      regenerar_chave: 'Confirmação + nova chave'
      suspender_ativar: 'Toggle status'
      excluir: 'Confirmação + exclusão'
      logs_uso: '/apis/chaves/[id]/logs'
    }
  }

  criar_chave: {
    informacoes_basicas: {
      nome: {
        tipo: 'text'
        obrigatorio: true
        placeholder: 'Nome identificador da chave'
      }
      aplicacao: {
        tipo: 'text'
        placeholder: 'Nome da aplicação que usará'
      }
      descricao: {
        tipo: 'textarea'
        placeholder: 'Descrição do uso pretendido'
      }
    }

    configuracao_acesso: {
      permissoes_modulos: {
        funcionarios: {
          ler: boolean
          criar: boolean
          editar: boolean
          excluir: boolean
        }
        empresas: {
          ler: boolean
          criar: boolean
          editar: boolean
          excluir: boolean
        }
        registro_ponto: {
          ler: boolean
          criar: boolean
          editar: boolean
        }
        relatorios: {
          gerar: boolean
          listar: boolean
        }
        biometria: {
          ler: boolean
          gerenciar_templates: boolean
          gerenciar_dispositivos: boolean
        }
      }

      restricoes_acesso: {
        ips_permitidos: {
          lista: string[]
          placeholder: '***********/24 ou IP específico'
        }
        horario_acesso: {
          ativo: boolean
          inicio: Time
          fim: Time
          dias_semana: number[]
        }
        empresas_permitidas: {
          todas: boolean
          especificas: UUID[]
        }
      }
    }

    rate_limiting: {
      requests_por_minuto: {
        valor: number
        min: 1
        max: 1000
        padrao: 60
      }
      requests_por_hora: {
        valor: number
        min: 10
        max: 10000
        padrao: 1000
      }
      requests_por_dia: {
        valor: number
        min: 100
        max: 100000
        padrao: 10000
      }
      burst_permitido: {
        valor: number
        descricao: 'Picos temporários permitidos'
        padrao: 10
      }
    }

    configuracao_expiracao: {
      tipo_expiracao: 'nunca' | 'data_especifica' | 'periodo'
      data_expiracao: Date
      periodo_dias: number
      renovacao_automatica: boolean
      notificar_expiracao: {
        ativo: boolean
        dias_antecedencia: number[]
        emails: string[]
      }
    }
  }

  chave_gerada: {
    chave_completa: {
      valor: string
      formato: 'rlpt_live_xxxxxxxxxxxxxxxxxxxx'
      aviso_seguranca: 'Esta é a única vez que a chave será exibida'
      opcoes_copia: {
        copiar_clipboard: boolean
        download_arquivo: boolean
        enviar_email: boolean
      }
    }

    informacoes_uso: {
      url_base_api: string
      header_autenticacao: 'Authorization: Bearer {chave}'
      exemplo_curl: string
      exemplo_javascript: string
      exemplo_python: string
      link_documentacao: string
    }

    proximos_passos: {
      testar_chave: '/apis/playground'
      ver_documentacao: '/apis/docs'
      configurar_webhooks: '/apis/webhooks'
      monitorar_uso: '/apis/chaves/[id]/analytics'
    }
  }
}
```

### **3. DOCUMENTAÇÃO DE APIs** (`/apis/docs`)

```typescript
interface DocumentacaoAPIsScreen {
  navegacao_apis: {
    versoes_disponiveis: {
      versao: 'v1' | 'v2'
      status: 'atual' | 'deprecated' | 'beta'
      data_lancamento: Date
      changelog_url: string
    }[]

    categorias_endpoints: {
      autenticacao: {
        icone: 'key'
        endpoints_count: number
        descricao: 'Login, logout, tokens'
      }
      funcionarios: {
        icone: 'users'
        endpoints_count: number
        descricao: 'CRUD de funcionários'
      }
      empresas: {
        icone: 'building'
        endpoints_count: number
        descricao: 'Gestão de empresas'
      }
      registro_ponto: {
        icone: 'clock'
        endpoints_count: number
        descricao: 'Registros de ponto'
      }
      relatorios: {
        icone: 'chart-bar'
        endpoints_count: number
        descricao: 'Geração de relatórios'
      }
      biometria: {
        icone: 'fingerprint'
        endpoints_count: number
        descricao: 'Sistema biométrico'
      }
      webhooks: {
        icone: 'webhook'
        endpoints_count: number
        descricao: 'Configuração de webhooks'
      }
    }
  }

  endpoint_detalhado: {
    informacoes_gerais: {
      metodo: 'GET' | 'POST' | 'PUT' | 'DELETE'
      url: string
      descricao: string
      tags: string[]
      autenticacao_requerida: boolean
      rate_limit: string
      versao_introducao: string
    }

    parametros: {
      path_parameters: {
        nome: string
        tipo: string
        obrigatorio: boolean
        descricao: string
        exemplo: any
      }[]

      query_parameters: {
        nome: string
        tipo: string
        obrigatorio: boolean
        descricao: string
        valores_permitidos?: any[]
        exemplo: any
      }[]

      body_schema: {
        tipo: 'object' | 'array'
        propriedades: {
          [nome: string]: {
            tipo: string
            obrigatorio: boolean
            descricao: string
            exemplo: any
          }
        }
      }
    }

    respostas: {
      [status_code: number]: {
        descricao: string
        schema: object
        exemplo: object
        headers?: { [nome: string]: string }
      }
    }

    exemplos_codigo: {
      curl: string
      javascript: string
      python: string
      php: string
      java: string
    }

    playground_interativo: {
      parametros_editaveis: boolean
      executar_request: boolean
      visualizar_resposta: boolean
      salvar_exemplo: boolean
    }
  }

  guias_integracao: {
    primeiros_passos: {
      obter_chave_api: string
      fazer_primeiro_request: string
      tratar_erros: string
      rate_limiting: string
    }

    casos_uso_comuns: {
      sincronizar_funcionarios: string
      registrar_ponto_automatico: string
      gerar_relatorios_programados: string
      integrar_biometria: string
    }

    sdks_bibliotecas: {
      javascript: {
        nome: 'rlponto-js'
        versao: string
        instalacao: string
        exemplo_uso: string
      }
      python: {
        nome: 'rlponto-python'
        versao: string
        instalacao: string
        exemplo_uso: string
      }
    }
  }
}
```

### **4. MONITORAMENTO E ANALYTICS** (`/apis/analytics`)

```typescript
interface MonitoramentoAnalyticsScreen {
  metricas_tempo_real: {
    requests_ativas: number
    tempo_resposta_atual: number
    taxa_erro_ultima_hora: number
    chaves_ativas_agora: number

    graficos_live: {
      requests_por_segundo: {
        dados: { timestamp: Date; quantidade: number }[]
        atualizacao: '1 segundo'
      }

      distribuicao_tempo_resposta: {
        dados: { faixa: string; quantidade: number }[]
        faixas: ['<100ms', '100-500ms', '500ms-1s', '1s-5s', '>5s']
      }
    }
  }

  analytics_historico: {
    filtros_periodo: {
      periodo: 'hoje' | 'ontem' | '7_dias' | '30_dias' | 'personalizado'
      data_inicio: Date
      data_fim: Date
      granularidade: 'hora' | 'dia' | 'semana'
    }

    metricas_principais: {
      total_requests: number
      requests_sucesso: number
      requests_erro: number
      tempo_resposta_medio: number
      tempo_resposta_p95: number
      chaves_unicas_ativas: number
      endpoints_mais_usados: string[]
    }

    graficos_detalhados: {
      volume_requests: {
        tipo: 'linha'
        dados: { timestamp: Date; requests: number; erros: number }[]
      }

      performance_endpoints: {
        tipo: 'barra'
        dados: { endpoint: string; tempo_medio: number; requests: number }[]
      }

      uso_por_chave: {
        tipo: 'pizza'
        dados: { chave_nome: string; requests: number; percentual: number }[]
      }

      distribuicao_status_codes: {
        tipo: 'donut'
        dados: { status: number; quantidade: number; cor: string }[]
      }
    }
  }

  alertas_configuracao: {
    limites_alerta: {
      tempo_resposta_alto: {
        ativo: boolean
        limite_ms: number
        periodo_analise_minutos: number
        notificar: string[]
      }

      taxa_erro_alta: {
        ativo: boolean
        limite_percentual: number
        periodo_analise_minutos: number
        notificar: string[]
      }

      volume_anormal: {
        ativo: boolean
        desvio_percentual: number
        baseline_dias: number
        notificar: string[]
      }

      chave_suspeita: {
        ativo: boolean
        requests_por_minuto: number
        acao_automatica: 'alertar' | 'suspender' | 'rate_limit'
      }
    }

    webhooks_monitoramento: {
      falha_entrega: {
        ativo: boolean
        tentativas_falhadas: number
        notificar: string[]
      }

      tempo_resposta_webhook: {
        ativo: boolean
        limite_segundos: number
        notificar: string[]
      }
    }
  }

  logs_detalhados: {
    filtros_avancados: {
      chave_api: string
      endpoint: string
      metodo: string[]
      status_code: number[]
      ip_origem: string
      user_agent: string
      tempo_resposta_min: number
      tempo_resposta_max: number
    }

    visualizacao_logs: {
      timestamp: Date
      chave_api_parcial: string
      metodo: string
      endpoint: string
      status_code: number
      tempo_resposta_ms: number
      ip_origem: string
      user_agent: string
      request_id: string
      erro_detalhes?: string
    }[]

    acoes_logs: {
      exportar_csv: boolean
      filtrar_tempo_real: boolean
      agrupar_por_endpoint: boolean
      mostrar_request_body: boolean
      mostrar_response_body: boolean
    }
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS PRINCIPAIS:**

```typescript
interface APIsModule {
  // ✅ GESTÃO DE CHAVES API
  'GET /api/api-keys': {
    query: {
      status?: 'ativa' | 'suspensa' | 'expirada'
      aplicacao?: string
    }
    response: ChaveAPI[]
  }

  'POST /api/api-keys': {
    body: {
      nome: string
      aplicacao: string
      permissoes: PermissaoAPI[]
      rate_limits: RateLimitConfig
      restricoes: RestricaoAcesso
      expiracao?: ExpiracaoConfig
    }
    response: {
      chave_id: UUID
      chave_api: string
      configuracoes: ChaveAPIConfig
    }
  }

  'PUT /api/api-keys/[id]': {
    body: Partial<ChaveAPIConfig>
    response: ChaveAPI
  }

  'DELETE /api/api-keys/[id]': {
    response: { message: 'Chave removida' }
  }

  // ✅ ANALYTICS E MONITORAMENTO
  'GET /api/api-analytics': {
    query: {
      periodo: 'hoje' | '7_dias' | '30_dias'
      chave_id?: UUID
      endpoint?: string
      granularidade?: 'hora' | 'dia'
    }
    response: {
      metricas: MetricasAPI
      graficos: GraficosAnalytics
      top_endpoints: EndpointStats[]
    }
  }

  'GET /api/api-logs': {
    query: {
      chave_id?: UUID
      endpoint?: string
      status_code?: number
      desde?: Date
      limite?: number
    }
    response: LogAPI[]
  }

  // ✅ DOCUMENTAÇÃO
  'GET /api/openapi.json': {
    response: OpenAPISpecification
  }

  'GET /api/docs': {
    response: 'Swagger UI HTML'
  }

  // ✅ WEBHOOKS
  'GET /api/webhooks': {
    response: WebhookConfig[]
  }

  'POST /api/webhooks': {
    body: {
      nome: string
      url_destino: string
      eventos: string[]
      ativo: boolean
      secret_key?: string
      headers_customizados?: { [key: string]: string }
    }
    response: WebhookConfig
  }

  'POST /api/webhooks/[id]/test': {
    response: {
      sucesso: boolean
      tempo_resposta: number
      status_code: number
      erro?: string
    }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ CHAVES API
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Identificação
  nome VARCHAR(255) NOT NULL,
  aplicacao VARCHAR(255),
  descricao TEXT,

  -- Chave
  chave_hash VARCHAR(64) NOT NULL UNIQUE, -- SHA-256 da chave
  chave_parcial VARCHAR(20) NOT NULL, -- últimos caracteres para exibição

  -- Permissões
  permissoes JSONB NOT NULL DEFAULT '{}',
  restricoes_ip INET[],
  horario_acesso_inicio TIME,
  horario_acesso_fim TIME,
  dias_semana_permitidos INTEGER[],
  empresas_permitidas UUID[],

  -- Rate Limiting
  requests_por_minuto INTEGER DEFAULT 60,
  requests_por_hora INTEGER DEFAULT 1000,
  requests_por_dia INTEGER DEFAULT 10000,
  burst_permitido INTEGER DEFAULT 10,

  -- Expiração
  data_expiracao TIMESTAMP,
  renovacao_automatica BOOLEAN DEFAULT FALSE,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,
  suspenso BOOLEAN DEFAULT FALSE,
  motivo_suspensao TEXT,

  -- Uso
  primeiro_uso TIMESTAMP,
  ultimo_uso TIMESTAMP,
  total_requests BIGINT DEFAULT 0,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ✅ LOGS DE API
CREATE TABLE api_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Request
  chave_api_id UUID REFERENCES api_keys(id),
  request_id VARCHAR(50) NOT NULL,
  timestamp_request TIMESTAMP DEFAULT NOW(),

  -- Endpoint
  metodo VARCHAR(10) NOT NULL,
  endpoint VARCHAR(500) NOT NULL,
  versao_api VARCHAR(10) DEFAULT 'v1',

  -- Cliente
  ip_origem INET,
  user_agent TEXT,

  -- Request Data
  query_params JSONB,
  request_body_size INTEGER,
  request_body_hash VARCHAR(64), -- para requests grandes

  -- Response
  status_code INTEGER NOT NULL,
  tempo_resposta_ms INTEGER NOT NULL,
  response_body_size INTEGER,

  -- Erro
  erro_codigo VARCHAR(50),
  erro_mensagem TEXT,

  -- Rate Limiting
  rate_limit_aplicado BOOLEAN DEFAULT FALSE,
  rate_limit_restante INTEGER,

  CONSTRAINT chk_metodo CHECK (
    metodo IN ('GET', 'POST', 'PUT', 'DELETE', 'PATCH')
  )
);

-- ✅ WEBHOOKS
CREATE TABLE webhooks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Configuração
  nome VARCHAR(255) NOT NULL,
  url_destino VARCHAR(500) NOT NULL,
  secret_key VARCHAR(255),

  -- Eventos
  eventos_suscritos JSONB NOT NULL, -- array de eventos
  filtros JSONB DEFAULT '{}',

  -- Headers
  headers_customizados JSONB DEFAULT '{}',

  -- Configuração de Entrega
  timeout_segundos INTEGER DEFAULT 30,
  retry_tentativas INTEGER DEFAULT 3,
  retry_delay_segundos INTEGER DEFAULT 60,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Estatísticas
  total_entregas INTEGER DEFAULT 0,
  entregas_sucesso INTEGER DEFAULT 0,
  entregas_erro INTEGER DEFAULT 0,
  ultima_entrega TIMESTAMP,
  ultimo_erro TEXT,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ ENTREGAS DE WEBHOOK
CREATE TABLE webhook_entregas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webhook_id UUID NOT NULL REFERENCES webhooks(id),

  -- Evento
  evento_tipo VARCHAR(100) NOT NULL,
  evento_data JSONB NOT NULL,

  -- Entrega
  tentativa INTEGER NOT NULL DEFAULT 1,
  timestamp_entrega TIMESTAMP DEFAULT NOW(),

  -- Resultado
  sucesso BOOLEAN NOT NULL,
  status_code INTEGER,
  tempo_resposta_ms INTEGER,
  erro_detalhes TEXT,

  -- Request/Response
  request_headers JSONB,
  request_body TEXT,
  response_headers JSONB,
  response_body TEXT
);

-- ✅ RATE LIMITING
CREATE TABLE rate_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chave_api_id UUID NOT NULL REFERENCES api_keys(id),

  -- Janela de Tempo
  janela_inicio TIMESTAMP NOT NULL,
  janela_tipo VARCHAR(20) NOT NULL, -- 'minuto', 'hora', 'dia'

  -- Contadores
  requests_realizados INTEGER DEFAULT 0,
  limite_janela INTEGER NOT NULL,

  -- Status
  limite_excedido BOOLEAN DEFAULT FALSE,
  primeiro_excesso TIMESTAMP,

  CONSTRAINT chk_janela_tipo CHECK (
    janela_tipo IN ('minuto', 'hora', 'dia')
  ),

  UNIQUE(chave_api_id, janela_inicio, janela_tipo)
);

-- ✅ ÍNDICES
CREATE INDEX idx_api_keys_ativo ON api_keys(ativo);
CREATE INDEX idx_api_keys_chave_hash ON api_keys(chave_hash);
CREATE INDEX idx_api_logs_chave_timestamp ON api_logs(chave_api_id, timestamp_request);
CREATE INDEX idx_api_logs_endpoint ON api_logs(endpoint);
CREATE INDEX idx_api_logs_status_code ON api_logs(status_code);
CREATE INDEX idx_webhooks_ativo ON webhooks(ativo);
CREATE INDEX idx_webhook_entregas_webhook_timestamp ON webhook_entregas(webhook_id, timestamp_entrega);
CREATE INDEX idx_rate_limits_chave_janela ON rate_limits(chave_api_id, janela_inicio);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🔑 GESTÃO DE CHAVES:**

1. **Chaves únicas** por aplicação
2. **Hash seguro** (SHA-256) para armazenamento
3. **Expiração automática** configurável
4. **Rotação** recomendada a cada 90 dias

### **🚦 RATE LIMITING:**

1. **Limites hierárquicos**: minuto < hora < dia
2. **Burst permitido** para picos temporários
3. **Throttling gradual** antes do bloqueio
4. **Reset automático** das janelas

### **📊 MONITORAMENTO:**

1. **Logs detalhados** de todas as requests
2. **Retenção** de 90 dias para logs
3. **Alertas automáticos** para anomalias
4. **Métricas em tempo real**

### **🔗 WEBHOOKS:**

1. **Retry automático** com backoff exponencial
2. **Assinatura HMAC** para segurança
3. **Timeout configurável** por webhook
4. **Logs de entrega** para auditoria

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE AUTENTICAÇÃO:**

```typescript
describe('Autenticação API', () => {
  test('Aceitar chave válida', async () => {
    const chave = await criarChaveAPI({
      nome: 'Teste',
      permissoes: ['funcionarios:read'],
    })

    const response = await request
      .get('/api/funcionarios')
      .set('Authorization', `Bearer ${chave.chave_api}`)
      .expect(200)
  })

  test('Rejeitar chave inválida', async () => {
    await request
      .get('/api/funcionarios')
      .set('Authorization', 'Bearer chave_invalida')
      .expect(401)
  })
})
```

### **✅ TESTES DE RATE LIMITING:**

```typescript
describe('Rate Limiting', () => {
  test('Aplicar limite por minuto', async () => {
    const chave = await criarChaveAPI({
      requests_por_minuto: 2,
    })

    // Primeira request - OK
    await request
      .get('/api/funcionarios')
      .set('Authorization', `Bearer ${chave.chave_api}`)
      .expect(200)

    // Segunda request - OK
    await request
      .get('/api/funcionarios')
      .set('Authorization', `Bearer ${chave.chave_api}`)
      .expect(200)

    // Terceira request - Rate limited
    await request
      .get('/api/funcionarios')
      .set('Authorization', `Bearer ${chave.chave_api}`)
      .expect(429)
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Autenticação**

- Sistema de chaves API
- JWT para APIs
- Middleware de autenticação

**Semana 3-4: Rate Limiting**

- Implementação de limites
- Throttling inteligente
- Monitoramento de uso

**Semana 5-6: Documentação**

- OpenAPI specification
- Swagger UI
- Exemplos de código

**Semana 7-8: Webhooks e Analytics**

- Sistema de webhooks
- Dashboard de analytics
- Alertas automáticos

### **🔧 DEPENDÊNCIAS:**

- Sistema de autenticação JWT
- Redis (rate limiting)
- OpenAPI/Swagger
- Sistema de logs
- Monitoramento (Prometheus)
- Notificações (webhooks)
