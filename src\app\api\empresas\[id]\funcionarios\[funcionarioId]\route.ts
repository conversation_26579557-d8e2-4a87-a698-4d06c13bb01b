import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para atualização de alocação
const updateAlocacaoSchema = z.object({
  dataFimPrevista: z.string().datetime().optional(),
  valorHoraProjeto: z.number().positive().optional(),
  observacoes: z.string().optional()
})

// Schema para finalização de alocação
const finalizarAlocacaoSchema = z.object({
  dataFim: z.string().datetime("Data de fim inválida"),
  motivo: z.string().min(1, "Motivo é obrigatório")
})

// PUT /api/empresas/[id]/funcionarios/[funcionarioId] - Atualizar alocação
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; funcionarioId: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Buscar alocação ativa
    const alocacao = await prisma.funcionarioAlocacao.findFirst({
      where: {
        empresaId: params.id,
        funcionarioId: params.funcionarioId,
        status: 'ativo'
      },
      include: {
        funcionario: {
          select: {
            id: true,
            nomeCompleto: true
          }
        },
        empresa: {
          select: {
            id: true,
            nomeFantasia: true
          }
        }
      }
    })

    if (!alocacao) {
      return NextResponse.json(
        { error: "Alocação não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = updateAlocacaoSchema.parse(body)

    // Preparar dados para atualização
    const updateData: any = { ...data }

    if (data.dataFimPrevista) {
      updateData.dataFimPrevista = new Date(data.dataFimPrevista)
    }

    // Atualizar alocação
    const alocacaoAtualizada = await prisma.funcionarioAlocacao.update({
      where: { id: alocacao.id },
      data: updateData,
      include: {
        funcionario: {
          select: {
            id: true,
            nomeCompleto: true,
            cargo: true
          }
        },
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "UPDATE_ALOCACAO",
        tabelaAfetada: "funcionario_alocacoes",
        registroId: alocacao.id,
        dadosAnteriores: alocacao,
        dadosNovos: alocacaoAtualizada,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json(alocacaoAtualizada)

  } catch (error) {
    console.error('Erro ao atualizar alocação:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// DELETE /api/empresas/[id]/funcionarios/[funcionarioId] - Finalizar alocação
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; funcionarioId: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Buscar alocação ativa
    const alocacao = await prisma.funcionarioAlocacao.findFirst({
      where: {
        empresaId: params.id,
        funcionarioId: params.funcionarioId,
        status: 'ativo'
      }
    })

    if (!alocacao) {
      return NextResponse.json(
        { error: "Alocação ativa não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const data = finalizarAlocacaoSchema.parse(body)

    // Finalizar alocação
    const alocacaoFinalizada = await prisma.funcionarioAlocacao.update({
      where: { id: alocacao.id },
      data: {
        status: 'finalizado',
        dataFimReal: new Date(data.dataFim),
        motivoFinalizacao: data.motivo
      },
      include: {
        funcionario: {
          select: {
            id: true,
            nomeCompleto: true,
            cargo: true
          }
        },
        empresa: {
          select: {
            id: true,
            nomeFantasia: true,
            nomeProjeto: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "FINALIZAR_ALOCACAO",
        tabelaAfetada: "funcionario_alocacoes",
        registroId: alocacao.id,
        dadosAnteriores: alocacao,
        dadosNovos: alocacaoFinalizada,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      message: "Alocação finalizada com sucesso",
      alocacao: alocacaoFinalizada
    })

  } catch (error) {
    console.error('Erro ao finalizar alocação:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
