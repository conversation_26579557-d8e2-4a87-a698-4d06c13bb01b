"use client"

import { useState, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Camera, Fingerprint, Upload, X, Eye, RefreshCw } from "lucide-react"

interface BiometriaStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function BiometriaStep({ data, onUpdate }: BiometriaStepProps) {
  const [formData, setFormData] = useState({
    foto: data.foto || null,
    templateBiometrico: data.templateBiometrico || null,
    qualidadeTemplate: data.qualidadeTemplate || 0,
    observacoes: data.observacoes || ''
  })

  const [capturing, setCapturing] = useState(false)
  const [scanningFingerprint, setScanningFingerprint] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const handleChange = (field: string, value: any) => {
    const newFormData = { ...formData, [field]: value }
    setFormData(newFormData)
    onUpdate(newFormData)
  }

  // Capturar foto da webcam
  const startCamera = async () => {
    try {
      setCapturing(true)
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: 640, 
          height: 480,
          facingMode: 'user'
        } 
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
    } catch (error) {
      console.error('Erro ao acessar câmera:', error)
      alert('Erro ao acessar a câmera. Verifique as permissões.')
      setCapturing(false)
    }
  }

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current
      const video = videoRef.current
      const context = canvas.getContext('2d')
      
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      
      if (context) {
        context.drawImage(video, 0, 0)
        
        canvas.toBlob((blob) => {
          if (blob) {
            const photoData = {
              blob,
              url: URL.createObjectURL(blob),
              timestamp: new Date().toISOString()
            }
            handleChange('foto', photoData)
          }
        }, 'image/jpeg', 0.8)
      }
      
      stopCamera()
    }
  }

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
    }
    setCapturing(false)
  }

  // Upload de foto
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validar tipo de arquivo
    if (!file.type.startsWith('image/')) {
      alert('Apenas arquivos de imagem são permitidos')
      return
    }

    // Validar tamanho (2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 2MB')
      return
    }

    const photoData = {
      blob: file,
      url: URL.createObjectURL(file),
      timestamp: new Date().toISOString()
    }

    handleChange('foto', photoData)
  }

  // Simular captura de biometria
  const startFingerprintScan = async () => {
    setScanningFingerprint(true)
    
    // Simular processo de captura
    setTimeout(() => {
      const templateData = {
        template: `TEMPLATE_${Date.now()}`, // Em produção, seria o template real
        qualidade: Math.floor(Math.random() * 40) + 60, // Qualidade entre 60-100
        timestamp: new Date().toISOString()
      }
      
      handleChange('templateBiometrico', templateData.template)
      handleChange('qualidadeTemplate', templateData.qualidade)
      setScanningFingerprint(false)
    }, 3000)
  }

  const removePhoto = () => {
    if (formData.foto?.url) {
      URL.revokeObjectURL(formData.foto.url)
    }
    handleChange('foto', null)
  }

  const removeBiometric = () => {
    handleChange('templateBiometrico', null)
    handleChange('qualidadeTemplate', 0)
  }

  const getQualityColor = (quality: number) => {
    if (quality >= 80) return 'text-green-600'
    if (quality >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getQualityLabel = (quality: number) => {
    if (quality >= 80) return 'Excelente'
    if (quality >= 60) return 'Boa'
    return 'Ruim'
  }

  return (
    <div className="space-y-6">
      {/* Foto do Funcionário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Foto do Funcionário
          </CardTitle>
          <CardDescription>
            Foto para identificação no sistema
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!formData.foto ? (
            <div className="space-y-4">
              {!capturing ? (
                <div className="flex gap-4">
                  <Button onClick={startCamera} className="flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Capturar Foto
                  </Button>
                  
                  <div>
                    <Label htmlFor="photoUpload" className="cursor-pointer">
                      <Button variant="outline" className="flex items-center gap-2" asChild>
                        <span>
                          <Upload className="h-4 w-4" />
                          Upload Foto
                        </span>
                      </Button>
                    </Label>
                    <Input
                      id="photoUpload"
                      type="file"
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="hidden"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <video
                    ref={videoRef}
                    className="w-full max-w-md mx-auto rounded-lg border"
                    autoPlay
                    muted
                  />
                  <div className="flex gap-2 justify-center">
                    <Button onClick={capturePhoto}>
                      Capturar
                    </Button>
                    <Button variant="outline" onClick={stopCamera}>
                      Cancelar
                    </Button>
                  </div>
                </div>
              )}
              
              <canvas ref={canvasRef} className="hidden" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <img
                  src={formData.foto.url}
                  alt="Foto do funcionário"
                  className="w-32 h-32 object-cover rounded-lg border"
                />
                <div className="flex-1">
                  <p className="text-sm text-gray-600">
                    Foto capturada em: {new Date(formData.foto.timestamp).toLocaleString('pt-BR')}
                  </p>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(formData.foto.url, '_blank')}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Visualizar
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={removePhoto}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Remover
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Biometria */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Fingerprint className="h-5 w-5" />
            Biometria
          </CardTitle>
          <CardDescription>
            Impressão digital para controle de acesso
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!formData.templateBiometrico ? (
            <div className="text-center space-y-4">
              <div className="w-32 h-32 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                <Fingerprint className="h-16 w-16 text-gray-400" />
              </div>
              
              <div>
                <Button 
                  onClick={startFingerprintScan}
                  disabled={scanningFingerprint}
                  className="flex items-center gap-2"
                >
                  {scanningFingerprint ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Capturando...
                    </>
                  ) : (
                    <>
                      <Fingerprint className="h-4 w-4" />
                      Capturar Biometria
                    </>
                  )}
                </Button>
                
                {scanningFingerprint && (
                  <p className="text-sm text-gray-600 mt-2">
                    Posicione o dedo no leitor biométrico
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <Fingerprint className="h-8 w-8 text-green-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Biometria capturada com sucesso</p>
                  <p className="text-sm text-gray-600">
                    Template: {formData.templateBiometrico.slice(0, 20)}...
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm">Qualidade:</span>
                    <span className={`text-sm font-medium ${getQualityColor(formData.qualidadeTemplate)}`}>
                      {formData.qualidadeTemplate}% - {getQualityLabel(formData.qualidadeTemplate)}
                    </span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={removeBiometric}
                >
                  <X className="h-4 w-4 mr-1" />
                  Remover
                </Button>
              </div>
              
              {formData.qualidadeTemplate < 60 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    ⚠️ Qualidade baixa. Recomenda-se capturar novamente para melhor precisão.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Observações */}
      <Card>
        <CardHeader>
          <CardTitle>Observações</CardTitle>
          <CardDescription>
            Informações adicionais sobre a biometria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="observacoes">Observações</Label>
            <textarea
              id="observacoes"
              value={formData.observacoes}
              onChange={(e) => handleChange('observacoes', e.target.value)}
              placeholder="Ex: Dificuldade na captura, dedo machucado, etc."
              className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </CardContent>
      </Card>

      {/* Resumo */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo da Biometria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {formData.foto && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Camera className="h-3 w-3" />
                Foto capturada
              </Badge>
            )}
            {formData.templateBiometrico && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Fingerprint className="h-3 w-3" />
                Biometria capturada
              </Badge>
            )}
            {formData.qualidadeTemplate > 0 && (
              <Badge 
                variant={formData.qualidadeTemplate >= 60 ? "default" : "secondary"}
                className={formData.qualidadeTemplate >= 60 ? "" : "bg-yellow-100 text-yellow-800"}
              >
                Qualidade: {formData.qualidadeTemplate}%
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dicas */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Dicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• A foto deve ser nítida e mostrar claramente o rosto</li>
            <li>• Para melhor qualidade biométrica, mantenha o dedo limpo e seco</li>
            <li>• Qualidade acima de 80% garante melhor precisão no reconhecimento</li>
            <li>• Em caso de dificuldade, tente capturar outro dedo</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
