# 🚀 Deploy RLP-V2

## Configuração SSH Realizada

✅ **Chave SSH criada**: `C:\Users\<USER>\.ssh\RLP-V2`
✅ **Servidor configurado**: `***********` (alias: `rlp-server`)
✅ **Acesso sem senha**: Configurado
✅ **Node.js instalado**: v20.19.4
✅ **PM2 instalado**: Para gerenciamento de processos

## Scripts de Deploy

### 1. Deploy Completo
```powershell
.\deploy-simple.ps1
```

### 2. Monitoramento
```powershell
.\monitor.ps1
```

## Comandos Úteis

### SSH
```bash
# Conectar ao servidor
ssh rlp-server

# Verificar status da aplicação
ssh rlp-server "pm2 status"

# Ver logs
ssh rlp-server "pm2 logs rlp-v2"
```

### PM2 no Servidor
```bash
# Status
pm2 status

# Restart
pm2 restart rlp-v2

# Stop
pm2 stop rlp-v2

# Logs em tempo real
pm2 logs rlp-v2 --follow

# Monitoramento
pm2 monit
```

## URLs

- **Aplicação**: http://***********:3000
- **Servidor SSH**: root@***********
- **Diretório Deploy**: `/var/www/rlp-v2`

## Estrutura no Servidor

```
/var/www/rlp-v2/
├── .next/              # Build do Next.js
├── public/             # Arquivos estáticos
├── package.json        # Dependências
├── package-lock.json   # Lock das dependências
├── next.config.ts      # Configuração Next.js
├── ecosystem.config.js # Configuração PM2
└── node_modules/       # Dependências instaladas
```

## Processo de Deploy

1. **Build local** da aplicação Next.js
2. **Empacotamento** dos arquivos necessários
3. **Upload** para o servidor via SCP
4. **Extração** e instalação de dependências
5. **Configuração PM2** para gerenciamento
6. **Restart** da aplicação

## Troubleshooting

### Aplicação não inicia
```bash
ssh rlp-server "cd /var/www/rlp-v2 && npm run build"
ssh rlp-server "pm2 restart rlp-v2"
```

### Verificar logs de erro
```bash
ssh rlp-server "pm2 logs rlp-v2 --err"
```

### Reinstalar dependências
```bash
ssh rlp-server "cd /var/www/rlp-v2 && rm -rf node_modules && npm ci"
```

## Status Atual

✅ **Deploy realizado com sucesso**
✅ **Aplicação rodando** em http://***********:3000
✅ **PM2 configurado** e funcionando
✅ **Scripts de deploy** prontos para uso
