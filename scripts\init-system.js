const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function initializeSystem() {
  try {
    console.log('🚀 Inicializando sistema RLPONTO...')
    
    // Testar conexão
    await prisma.$connect()
    console.log('✅ Conexão com banco estabelecida')
    
    // Verificar se já existe usuário admin
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!adminUser) {
      console.log('👤 Criando usuário administrador...')
      const passwordHash = await bcrypt.hash('admin123', 12)
      
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          nome: 'Administrador',
          passwordHash,
          nivelAcesso: 'admin_total',
          ativo: true,
          emailVerified: new Date()
        }
      })
      console.log('✅ Usuário admin criado: <EMAIL> / admin123')
    } else {
      console.log('✅ Usuário admin já existe')
    }
    
    // Verificar se já existe empresa principal
    let empresaPrincipal = await prisma.empresa.findFirst({
      where: { tipoEmpresa: 'principal' }
    })
    
    if (!empresaPrincipal) {
      console.log('🏢 Criando empresa principal...')
      empresaPrincipal = await prisma.empresa.create({
        data: {
          tipoEmpresa: 'principal',
          nomeFantasia: 'RLPONTO Sistemas',
          razaoSocial: 'RLPONTO Sistemas de Controle de Ponto LTDA',
          cnpj: '12.345.678/0001-90',
          endereco: {
            logradouro: 'Rua Principal, 123',
            cidade: 'São Paulo',
            estado: 'SP',
            cep: '01234-567'
          },
          contatos: {
            telefone: '(11) 99999-9999',
            email: '<EMAIL>'
          },
          configuracoes: {
            jornada_padrao: {
              horas_semanais: 40,
              dias_semana: [1, 2, 3, 4, 5],
              horario_entrada: '08:00',
              horario_saida: '17:00',
              intervalo_almoco: 60
            },
            tolerancias: {
              entrada_minutos: 10,
              saida_minutos: 10,
              intervalo_minutos: 5
            },
            parametros_ponto: {
              permitir_registro_manual: true,
              exigir_justificativa_atraso: true,
              bloquear_registro_fora_jornada: false
            }
          },
          createdBy: adminUser.id
        }
      })
      console.log('✅ Empresa principal criada')
    } else {
      console.log('✅ Empresa principal já existe')
    }
    
    // Criar usuário de teste se não existir
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!testUser) {
      console.log('👤 Criando usuário de teste...')
      const passwordHash = await bcrypt.hash('teste123', 12)
      
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          nome: 'Usuário Teste',
          passwordHash,
          nivelAcesso: 'gerente_rh',
          ativo: true,
          emailVerified: new Date()
        }
      })
      console.log('✅ Usuário teste criado: <EMAIL> / teste123')
    } else {
      console.log('✅ Usuário teste já existe')
    }
    
    // Estatísticas finais
    const stats = {
      usuarios: await prisma.user.count(),
      empresas: await prisma.empresa.count(),
      funcionarios: await prisma.funcionario.count()
    }
    
    console.log('\n📊 Sistema inicializado com sucesso!')
    console.log(`👥 Usuários: ${stats.usuarios}`)
    console.log(`🏢 Empresas: ${stats.empresas}`)
    console.log(`👷 Funcionários: ${stats.funcionarios}`)
    
    console.log('\n🔑 Credenciais de acesso:')
    console.log('📧 Admin: <EMAIL> / admin123')
    console.log('📧 Teste: <EMAIL> / teste123')
    
  } catch (error) {
    console.error('❌ Erro na inicialização:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

initializeSystem()
