"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, ArrowRight, Briefcase, FileText, Fingerprint, MapPin, Phone, Save, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"

// Componentes das etapas
import BiometriaStep from "@/components/funcionarios/steps/BiometriaStep"
import ContatosStep from "@/components/funcionarios/steps/ContatosStep"
import DadosPessoaisStep from "@/components/funcionarios/steps/DadosPessoaisStep"
import DadosProfissionaisStep from "@/components/funcionarios/steps/DadosProfissionaisStep"
import DocumentosStep from "@/components/funcionarios/steps/DocumentosStep"
import EnderecoStep from "@/components/funcionarios/steps/EnderecoStep"

const STEPS = [
  {
    id: 1,
    title: "Dados Pessoais",
    description: "Informações básicas do funcionário",
    icon: User,
    component: DadosPessoaisStep
  },
  {
    id: 2,
    title: "Endereço",
    description: "Endereço residencial",
    icon: MapPin,
    component: EnderecoStep
  },
  {
    id: 3,
    title: "Contatos",
    description: "Telefones e e-mails",
    icon: Phone,
    component: ContatosStep
  },
  {
    id: 4,
    title: "Documentos",
    description: "RG, CPF, PIS e outros",
    icon: FileText,
    component: DocumentosStep
  },
  {
    id: 5,
    title: "Dados Profissionais",
    description: "Cargo, salário e benefícios",
    icon: Briefcase,
    component: DadosProfissionaisStep
  },
  {
    id: 6,
    title: "Biometria",
    description: "Foto e impressão digital",
    icon: Fingerprint,
    component: BiometriaStep
  }
]

export default function NovoFuncionarioPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({})
  const [isLoading, setIsLoading] = useState(false)

  const currentStepData = STEPS.find(step => step.id === currentStep)
  const progress = (currentStep / STEPS.length) * 100

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepId: number) => {
    setCurrentStep(stepId)
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Implementar salvamento
      console.log("Salvando funcionário:", formData)
      // Redirecionar após salvar
      router.push("/funcionarios")
    } catch (error) {
      console.error("Erro ao salvar funcionário:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateFormData = (stepData: any) => {
    setFormData(prev => ({ ...prev, ...stepData }))
  }

  const CurrentStepComponent = currentStepData?.component

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Button
                variant="ghost"
                onClick={() => router.push("/funcionarios")}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
              <h1 className="text-3xl font-bold text-gray-900">Novo Funcionário</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleSave}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Salvando..." : "Salvar"}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Etapa {currentStep} de {STEPS.length}</span>
              <span>{Math.round(progress)}% concluído</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
          
          {/* Steps Navigation */}
          <div className="flex space-x-4 overflow-x-auto">
            {STEPS.map((step) => {
              const Icon = step.icon
              const isActive = step.id === currentStep
              const isCompleted = step.id < currentStep
              
              return (
                <button
                  key={step.id}
                  onClick={() => handleStepClick(step.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    isActive
                      ? "bg-blue-100 text-blue-700 border-2 border-blue-300"
                      : isCompleted
                      ? "bg-green-100 text-green-700 border-2 border-green-300"
                      : "bg-gray-100 text-gray-600 border-2 border-gray-200 hover:bg-gray-200"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="font-medium">{step.title}</span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {currentStepData && <currentStepData.icon className="h-5 w-5" />}
              {currentStepData?.title}
            </CardTitle>
            <CardDescription>
              {currentStepData?.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {CurrentStepComponent && (
              <CurrentStepComponent
                data={formData}
                onUpdate={updateFormData}
              />
            )}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-6">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Anterior
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={currentStep === STEPS.length}
          >
            Próximo
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </main>
    </div>
  )
}
