# 📊 MÓDULO DE RELATÓRIOS

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerar relatórios completos de ponto e RH
- Implementar sistema de impressão universal
- Fornecer dashboards analíticos
- Automatizar relatórios periódicos
- Garantir conformidade legal

### **📊 RESPONSABILIDADES:**

- Relatórios de ponto individuais e coletivos
- Dashboards executivos
- Relatórios de conformidade
- Exportação múltiplos formatos
- Agendamento automático
- Templates personalizáveis

---

## 🚀 FUNCIONALIDADES

### **✅ CATEGORIAS DE RELATÓRIOS:**

#### **1. RELATÓRIOS DE PONTO:**

- Espelho de ponto individual
- Relatório coletivo por empresa
- Controle de frequência
- Horas extras e adicionais
- Irregularidades e justificativas

#### **2. RELATÓRIOS GERENCIAIS:**

- Dashboard executivo
- Indicadores de produtividade
- Análise de custos
- Relatórios de EPIs
- Conformidade trabalhista

#### **3. RELATÓRIOS LEGAIS:**

- Livro de registro de empregados
- Controle de jornada (Art. 74 CLT)
- Relatórios para fiscalização
- Documentos para auditoria

#### **4. SISTEMA DE IMPRESSÃO:**

- Templates minimalistas
- Economia de papel
- Impressão em lote
- QR codes para validação

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE RELATÓRIOS** (`/relatorios`)

```typescript
interface DashboardRelatoriosScreen {
  categorias_relatorios: {
    ponto_frequencia: {
      icone: 'clock'
      cor: '#4ecdc4'
      relatorios: [
        'Espelho de Ponto Individual',
        'Relatório Coletivo de Ponto',
        'Controle de Frequência',
        'Horas Extras Detalhado',
        'Irregularidades por Período',
      ]
    }

    gerenciais: {
      icone: 'chart-bar'
      cor: '#45b7d1'
      relatorios: [
        'Dashboard Executivo',
        'Indicadores de Produtividade',
        'Análise de Custos por Projeto',
        'Relatório de EPIs',
        'Turnover e Absenteísmo',
      ]
    }

    legais_conformidade: {
      icone: 'scale'
      cor: '#96ceb4'
      relatorios: [
        'Livro de Registro de Empregados',
        'Controle de Jornada (Art. 74)',
        'Relatório para Fiscalização',
        'Documentos para Auditoria',
        'Conformidade eSocial',
      ]
    }

    personalizados: {
      icone: 'settings'
      cor: '#feca57'
      relatorios: [
        'Relatórios Salvos',
        'Templates Personalizados',
        'Relatórios Agendados',
        'Favoritos do Usuário',
      ]
    }
  }

  relatorios_rapidos: {
    hoje: {
      funcionarios_presentes: number
      atrasos: number
      horas_extras: number
      irregularidades: number
    }

    mes_atual: {
      total_horas_trabalhadas: number
      custo_folha_estimado: number
      produtividade_media: number
      conformidade_percentual: number
    }

    acoes_rapidas: [
      'Espelho Hoje',
      'Presença Atual',
      'Irregularidades Pendentes',
      'Relatório Mensal',
    ]
  }

  agendamentos_ativos: {
    lista: {
      nome_relatorio: string
      frequencia: 'diario' | 'semanal' | 'mensal'
      proximo_envio: Date
      destinatarios: string[]
      status: 'ativo' | 'pausado'
    }[]

    acoes: {
      criar_agendamento: '/relatorios/agendar'
      gerenciar_existentes: '/relatorios/agendamentos'
    }
  }
}
```

### **2. GERADOR DE RELATÓRIOS** (`/relatorios/gerar`)

```typescript
interface GeradorRelatoriosScreen {
  selecao_relatorio: {
    tipo_relatorio: {
      opcoes: [
        'espelho_ponto_individual',
        'relatorio_coletivo',
        'controle_frequencia',
        'horas_extras',
        'irregularidades',
        'dashboard_executivo',
        'analise_custos',
        'relatorio_epis',
        'conformidade_legal',
      ]
      descricoes: {
        [key: string]: {
          titulo: string
          descricao: string
          tempo_estimado: string
          formato_padrao: string[]
        }
      }
    }
  }

  filtros_parametros: {
    periodo: {
      tipo_periodo:
        | 'hoje'
        | 'ontem'
        | 'semana_atual'
        | 'mes_atual'
        | 'personalizado'
      data_inicio: Date
      data_fim: Date
      max_periodo: '12 meses'
    }

    funcionarios: {
      selecao: 'todos' | 'por_empresa' | 'por_cargo' | 'individuais'
      empresas_selecionadas: UUID[]
      cargos_selecionados: string[]
      funcionarios_individuais: UUID[]
      incluir_inativos: boolean
    }

    configuracoes_especificas: {
      incluir_fotos: boolean
      incluir_justificativas: boolean
      incluir_calculos_detalhados: boolean
      incluir_graficos: boolean
      incluir_assinatura_digital: boolean
      agrupar_por: 'funcionario' | 'empresa' | 'cargo' | 'turno'
    }
  }

  template_formatacao: {
    formato_saida: {
      opcoes: ['PDF', 'Excel', 'CSV', 'HTML']
      configuracoes_pdf: {
        orientacao: 'retrato' | 'paisagem'
        tamanho_papel: 'A4' | 'A3' | 'Carta'
        margem: 'normal' | 'estreita' | 'ampla'
        incluir_cabecalho: boolean
        incluir_rodape: boolean
      }
    }

    template_visual: {
      estilo: 'profissional' | 'minimalista' | 'colorido' | 'personalizado'
      logo_empresa: boolean
      cores_personalizadas: {
        primaria: string
        secundaria: string
        texto: string
      }
      fonte: 'Arial' | 'Times' | 'Calibri' | 'Roboto'
      tamanho_fonte: number
    }

    economia_papel: {
      ativo: boolean
      densidade_informacao: 'alta' | 'media' | 'baixa'
      remover_espacos_extras: boolean
      comprimir_tabelas: boolean
      usar_abreviacoes: boolean
    }
  }

  preview_configuracao: {
    estimativa: {
      numero_paginas: number
      tempo_geracao: string
      tamanho_arquivo: string
      custo_impressao_estimado: number
    }

    preview_visual: {
      primeira_pagina: 'Renderização da primeira página'
      estrutura_relatorio: string[]
    }
  }

  opcoes_entrega: {
    acao_apos_geracao: {
      visualizar_online: boolean
      download_automatico: boolean
      enviar_email: boolean
      imprimir_automaticamente: boolean
    }

    email_configuracao: {
      destinatarios: string[]
      assunto_personalizado: string
      mensagem_corpo: string
      anexar_arquivo: boolean
      link_visualizacao: boolean
    }

    impressao_configuracao: {
      impressora_padrao: string
      numero_copias: number
      imprimir_frente_verso: boolean
      qualidade: 'rascunho' | 'normal' | 'alta'
    }
  }
}
```

### **3. DASHBOARD EXECUTIVO** (`/relatorios/dashboard`)

```typescript
interface DashboardExecutivoScreen {
  metricas_principais: {
    periodo_selecionado: {
      tipo: 'hoje' | 'semana' | 'mes' | 'trimestre' | 'ano'
      comparacao_anterior: boolean
    }

    kpis_principais: {
      total_funcionarios: {
        valor: number
        variacao_percentual: number
        tendencia: 'crescimento' | 'estavel' | 'reducao'
      }

      horas_trabalhadas: {
        valor: number
        meta: number
        percentual_meta: number
      }

      produtividade_media: {
        valor: number // %
        benchmark_setor: number
        posicao_ranking: string
      }

      custo_total_folha: {
        valor: number
        orcamento: number
        variacao_orcamento: number
      }

      conformidade_legal: {
        percentual: number
        irregularidades_pendentes: number
        prazo_medio_resolucao: number // dias
      }
    }
  }

  graficos_analises: {
    presenca_diaria: {
      tipo: 'linha'
      dados: {
        data: Date
        presentes: number
        ausentes: number
        atrasos: number
      }[]
      periodo: 'ultimos_30_dias'
    }

    distribuicao_horas: {
      tipo: 'pizza'
      dados: {
        horas_normais: number
        horas_extras: number
        horas_noturnas: number
        horas_feriado: number
      }
    }

    custos_por_projeto: {
      tipo: 'barra'
      dados: {
        projeto: string
        custo_planejado: number
        custo_real: number
        variacao: number
      }[]
    }

    irregularidades_tendencia: {
      tipo: 'area'
      dados: {
        mes: string
        atrasos: number
        saidas_antecipadas: number
        faltas: number
        outros: number
      }[]
    }
  }

  alertas_criticos: {
    alta_prioridade: {
      tipo: 'error'
      alertas: [
        'Funcionários com mais de 5 irregularidades no mês',
        'Projetos com estouro de orçamento > 20%',
        'EPIs vencidos em uso',
        'Documentos trabalhistas pendentes',
      ]
    }

    media_prioridade: {
      tipo: 'warning'
      alertas: [
        'Aumento de 15% nos atrasos',
        'Redução na produtividade média',
        'Funcionários próximos ao limite de horas extras',
        'Relatórios legais com prazo próximo',
      ]
    }

    informativo: {
      tipo: 'info'
      alertas: [
        'Novos funcionários admitidos',
        'Atualizações de configuração pendentes',
        'Backup automático realizado',
        'Relatórios agendados executados',
      ]
    }
  }

  acoes_rapidas: {
    relatorios_frequentes: [
      'Espelho de Ponto Hoje',
      'Irregularidades Pendentes',
      'Relatório Mensal Completo',
      'Análise de Custos',
    ]

    configuracoes: [
      'Agendar Novo Relatório',
      'Configurar Alertas',
      'Personalizar Dashboard',
      'Exportar Dados',
    ]
  }
}
```

### **4. SISTEMA DE IMPRESSÃO** (`/relatorios/impressao`)

```typescript
interface SistemaImpressaoScreen {
  templates_impressao: {
    minimalista_economia: {
      nome: 'Template Econômico'
      descricao: 'Máxima economia de papel'
      caracteristicas: [
        'Fonte 8pt condensada',
        'Margens mínimas (5mm)',
        'Sem logos ou imagens',
        'Tabelas compactas',
        'Informações essenciais apenas',
      ]
      economia_papel: 'até 60%'
    }

    profissional_padrao: {
      nome: 'Template Profissional'
      descricao: 'Equilibrio entre economia e apresentação'
      caracteristicas: [
        'Fonte 10pt padrão',
        'Margens normais (15mm)',
        'Logo da empresa',
        'Formatação clara',
        'Informações completas',
      ]
      economia_papel: 'até 30%'
    }

    completo_detalhado: {
      nome: 'Template Completo'
      descricao: 'Máximo detalhamento'
      caracteristicas: [
        'Fonte 12pt legível',
        'Margens amplas (20mm)',
        'Gráficos e imagens',
        'Formatação rica',
        'Todas as informações',
      ]
      economia_papel: 'padrão'
    }
  }

  configuracao_impressao: {
    configuracoes_globais: {
      template_padrao: string
      impressora_padrao: string
      qualidade_padrao: 'rascunho' | 'normal' | 'alta'
      frente_verso_automatico: boolean
      economia_papel_ativa: boolean
    }

    configuracoes_por_relatorio: {
      [tipo_relatorio: string]: {
        template_especifico: string
        configuracoes_especiais: object
        aprovacao_necessaria: boolean
        limite_paginas: number
      }
    }

    controle_custos: {
      custo_por_pagina: number
      limite_mensal_usuario: number
      limite_mensal_empresa: number
      alertar_quando_atingir: number // %
    }
  }

  fila_impressao: {
    trabalhos_pendentes: {
      id: UUID
      usuario: string
      relatorio: string
      paginas: number
      prioridade: 'baixa' | 'normal' | 'alta'
      status: 'aguardando' | 'processando' | 'imprimindo' | 'concluido' | 'erro'
      tempo_estimado: string
    }[]

    acoes_fila: {
      pausar_fila: boolean
      limpar_fila: boolean
      reordenar_prioridades: boolean
      cancelar_trabalho: (id: UUID) => void
    }
  }

  historico_impressoes: {
    filtros: {
      periodo: DateRange
      usuario: string
      tipo_relatorio: string
      status: string
    }

    estatisticas: {
      total_impressoes: number
      total_paginas: number
      custo_total: number
      economia_papel_percentual: number
    }

    detalhes: {
      data_impressao: Date
      usuario: string
      relatorio: string
      paginas: number
      custo: number
      template_usado: string
      economia_aplicada: boolean
    }[]
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface RelatoriosAPI {
  // ✅ GERAÇÃO DE RELATÓRIOS
  'POST /api/relatorios/gerar': {
    body: {
      tipo_relatorio: string
      parametros: {
        periodo: DateRange
        funcionarios?: UUID[]
        empresas?: UUID[]
        configuracoes?: object
      }
      formato: 'PDF' | 'Excel' | 'CSV' | 'HTML'
      template?: string
    }
    response: {
      relatorio_id: UUID
      url_download: string
      tempo_estimado: number
      status: 'processando' | 'concluido' | 'erro'
    }
  }

  'GET /api/relatorios/[id]/status': {
    response: {
      status: 'processando' | 'concluido' | 'erro'
      progresso_percentual: number
      url_download?: string
      erro_detalhes?: string
    }
  }

  // ✅ DASHBOARD
  'GET /api/relatorios/dashboard': {
    query: {
      periodo: 'hoje' | 'semana' | 'mes' | 'trimestre'
      empresas?: UUID[]
    }
    response: {
      metricas: MetricasDashboard
      graficos: GraficosDashboard
      alertas: AlertasDashboard
    }
  }

  // ✅ AGENDAMENTOS
  'POST /api/relatorios/agendar': {
    body: {
      nome: string
      tipo_relatorio: string
      parametros: object
      frequencia: 'diario' | 'semanal' | 'mensal'
      destinatarios: string[]
      ativo: boolean
    }
    response: AgendamentoRelatorio
  }

  'GET /api/relatorios/agendamentos': {
    response: AgendamentoRelatorio[]
  }

  // ✅ IMPRESSÃO
  'POST /api/relatorios/imprimir': {
    body: {
      relatorio_id: UUID
      impressora: string
      configuracoes: ConfiguracaoImpressao
    }
    response: {
      trabalho_impressao_id: UUID
      posicao_fila: number
      tempo_estimado: string
    }
  }

  'GET /api/relatorios/impressao/fila': {
    response: {
      trabalhos: TrabalhoImpressao[]
      estatisticas_fila: EstatisticasFila
    }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ RELATÓRIOS GERADOS
CREATE TABLE relatorios_gerados (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Identificação
  nome VARCHAR(255) NOT NULL,
  tipo_relatorio VARCHAR(100) NOT NULL,
  descricao TEXT,

  -- Parâmetros
  parametros JSONB NOT NULL,
  periodo_inicio DATE,
  periodo_fim DATE,

  -- Arquivo
  formato VARCHAR(10) NOT NULL, -- PDF, Excel, CSV, HTML
  tamanho_arquivo BIGINT,
  url_arquivo VARCHAR(500),
  hash_arquivo VARCHAR(64),

  -- Processamento
  status VARCHAR(20) DEFAULT 'processando',
  progresso_percentual INTEGER DEFAULT 0,
  tempo_processamento INTEGER, -- segundos
  erro_detalhes TEXT,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),
  expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '30 days')
);

-- ✅ AGENDAMENTOS
CREATE TABLE relatorios_agendados (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Configuração
  nome VARCHAR(255) NOT NULL,
  tipo_relatorio VARCHAR(100) NOT NULL,
  parametros JSONB NOT NULL,

  -- Agendamento
  frequencia VARCHAR(20) NOT NULL, -- diario, semanal, mensal
  dia_semana INTEGER, -- 1-7 para semanal
  dia_mes INTEGER, -- 1-31 para mensal
  horario TIME DEFAULT '08:00',

  -- Destinatários
  destinatarios JSONB NOT NULL,
  formato_padrao VARCHAR(10) DEFAULT 'PDF',

  -- Controle
  ativo BOOLEAN DEFAULT TRUE,
  ultima_execucao TIMESTAMP,
  proxima_execucao TIMESTAMP,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ HISTÓRICO DE IMPRESSÕES
CREATE TABLE impressoes_historico (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  relatorio_id UUID REFERENCES relatorios_gerados(id),

  -- Configuração
  impressora VARCHAR(100),
  template_usado VARCHAR(100),
  configuracoes JSONB,

  -- Resultado
  paginas_impressas INTEGER,
  custo_estimado DECIMAL(10,2),
  economia_papel_percentual DECIMAL(5,2),

  -- Status
  status VARCHAR(20) DEFAULT 'aguardando',
  inicio_impressao TIMESTAMP,
  fim_impressao TIMESTAMP,
  erro_impressao TEXT,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ ÍNDICES
CREATE INDEX idx_relatorios_tipo ON relatorios_gerados(tipo_relatorio);
CREATE INDEX idx_relatorios_status ON relatorios_gerados(status);
CREATE INDEX idx_relatorios_created_by ON relatorios_gerados(created_by);
CREATE INDEX idx_agendados_proxima_execucao ON relatorios_agendados(proxima_execucao);
CREATE INDEX idx_impressoes_status ON impressoes_historico(status);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **📊 GERAÇÃO DE RELATÓRIOS:**

1. **Limite de período**: máximo 12 meses
2. **Timeout**: 10 minutos para relatórios grandes
3. **Retenção**: arquivos mantidos por 30 dias
4. **Permissões**: baseadas no nível de acesso

### **🖨️ SISTEMA DE IMPRESSÃO:**

1. **Economia obrigatória**: template econômico por padrão
2. **Limite de páginas**: configurável por usuário
3. **Aprovação**: relatórios > 50 páginas precisam aprovação
4. **Custo**: controle de gastos por departamento

### **📅 AGENDAMENTOS:**

1. **Máximo 10 agendamentos** por usuário
2. **Execução automática** em horários de baixo uso
3. **Falha**: 3 tentativas antes de desativar
4. **Notificação**: email em caso de erro

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE GERAÇÃO:**

```typescript
describe('Geração de Relatórios', () => {
  test('Gerar espelho de ponto individual', async () => {
    const relatorio = await gerarRelatorio({
      tipo: 'espelho_ponto_individual',
      funcionario_id: 'uuid',
      periodo: { inicio: '2024-01-01', fim: '2024-01-31' },
    })

    expect(relatorio.status).toBe('concluido')
    expect(relatorio.url_arquivo).toBeDefined()
  })

  test('Respeitar limite de período', async () => {
    await expect(
      gerarRelatorio({
        periodo: { inicio: '2023-01-01', fim: '2024-12-31' }, // > 12 meses
      })
    ).rejects.toThrow('Período máximo excedido')
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**

- Modelo de dados
- APIs de geração
- Templates básicos

**Semana 3-4: Dashboard**

- Métricas e KPIs
- Gráficos interativos
- Alertas automáticos

**Semana 5-6: Sistema de Impressão**

- Templates econômicos
- Fila de impressão
- Controle de custos

**Semana 7-8: Agendamentos**

- Sistema de agendamento
- Execução automática
- Notificações

### **🔧 DEPENDÊNCIAS:**

- Módulo de Registro de Ponto
- Módulo de Funcionários
- Módulo de Empresas
- Sistema de arquivos (S3)
- Biblioteca de gráficos (Chart.js)
- Sistema de impressão (CUPS)
