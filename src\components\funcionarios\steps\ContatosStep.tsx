"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Phone, Mail, UserPlus } from "lucide-react"

interface ContatosStepProps {
  data: any
  onUpdate: (data: any) => void
}

export default function ContatosStep({ data, onUpdate }: ContatosStepProps) {
  const [formData, setFormData] = useState({
    telefonePrincipal: data.telefonePrincipal || '',
    telefoneSecundario: data.telefoneSecundario || '',
    email: data.email || '',
    contatoEmergencia: {
      nome: data.contatoEmergencia?.nome || '',
      parentesco: data.contatoEmergencia?.parentesco || '',
      telefone: data.contatoEmergencia?.telefone || '',
      email: data.contatoEmergencia?.email || ''
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Formatar telefone
  const formatarTelefone = (value: string) => {
    const telefone = value.replace(/\D/g, '')
    
    if (telefone.length <= 10) {
      // Telefone fixo: (11) 1234-5678
      return telefone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3')
    } else {
      // Celular: (11) 91234-5678
      return telefone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3')
    }
  }

  // Validar email
  const validarEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  }

  // Validar telefone
  const validarTelefone = (telefone: string) => {
    const telefoneLimpo = telefone.replace(/\D/g, '')
    return telefoneLimpo.length >= 10 && telefoneLimpo.length <= 11
  }

  const handleChange = (field: string, value: any) => {
    let newFormData = { ...formData }

    if (field.startsWith('contatoEmergencia.')) {
      const subField = field.split('.')[1]
      newFormData.contatoEmergencia = {
        ...formData.contatoEmergencia,
        [subField]: value
      }
    } else {
      newFormData[field] = value
    }

    setFormData(newFormData)
    onUpdate(newFormData)

    // Validações
    if (field === 'email' && value) {
      if (!validarEmail(value)) {
        setErrors(prev => ({ ...prev, email: 'Email inválido' }))
      } else {
        setErrors(prev => ({ ...prev, email: '' }))
      }
    }

    if (field === 'telefonePrincipal' && value) {
      if (!validarTelefone(value)) {
        setErrors(prev => ({ ...prev, telefonePrincipal: 'Telefone inválido' }))
      } else {
        setErrors(prev => ({ ...prev, telefonePrincipal: '' }))
      }
    }

    if (field === 'telefoneSecundario' && value) {
      if (!validarTelefone(value)) {
        setErrors(prev => ({ ...prev, telefoneSecundario: 'Telefone inválido' }))
      } else {
        setErrors(prev => ({ ...prev, telefoneSecundario: '' }))
      }
    }

    if (field === 'contatoEmergencia.telefone' && value) {
      if (!validarTelefone(value)) {
        setErrors(prev => ({ ...prev, 'contatoEmergencia.telefone': 'Telefone inválido' }))
      } else {
        setErrors(prev => ({ ...prev, 'contatoEmergencia.telefone': '' }))
      }
    }

    if (field === 'contatoEmergencia.email' && value) {
      if (!validarEmail(value)) {
        setErrors(prev => ({ ...prev, 'contatoEmergencia.email': 'Email inválido' }))
      } else {
        setErrors(prev => ({ ...prev, 'contatoEmergencia.email': '' }))
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Contatos Pessoais */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Contatos Pessoais
          </CardTitle>
          <CardDescription>
            Telefones e email do funcionário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="telefonePrincipal">Telefone Principal</Label>
              <Input
                id="telefonePrincipal"
                value={formatarTelefone(formData.telefonePrincipal)}
                onChange={(e) => handleChange('telefonePrincipal', e.target.value)}
                placeholder="(11) 91234-5678"
                maxLength={15}
                className={errors.telefonePrincipal ? 'border-red-500' : ''}
              />
              {errors.telefonePrincipal && (
                <p className="text-sm text-red-500 mt-1">{errors.telefonePrincipal}</p>
              )}
            </div>

            <div>
              <Label htmlFor="telefoneSecundario">Telefone Secundário</Label>
              <Input
                id="telefoneSecundario"
                value={formatarTelefone(formData.telefoneSecundario)}
                onChange={(e) => handleChange('telefoneSecundario', e.target.value)}
                placeholder="(11) 1234-5678"
                maxLength={15}
                className={errors.telefoneSecundario ? 'border-red-500' : ''}
              />
              {errors.telefoneSecundario && (
                <p className="text-sm text-red-500 mt-1">{errors.telefoneSecundario}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email}</p>
            )}
            <p className="text-sm text-gray-600 mt-1">
              Email será usado para comunicações importantes
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Contato de Emergência */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Contato de Emergência
          </CardTitle>
          <CardDescription>
            Pessoa para contato em caso de emergência
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contatoEmergenciaNome">Nome Completo</Label>
              <Input
                id="contatoEmergenciaNome"
                value={formData.contatoEmergencia.nome}
                onChange={(e) => handleChange('contatoEmergencia.nome', e.target.value)}
                placeholder="Nome da pessoa de contato"
              />
            </div>

            <div>
              <Label htmlFor="contatoEmergenciaParentesco">Parentesco</Label>
              <Input
                id="contatoEmergenciaParentesco"
                value={formData.contatoEmergencia.parentesco}
                onChange={(e) => handleChange('contatoEmergencia.parentesco', e.target.value)}
                placeholder="Ex: Mãe, Pai, Cônjuge, Irmão(ã)"
              />
            </div>

            <div>
              <Label htmlFor="contatoEmergenciaTelefone">Telefone</Label>
              <Input
                id="contatoEmergenciaTelefone"
                value={formatarTelefone(formData.contatoEmergencia.telefone)}
                onChange={(e) => handleChange('contatoEmergencia.telefone', e.target.value)}
                placeholder="(11) 91234-5678"
                maxLength={15}
                className={errors['contatoEmergencia.telefone'] ? 'border-red-500' : ''}
              />
              {errors['contatoEmergencia.telefone'] && (
                <p className="text-sm text-red-500 mt-1">{errors['contatoEmergencia.telefone']}</p>
              )}
            </div>

            <div>
              <Label htmlFor="contatoEmergenciaEmail">Email (opcional)</Label>
              <Input
                id="contatoEmergenciaEmail"
                type="email"
                value={formData.contatoEmergencia.email}
                onChange={(e) => handleChange('contatoEmergencia.email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors['contatoEmergencia.email'] ? 'border-red-500' : ''}
              />
              {errors['contatoEmergencia.email'] && (
                <p className="text-sm text-red-500 mt-1">{errors['contatoEmergencia.email']}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resumo dos Contatos */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo dos Contatos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Contatos do Funcionário */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Funcionário</h4>
              <div className="flex flex-wrap gap-2">
                {formData.telefonePrincipal && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {formatarTelefone(formData.telefonePrincipal)}
                  </Badge>
                )}
                {formData.telefoneSecundario && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {formatarTelefone(formData.telefoneSecundario)}
                  </Badge>
                )}
                {formData.email && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    {formData.email}
                  </Badge>
                )}
              </div>
            </div>

            {/* Contato de Emergência */}
            {(formData.contatoEmergencia.nome || formData.contatoEmergencia.telefone) && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Contato de Emergência</h4>
                <div className="flex flex-wrap gap-2">
                  {formData.contatoEmergencia.nome && (
                    <Badge variant="secondary">
                      {formData.contatoEmergencia.nome}
                      {formData.contatoEmergencia.parentesco && 
                        ` (${formData.contatoEmergencia.parentesco})`
                      }
                    </Badge>
                  )}
                  {formData.contatoEmergencia.telefone && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {formatarTelefone(formData.contatoEmergencia.telefone)}
                    </Badge>
                  )}
                  {formData.contatoEmergencia.email && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      {formData.contatoEmergencia.email}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dicas */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Dicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Mantenha os contatos sempre atualizados</li>
            <li>• O contato de emergência deve ser alguém de confiança</li>
            <li>• Verifique se os números estão corretos</li>
            <li>• O email será usado para comunicações importantes</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
