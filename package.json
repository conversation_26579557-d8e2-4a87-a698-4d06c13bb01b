{"name": "02projeto", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "analyze": "cross-env ANALYZE=true next build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@tanstack/react-query": "^5.83.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "framer-motion": "^12.23.9", "lodash-es": "^4.17.21", "lucide-react": "^0.532.0", "next": "15.4.4", "next-auth": "^5.0.0-beta.29", "prettier": "^3.6.2", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}