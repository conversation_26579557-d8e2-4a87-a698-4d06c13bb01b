import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { auth } from "@/lib/auth-basic"
import { z } from "zod"

const prisma = new PrismaClient()

// Schema para configurações
const configuracoesSchema = z.object({
  jornada_padrao: z.object({
    horas_semanais: z.number().min(1).max(60).optional(),
    dias_semana: z.array(z.number().min(1).max(7)).optional(),
    horario_entrada: z.string().optional(),
    horario_saida: z.string().optional(),
    intervalo_almoco: z.number().min(0).max(240).optional() // em minutos
  }).optional(),
  tolerancias: z.object({
    entrada_minutos: z.number().min(0).max(60).optional(),
    saida_minutos: z.number().min(0).max(60).optional(),
    intervalo_minutos: z.number().min(0).max(30).optional()
  }).optional(),
  parametros_ponto: z.object({
    permitir_registro_manual: z.boolean().optional(),
    exigir_justificativa_atraso: z.boolean().optional(),
    bloquear_registro_fora_jornada: z.boolean().optional(),
    permitir_registro_offline: z.boolean().optional()
  }).optional(),
  notificacoes: z.object({
    email_atraso: z.boolean().optional(),
    email_ausencia: z.boolean().optional(),
    email_relatorio_diario: z.boolean().optional(),
    whatsapp_alertas: z.boolean().optional()
  }).optional(),
  relatorios: z.object({
    formato_padrao: z.enum(['pdf', 'excel', 'csv']).optional(),
    incluir_fotos: z.boolean().optional(),
    incluir_localizacao: z.boolean().optional(),
    envio_automatico: z.boolean().optional()
  }).optional()
})

// GET /api/empresas/[id]/configuracoes - Obter configurações da empresa
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Buscar empresa
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id },
      include: {
        empresaPrincipal: {
          select: {
            id: true,
            nomeFantasia: true,
            configuracoes: true,
            tolerancias: true
          }
        }
      }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    // Montar configurações com herança
    let configuracoes = {}
    let tolerancias = {}

    // Se for empresa cliente, herdar configurações da principal
    if (empresa.tipoEmpresa === 'cliente' && empresa.empresaPrincipal) {
      configuracoes = {
        ...empresa.empresaPrincipal.configuracoes,
        ...empresa.configuracoes
      }
      tolerancias = {
        ...empresa.empresaPrincipal.tolerancias,
        ...empresa.tolerancias
      }
    } else {
      configuracoes = empresa.configuracoes
      tolerancias = empresa.tolerancias
    }

    return NextResponse.json({
      empresa: {
        id: empresa.id,
        nomeFantasia: empresa.nomeFantasia,
        tipoEmpresa: empresa.tipoEmpresa
      },
      configuracoes,
      tolerancias,
      heranca: {
        herdaDaPrincipal: empresa.tipoEmpresa === 'cliente',
        empresaPrincipal: empresa.empresaPrincipal ? {
          id: empresa.empresaPrincipal.id,
          nomeFantasia: empresa.empresaPrincipal.nomeFantasia
        } : null
      }
    })

  } catch (error) {
    console.error('Erro ao buscar configurações:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// PUT /api/empresas/[id]/configuracoes - Atualizar configurações da empresa
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado', 'gerente_rh'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Verificar se empresa existe
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { configuracoes, tolerancias } = body

    // Validar configurações se fornecidas
    if (configuracoes) {
      configuracoesSchema.parse(configuracoes)
    }

    // Preparar dados para atualização
    const updateData: any = {}

    if (configuracoes) {
      // Mesclar com configurações existentes
      updateData.configuracoes = {
        ...empresa.configuracoes,
        ...configuracoes
      }
    }

    if (tolerancias) {
      // Mesclar com tolerâncias existentes
      updateData.tolerancias = {
        ...empresa.tolerancias,
        ...tolerancias
      }
    }

    // Atualizar empresa
    const empresaAtualizada = await prisma.empresa.update({
      where: { id: params.id },
      data: updateData,
      include: {
        empresaPrincipal: {
          select: {
            id: true,
            nomeFantasia: true
          }
        }
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "UPDATE_CONFIGURACOES_EMPRESA",
        tabelaAfetada: "empresas",
        registroId: params.id,
        dadosAnteriores: {
          configuracoes: empresa.configuracoes,
          tolerancias: empresa.tolerancias
        },
        dadosNovos: {
          configuracoes: empresaAtualizada.configuracoes,
          tolerancias: empresaAtualizada.tolerancias
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      message: "Configurações atualizadas com sucesso",
      empresa: empresaAtualizada
    })

  } catch (error) {
    console.error('Erro ao atualizar configurações:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Dados inválidos",
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// POST /api/empresas/[id]/configuracoes/herdar - Herdar configurações da empresa principal
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Verificar permissão
    if (!['admin_total', 'admin_limitado'].includes(session.user.nivelAcesso)) {
      return NextResponse.json(
        { error: "Permissão insuficiente" },
        { status: 403 }
      )
    }

    // Buscar empresa cliente
    const empresa = await prisma.empresa.findUnique({
      where: { id: params.id },
      include: {
        empresaPrincipal: true
      }
    })

    if (!empresa) {
      return NextResponse.json(
        { error: "Empresa não encontrada" },
        { status: 404 }
      )
    }

    if (empresa.tipoEmpresa !== 'cliente') {
      return NextResponse.json(
        { error: "Apenas empresas clientes podem herdar configurações" },
        { status: 400 }
      )
    }

    if (!empresa.empresaPrincipal) {
      return NextResponse.json(
        { error: "Empresa principal não encontrada" },
        { status: 400 }
      )
    }

    // Herdar configurações da empresa principal
    const empresaAtualizada = await prisma.empresa.update({
      where: { id: params.id },
      data: {
        configuracoes: empresa.empresaPrincipal.configuracoes,
        tolerancias: empresa.empresaPrincipal.tolerancias
      }
    })

    // Log de auditoria
    await prisma.logAuditoria.create({
      data: {
        usuarioId: session.user.id,
        acao: "HERDAR_CONFIGURACOES",
        tabelaAfetada: "empresas",
        registroId: params.id,
        dadosAnteriores: {
          configuracoes: empresa.configuracoes,
          tolerancias: empresa.tolerancias
        },
        dadosNovos: {
          configuracoes: empresaAtualizada.configuracoes,
          tolerancias: empresaAtualizada.tolerancias
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      message: "Configurações herdadas com sucesso",
      empresa: empresaAtualizada
    })

  } catch (error) {
    console.error('Erro ao herdar configurações:', error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
