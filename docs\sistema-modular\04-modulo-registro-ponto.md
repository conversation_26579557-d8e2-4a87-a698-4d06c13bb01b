# ⏰ MÓDULO DE REGISTRO DE PONTO

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**

- Gerenciar registros de ponto biométricos e manuais
- Implementar sistema de 6 tipos de batida (B1-B6)
- Aplicar princ<PERSON><PERSON> "NUNCA IMPEDIR FUNCIONÁRIO ATIVO"
- Controlar jornadas, turnos e horas extras
- Classificar automaticamente irregularidades

### **📊 RESPONSABILIDADES:**

- Registro de ponto em tempo real
- Validação de sequência de batidas
- Cálculo automático de horas
- Classificação de irregularidades
- Espelho de ponto individual
- Justificativas e correções

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. REGISTRO DE PONTO:**

- 6 tipos de batida: B1, B2, B3, B4, B5, B6
- Registro biométrico e manual
- Validação de localização (GPS)
- F<PERSON><PERSON> obrigatória<PERSON> (manual)
- <PERSON><PERSON><PERSON><PERSON><PERSON> "NUNCA IMPEDIR"

#### **2. CLASSIFICAÇÃO AUTOMÁTICA:**

- Análise de tolerâncias
- Detecção de irregularidades
- Classificação por tipo
- Alertas para RH
- Sugestões de ação

#### **3. ESPELHO DE PONTO:**

- Visualização individual
- Filtros por período
- Exportação PDF/Excel
- Assinatura digital
- Histórico completo

#### **4. GESTÃO DE TURNOS:**

- 4 turnos configuráveis
- Cruzamento de meia-noite
- Adicional noturno
- Horas extras automáticas

---

## 📱 TELAS E INTERFACES

### **1. REGISTRO DE PONTO** (`/ponto/registrar`)

```typescript
interface RegistroPontoScreen {
  identificacao_funcionario: {
    metodos: {
      biometria: {
        ativo: boolean
        dispositivos_disponiveis: DispositivoBiometrico[]
        tentativas_infinitas: true // Princípio NUNCA IMPEDIR
      }
      manual: {
        busca_funcionario: {
          por_matricula: string
          por_nome: string
          por_cpf: string
        }
        validacao_supervisor: {
          obrigatorio: true
          nivel_minimo: 'supervisor_obra'
        }
      }
      qr_code: {
        scanner: boolean
        geracao_qr: 'Por funcionário'
      }
    }
  }

  dados_registro: {
    funcionario_identificado: {
      nome: string
      matricula: string
      foto: string
      empresa_atual: string
      ultimo_registro: {
        tipo: TipoBatida
        horario: DateTime
        local: string
      }
    }

    proximo_registro: {
      tipo_sugerido: TipoBatida
      descricao: string
      horario_esperado: Time
      tolerancia: number // minutos
    }

    dados_contexto: {
      data_atual: Date
      horario_atual: Time
      localizacao: {
        latitude: number
        longitude: number
        precisao: number
        endereco: string
      }
      dispositivo: {
        ip: string
        user_agent: string
        tipo: 'biometrico' | 'manual' | 'mobile'
      }
    }
  }

  confirmacao_registro: {
    tipo_batida: {
      selecionado: TipoBatida
      opcoes_disponiveis: TipoBatida[]
      permitir_alteracao: boolean
    }

    validacoes: {
      sequencia_correta: boolean
      dentro_tolerancia: boolean
      localizacao_valida: boolean
      jornada_valida: boolean
    }

    alertas: {
      tipo: 'info' | 'warning' | 'error'
      mensagem: string
      requer_justificativa: boolean
    }[]

    foto_obrigatoria: {
      condicional: 'registro_manual'
      captura: 'camera' | 'upload'
      validacao: 'Face detection'
    }

    justificativa: {
      obrigatoria_se: [
        'fora_sequencia',
        'fora_tolerancia',
        'localizacao_invalida',
        'registro_duplicado',
      ]
      campo: 'textarea'
      minimo_caracteres: 20
    }
  }

  resultado_registro: {
    sucesso: {
      numero_registro: string
      horario_registrado: DateTime
      tipo_batida: TipoBatida
      classificacao: ClassificacaoRegistro
      proxima_batida_esperada: {
        tipo: TipoBatida
        horario_previsto: Time
      }
    }

    acoes_pos_registro: {
      imprimir_comprovante: boolean
      enviar_email: boolean
      notificar_supervisor: boolean
    }
  }
}
```

### **2. ESPELHO DE PONTO** (`/ponto/espelho`)

```typescript
interface EspelhoPontoScreen {
  filtros_periodo: {
    funcionario: {
      busca: string
      selecionado: Funcionario
      permissao_visualizacao: boolean
    }

    periodo: {
      tipo: 'mes_atual' | 'mes_anterior' | 'personalizado'
      data_inicio: Date
      data_fim: Date
      max_dias: 31
    }

    filtros_avancados: {
      apenas_irregularidades: boolean
      tipo_irregularidade: ClassificacaoRegistro[]
      turno: TipoTurno[]
      empresa: UUID[]
    }
  }

  resumo_periodo: {
    estatisticas: {
      dias_trabalhados: number
      horas_normais: number
      horas_extras: number
      horas_noturnas: number
      atrasos: number
      saidas_antecipadas: number
      faltas: number
      irregularidades: number
    }

    indicadores: {
      pontualidade_percentual: number
      assiduidade_percentual: number
      horas_previstas: number
      horas_realizadas: number
      saldo_horas: number // + ou -
    }
  }

  calendario_ponto: {
    visualizacao: 'calendario' | 'lista'

    dia_calendario: {
      data: Date
      status: 'trabalhado' | 'falta' | 'feriado' | 'folga'
      registros: RegistroPonto[]
      irregularidades: ClassificacaoRegistro[]
      horas_trabalhadas: number
      observacoes: string
    }

    legenda_cores: {
      verde: 'Dia normal'
      amarelo: 'Irregularidades menores'
      vermelho: 'Irregularidades graves'
      azul: 'Horas extras'
      cinza: 'Folga/Feriado'
    }
  }

  detalhes_dia: {
    data_selecionada: Date

    registros_dia: {
      sequencia: number
      tipo_batida: TipoBatida
      horario_registrado: Time
      horario_previsto: Time
      diferenca: number // minutos
      classificacao: ClassificacaoRegistro
      metodo: 'biometrico' | 'manual'
      dispositivo: string
      localizacao: string
      justificativa: string
      aprovado_por: string
    }[]

    calculos_dia: {
      jornada_prevista: number // minutos
      jornada_realizada: number
      intervalo_previsto: number
      intervalo_realizado: number
      horas_extras: number
      adicional_noturno: number
      desconto_atraso: number
    }

    acoes_dia: {
      adicionar_justificativa: boolean
      solicitar_correcao: boolean
      aprovar_irregularidade: boolean
      gerar_ocorrencia: boolean
    }
  }

  exportacao: {
    formatos: ['PDF', 'Excel', 'CSV']
    opcoes: {
      incluir_fotos: boolean
      incluir_justificativas: boolean
      incluir_calculos: boolean
      assinatura_digital: boolean
    }

    templates: {
      simples: 'Apenas registros'
      completo: 'Com cálculos e irregularidades'
      oficial: 'Para apresentação legal'
      personalizado: 'Campos selecionáveis'
    }
  }
}
```

### **3. CLASSIFICAÇÃO DE IRREGULARIDADES** (`/ponto/classificacoes`)

```typescript
interface ClassificacaoIrregularidadesScreen {
  dashboard_irregularidades: {
    resumo_geral: {
      total_registros_mes: number
      irregularidades_detectadas: number
      percentual_irregularidades: number
      pendentes_analise: number
      aprovadas_rh: number
      rejeitadas: number
    }

    tipos_irregularidades: {
      atraso: {
        quantidade: number
        tempo_medio: number // minutos
        funcionarios_afetados: number
      }
      saida_antecipada: {
        quantidade: number
        tempo_medio: number
        funcionarios_afetados: number
      }
      fora_sequencia: {
        quantidade: number
        funcionarios_afetados: number
      }
      registro_duplicado: {
        quantidade: number
        funcionarios_afetados: number
      }
      intervalo_irregular: {
        quantidade: number
        tempo_medio_excesso: number
      }
      localizacao_invalida: {
        quantidade: number
        funcionarios_afetados: number
      }
    }
  }

  lista_irregularidades: {
    filtros: {
      periodo: DateRange
      funcionario: string
      empresa: UUID
      tipo_irregularidade: ClassificacaoRegistro[]
      status_analise: 'pendente' | 'aprovado' | 'rejeitado'
      gravidade: 'baixa' | 'media' | 'alta'
    }

    colunas: [
      'data_registro',
      'funcionario',
      'empresa',
      'tipo_batida',
      'horario_registrado',
      'horario_previsto',
      'diferenca',
      'classificacao',
      'gravidade',
      'justificativa',
      'status_analise',
      'acoes',
    ]

    acoes_linha: {
      analisar: 'Modal de análise detalhada'
      aprovar: 'Aprovar irregularidade'
      rejeitar: 'Rejeitar com motivo'
      solicitar_justificativa: 'Email para funcionário'
      gerar_ocorrencia: 'Criar ocorrência disciplinar'
    }

    acoes_lote: {
      aprovar_selecionadas: 'Aprovação em massa'
      rejeitar_selecionadas: 'Rejeição em massa'
      exportar_relatorio: 'Excel com detalhes'
      notificar_supervisores: 'Email para supervisores'
    }
  }

  analise_detalhada: {
    dados_registro: {
      funcionario: FuncionarioCompleto
      registro: RegistroPontoCompleto
      contexto: {
        registros_anteriores: RegistroPonto[]
        jornada_configurada: JornadaTrabalho
        historico_irregularidades: number
      }
    }

    classificacao_automatica: {
      tipo: ClassificacaoRegistro
      gravidade: 'baixa' | 'media' | 'alta'
      motivo: string
      sugestao_acao: string
      impacto_financeiro: number
    }

    decisao_rh: {
      acao: 'aprovar' | 'rejeitar' | 'solicitar_justificativa'
      motivo_decisao: string
      desconto_aplicar: boolean
      valor_desconto: number
      gerar_advertencia: boolean
      observacoes_internas: string
    }
  }
}
```

### **4. CONFIGURAÇÃO DE TURNOS** (`/ponto/turnos`)

```typescript
interface ConfiguracaoTurnosScreen {
  turnos_disponiveis: {
    primeiro_turno: {
      codigo: 'T1'
      nome: 'Primeiro Turno'
      horarios: {
        entrada: '06:00'
        saida_intervalo: '10:00'
        retorno_intervalo: '10:15'
        saida: '14:00'
      }
      tolerancias: {
        entrada: 10 // minutos
        saida: 10
        intervalo: 5
      }
      adicional_noturno: false
    }

    segundo_turno: {
      codigo: 'T2'
      nome: 'Segundo Turno'
      horarios: {
        entrada: '14:00'
        saida_intervalo: '18:00'
        retorno_intervalo: '18:15'
        saida: '22:00'
      }
      tolerancias: {
        entrada: 10
        saida: 10
        intervalo: 5
      }
      adicional_noturno: false
    }

    terceiro_turno: {
      codigo: 'T3'
      nome: 'Terceiro Turno (Noturno)'
      horarios: {
        entrada: '22:00'
        saida_intervalo: '02:00'
        retorno_intervalo: '02:15'
        saida: '06:00'
      }
      tolerancias: {
        entrada: 15 // maior tolerância noturno
        saida: 15
        intervalo: 10
      }
      adicional_noturno: true
      percentual_adicional: 20
      cruzamento_meia_noite: true
    }

    administrativo: {
      codigo: 'ADM'
      nome: 'Administrativo'
      horarios: {
        entrada: '08:00'
        saida_intervalo: '12:00'
        retorno_intervalo: '13:00'
        saida: '17:00'
      }
      tolerancias: {
        entrada: 5
        saida: 5
        intervalo: 15
      }
      adicional_noturno: false
    }
  }

  configuracao_turno: {
    dados_basicos: {
      codigo: {
        tipo: 'text'
        mascara: 'XXX'
        obrigatorio: true
        unico: true
      }
      nome: {
        tipo: 'text'
        obrigatorio: true
        placeholder: 'Nome do turno'
      }
      descricao: {
        tipo: 'textarea'
        placeholder: 'Descrição detalhada'
      }
      ativo: {
        tipo: 'checkbox'
        padrao: true
      }
    }

    horarios_trabalho: {
      entrada: {
        tipo: 'time'
        obrigatorio: true
      }
      saida_intervalo: {
        tipo: 'time'
        obrigatorio: true
        validacao: 'Após entrada'
      }
      retorno_intervalo: {
        tipo: 'time'
        obrigatorio: true
        validacao: 'Após saída intervalo'
      }
      saida: {
        tipo: 'time'
        obrigatorio: true
        validacao: 'Após retorno intervalo'
      }

      calculo_automatico: {
        jornada_diaria: 'Calculado automaticamente'
        intervalo_duracao: 'Calculado automaticamente'
        cruzamento_meia_noite: 'Detectado automaticamente'
      }
    }

    tolerancias: {
      tolerancia_entrada: {
        tipo: 'number'
        unidade: 'minutos'
        padrao: 10
        min: 0
        max: 60
      }
      tolerancia_saida: {
        tipo: 'number'
        unidade: 'minutos'
        padrao: 10
        min: 0
        max: 60
      }
      tolerancia_intervalo: {
        tipo: 'number'
        unidade: 'minutos'
        padrao: 5
        min: 0
        max: 30
      }
    }

    configuracoes_especiais: {
      adicional_noturno: {
        tipo: 'checkbox'
        label: 'Aplicar adicional noturno'
      }
      percentual_adicional: {
        tipo: 'number'
        unidade: '%'
        condicional: 'adicional_noturno'
        padrao: 20
        min: 0
        max: 100
      }
      horario_noturno_inicio: {
        tipo: 'time'
        condicional: 'adicional_noturno'
        padrao: '22:00'
      }
      horario_noturno_fim: {
        tipo: 'time'
        condicional: 'adicional_noturno'
        padrao: '05:00'
      }
    }
  }

  atribuicao_funcionarios: {
    funcionarios_turno: {
      lista_atual: Funcionario[]
      estatisticas: {
        total_funcionarios: number
        por_empresa: { [empresa: string]: number }
        por_cargo: { [cargo: string]: number }
      }
    }

    alterar_turno: {
      funcionarios_selecionados: UUID[]
      novo_turno: UUID
      data_vigencia: Date
      motivo_alteracao: string
      notificar_funcionarios: boolean
    }
  }
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS:**

```typescript
interface RegistroPontoAPI {
  // ✅ REGISTRO DE PONTO
  'POST /api/ponto/registrar': {
    body: {
      funcionario_id?: UUID // Para registro manual
      tipo_batida?: TipoBatida // Opcional, sistema sugere
      localizacao?: {
        latitude: number
        longitude: number
      }
      foto?: string // base64 para registro manual
      justificativa?: string
      template_biometrico?: string // Para registro biométrico
    }
    response: {
      registro: RegistroPonto
      classificacao: ClassificacaoRegistro
      proxima_batida: {
        tipo: TipoBatida
        horario_previsto: Time
      }
    }
  }

  'GET /api/ponto/funcionario/[id]/ultimo': {
    response: {
      ultimo_registro: RegistroPonto
      proximo_esperado: TipoBatida
      horario_previsto: Time
    }
  }

  // ✅ ESPELHO DE PONTO
  'GET /api/ponto/espelho': {
    query: {
      funcionario_id: UUID
      data_inicio: Date
      data_fim: Date
      incluir_calculos?: boolean
    }
    response: {
      registros: RegistroPonto[]
      resumo: ResumoEspelho
      calculos_periodo: CalculosPeriodo
    }
  }

  'GET /api/ponto/espelho/[funcionario_id]/[ano]/[mes]': {
    response: {
      calendario: CalendarioPonto
      estatisticas: EstatisticasMes
      irregularidades: IrregularidadeMes[]
    }
  }

  // ✅ CLASSIFICAÇÕES
  'GET /api/ponto/irregularidades': {
    query: {
      periodo?: DateRange
      funcionario_id?: UUID
      empresa_id?: UUID
      tipo?: ClassificacaoRegistro[]
      status?: 'pendente' | 'aprovado' | 'rejeitado'
    }
    response: {
      irregularidades: IrregularidadeCompleta[]
      resumo: ResumoIrregularidades
    }
  }

  'PUT /api/ponto/irregularidades/[id]/analisar': {
    body: {
      acao: 'aprovar' | 'rejeitar' | 'solicitar_justificativa'
      motivo: string
      desconto_aplicar?: boolean
      valor_desconto?: number
      observacoes?: string
    }
    response: { message: 'Irregularidade analisada' }
  }

  // ✅ TURNOS
  'GET /api/ponto/turnos': {
    response: TurnoTrabalho[]
  }

  'POST /api/ponto/turnos': {
    body: TurnoTrabalhoData
    response: TurnoTrabalho
  }

  'PUT /api/ponto/funcionarios/[id]/turno': {
    body: {
      turno_id: UUID
      data_vigencia: Date
      motivo: string
    }
    response: { message: 'Turno alterado' }
  }
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ TIPOS DE BATIDA
CREATE TYPE tipo_batida AS ENUM (
  'B1', -- inicio_jornada
  'B2', -- saida_intervalo
  'B3', -- retorno_intervalo
  'B4', -- fim_jornada
  'B5', -- inicio_hora_extra
  'B6'  -- fim_hora_extra
);

-- ✅ CLASSIFICAÇÕES
CREATE TYPE classificacao_registro AS ENUM (
  'normal',
  'atraso',
  'saida_antecipada',
  'fora_sequencia',
  'registro_duplicado',
  'intervalo_irregular',
  'localizacao_invalida',
  'hora_extra_nao_autorizada',
  'falta_registro'
);

-- ✅ REGISTROS DE PONTO
CREATE TABLE registros_ponto (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  empresa_id UUID NOT NULL REFERENCES empresas(id),

  -- Dados do Registro
  data_registro DATE NOT NULL,
  horario_registro TIMESTAMP NOT NULL,
  tipo_batida tipo_batida NOT NULL,

  -- Método de Registro
  metodo_registro VARCHAR(20) NOT NULL, -- 'biometrico', 'manual', 'mobile'
  dispositivo_id UUID REFERENCES dispositivos_biometricos(id),
  ip_origem INET,
  user_agent TEXT,

  -- Localização
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  precisao_gps INTEGER, -- metros
  endereco_capturado TEXT,

  -- Validações
  template_biometrico TEXT,
  qualidade_biometria INTEGER,
  foto_registro TEXT, -- URL da foto (registro manual)

  -- Classificação
  classificacao classificacao_registro DEFAULT 'normal',
  gravidade VARCHAR(10) DEFAULT 'baixa', -- 'baixa', 'media', 'alta'

  -- Justificativas
  justificativa TEXT,
  justificativa_aprovada BOOLEAN,
  aprovado_por UUID REFERENCES users(id),
  data_aprovacao TIMESTAMP,
  motivo_rejeicao TEXT,

  -- Cálculos
  horario_previsto TIME,
  diferenca_minutos INTEGER, -- diferença do previsto
  dentro_tolerancia BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- ✅ TURNOS DE TRABALHO
CREATE TABLE turnos_trabalho (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Identificação
  codigo VARCHAR(10) UNIQUE NOT NULL,
  nome VARCHAR(100) NOT NULL,
  descricao TEXT,

  -- Horários
  horario_entrada TIME NOT NULL,
  horario_saida_intervalo TIME NOT NULL,
  horario_retorno_intervalo TIME NOT NULL,
  horario_saida TIME NOT NULL,

  -- Tolerâncias (em minutos)
  tolerancia_entrada INTEGER DEFAULT 10,
  tolerancia_saida INTEGER DEFAULT 10,
  tolerancia_intervalo INTEGER DEFAULT 5,

  -- Configurações Especiais
  adicional_noturno BOOLEAN DEFAULT FALSE,
  percentual_adicional DECIMAL(5,2) DEFAULT 0,
  horario_noturno_inicio TIME DEFAULT '22:00',
  horario_noturno_fim TIME DEFAULT '05:00',
  cruzamento_meia_noite BOOLEAN DEFAULT FALSE,

  -- Cálculos Automáticos
  jornada_diaria_minutos INTEGER GENERATED ALWAYS AS (
    CASE
      WHEN cruzamento_meia_noite THEN
        (EXTRACT(EPOCH FROM horario_saida + INTERVAL '1 day' - horario_entrada) / 60)::INTEGER
        - (EXTRACT(EPOCH FROM horario_retorno_intervalo - horario_saida_intervalo) / 60)::INTEGER
      ELSE
        (EXTRACT(EPOCH FROM horario_saida - horario_entrada) / 60)::INTEGER
        - (EXTRACT(EPOCH FROM horario_retorno_intervalo - horario_saida_intervalo) / 60)::INTEGER
    END
  ) STORED,

  intervalo_duracao_minutos INTEGER GENERATED ALWAYS AS (
    (EXTRACT(EPOCH FROM horario_retorno_intervalo - horario_saida_intervalo) / 60)::INTEGER
  ) STORED,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ✅ ATRIBUIÇÃO DE TURNOS
CREATE TABLE funcionario_turnos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  funcionario_id UUID NOT NULL REFERENCES funcionarios(id),
  turno_id UUID NOT NULL REFERENCES turnos_trabalho(id),

  -- Vigência
  data_inicio DATE NOT NULL,
  data_fim DATE,

  -- Motivo da Alteração
  motivo_alteracao TEXT,

  -- Status
  ativo BOOLEAN DEFAULT TRUE,

  -- Auditoria
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),

  CONSTRAINT chk_vigencia_turno CHECK (
    data_fim IS NULL OR data_fim >= data_inicio
  )
);

-- ✅ ÍNDICES
CREATE INDEX idx_registros_funcionario_data ON registros_ponto(funcionario_id, data_registro);
CREATE INDEX idx_registros_empresa_data ON registros_ponto(empresa_id, data_registro);
CREATE INDEX idx_registros_classificacao ON registros_ponto(classificacao);
CREATE INDEX idx_registros_horario ON registros_ponto(horario_registro);
CREATE INDEX idx_turnos_codigo ON turnos_trabalho(codigo);
CREATE INDEX idx_funcionario_turnos_ativo ON funcionario_turnos(funcionario_id, ativo);
```

---

## 🔒 REGRAS DE NEGÓCIO

### **⏰ PRINCÍPIO FUNDAMENTAL:**

**"NUNCA IMPEDIR FUNCIONÁRIO ATIVO DE BATER PONTO"**

1. **Sistema sempre permite registro**
2. **Classificação automática posterior**
3. **RH analisa e decide ação**
4. **Funcionário nunca é bloqueado**

### **🔄 SEQUÊNCIA DE BATIDAS:**

1. **B1** → **B2** → **B3** → **B4** (jornada normal)
2. **B4** → **B5** → **B6** (hora extra)
3. Fora de sequência = classificação especial
4. Sistema sugere próxima batida

### **⚖️ TOLERÂNCIAS:**

1. **Configuráveis por turno**
2. **Dentro da tolerância = normal**
3. **Fora da tolerância = irregularidade**
4. **Gravidade baseada na diferença**

### **🌙 TURNO NOTURNO:**

1. **Cruzamento de meia-noite automático**
2. **Adicional noturno calculado**
3. **Tolerâncias maiores**
4. **Validação especial de sequência**

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE REGISTRO:**

```typescript
describe('Registro de Ponto', () => {
  test('Registro normal B1', async () => {
    const funcionario = await criarFuncionario()
    const registro = await registrarPonto({
      funcionario_id: funcionario.id,
      tipo_batida: 'B1',
    })

    expect(registro.classificacao).toBe('normal')
    expect(registro.tipo_batida).toBe('B1')
  })

  test('Nunca impedir funcionário ativo', async () => {
    const funcionario = await criarFuncionario({ ativo: true })

    // Mesmo com 100 tentativas, deve permitir
    for (let i = 0; i < 100; i++) {
      const registro = await registrarPonto({
        funcionario_id: funcionario.id,
        tipo_batida: 'B1',
      })
      expect(registro).toBeDefined()
    }
  })
})
```

### **✅ TESTES DE CLASSIFICAÇÃO:**

```typescript
describe('Classificação de Irregularidades', () => {
  test('Atraso dentro da tolerância', async () => {
    const turno = await criarTurno({
      horario_entrada: '08:00',
      tolerancia_entrada: 10,
    })

    const registro = await registrarPonto({
      horario_registro: '08:05', // 5 min atraso
      turno_id: turno.id,
    })

    expect(registro.classificacao).toBe('normal')
    expect(registro.dentro_tolerancia).toBe(true)
  })

  test('Atraso fora da tolerância', async () => {
    const registro = await registrarPonto({
      horario_registro: '08:15', // 15 min atraso
      horario_previsto: '08:00',
      tolerancia: 10,
    })

    expect(registro.classificacao).toBe('atraso')
    expect(registro.dentro_tolerancia).toBe(false)
  })
})
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Core do Sistema**

- Modelo de dados
- APIs de registro
- Validações básicas

**Semana 3-4: Classificação**

- Sistema de classificação
- Algoritmos de detecção
- Dashboard de irregularidades

**Semana 5-6: Espelho de Ponto**

- Visualização individual
- Cálculos de horas
- Exportação

**Semana 7-8: Turnos e Configurações**

- Gestão de turnos
- Configurações avançadas
- Testes completos

### **🔧 DEPENDÊNCIAS:**

- Módulo de Funcionários
- Módulo de Empresas
- Módulo Biométrico
- GPS/Geolocalização
- Sistema de notificações
